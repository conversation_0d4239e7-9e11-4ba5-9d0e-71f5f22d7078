"""
CodeAgent - 基于 smolagents 的智能代码编写助手
集成 DeepSeek 模型，实现从自然语言到代码生成的全流程自动化

主要功能：
- 自然语言理解与代码生成
- 文件操作与项目管理
- 命令执行与环境管理
- 任务分解与多工具协同
- 记忆管理与检查点机制
"""

import os
import json
import time
import hashlib
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from smolagents import CodeAgent, Tool, LiteLLMModel
from smolagents import PythonInterpreterTool

class MemoryManager:
    """记忆管理系统 - 实现持久化记忆与检索"""
    
    def __init__(self, memory_dir: str = ".codeagent_memory"):
        self.memory_dir = Path(memory_dir)
        self.memory_dir.mkdir(exist_ok=True)
        
        # 记忆文件路径
        self.conversation_file = self.memory_dir / "conversations.json"
        self.knowledge_file = self.memory_dir / "knowledge_base.json"
        self.checkpoints_dir = self.memory_dir / "checkpoints"
        self.checkpoints_dir.mkdir(exist_ok=True)
        
        # 初始化记忆存储
        self._init_memory_files()
    
    def _init_memory_files(self):
        """初始化记忆文件"""
        if not self.conversation_file.exists():
            self._save_json(self.conversation_file, {"conversations": []})
        
        if not self.knowledge_file.exists():
            self._save_json(self.knowledge_file, {
                "code_patterns": {},
                "project_structures": {},
                "user_preferences": {},
                "learned_solutions": {}
            })
    
    def _save_json(self, file_path: Path, data: Dict):
        """保存 JSON 数据"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def _load_json(self, file_path: Path) -> Dict:
        """加载 JSON 数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}
    
    def store_conversation(self, user_input: str, agent_response: str, context: Dict = None):
        """存储对话记录"""
        conversations = self._load_json(self.conversation_file)
        
        conversation_entry = {
            "timestamp": datetime.now().isoformat(),
            "user_input": user_input,
            "agent_response": agent_response,
            "context": context or {}
        }
        
        conversations["conversations"].append(conversation_entry)
        
        # 保持最近 100 条对话
        if len(conversations["conversations"]) > 100:
            conversations["conversations"] = conversations["conversations"][-100:]
        
        self._save_json(self.conversation_file, conversations)
    
    def store_knowledge(self, category: str, key: str, value: Any):
        """存储知识"""
        knowledge = self._load_json(self.knowledge_file)
        
        if category not in knowledge:
            knowledge[category] = {}
        
        knowledge[category][key] = {
            "value": value,
            "timestamp": datetime.now().isoformat()
        }
        
        self._save_json(self.knowledge_file, knowledge)
    
    def retrieve_knowledge(self, category: str, key: str = None) -> Any:
        """检索知识"""
        knowledge = self._load_json(self.knowledge_file)
        
        if category not in knowledge:
            return None
        
        if key is None:
            return knowledge[category]
        
        return knowledge[category].get(key, {}).get("value")
    
    def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:
        """搜索相关对话"""
        conversations = self._load_json(self.conversation_file)
        
        relevant_conversations = []
        query_lower = query.lower()
        
        for conv in conversations.get("conversations", []):
            if (query_lower in conv["user_input"].lower() or 
                query_lower in conv["agent_response"].lower()):
                relevant_conversations.append(conv)
        
        return relevant_conversations[-limit:]

class CheckpointManager:
    """检查点管理系统 - 实现工作区状态管理"""
    
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self.checkpoints_dir = memory_manager.checkpoints_dir
    
    def create_checkpoint(self, name: str, description: str = "") -> str:
        """创建检查点"""
        checkpoint_id = f"{name}_{int(time.time())}"
        checkpoint_dir = self.checkpoints_dir / checkpoint_id
        checkpoint_dir.mkdir(exist_ok=True)
        
        # 保存当前工作目录状态
        current_files = self._get_current_files()
        
        checkpoint_data = {
            "id": checkpoint_id,
            "name": name,
            "description": description,
            "timestamp": datetime.now().isoformat(),
            "files": current_files,
            "working_directory": str(Path.cwd())
        }
        
        checkpoint_file = checkpoint_dir / "checkpoint.json"
        self.memory_manager._save_json(checkpoint_file, checkpoint_data)
        
        # 复制重要文件
        self._backup_files(checkpoint_dir, current_files)
        
        return checkpoint_id
    
    def _get_current_files(self) -> Dict[str, str]:
        """获取当前目录的文件信息"""
        files_info = {}
        current_dir = Path.cwd()
        
        for file_path in current_dir.rglob("*"):
            if file_path.is_file() and not self._should_ignore_file(file_path):
                relative_path = file_path.relative_to(current_dir)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    files_info[str(relative_path)] = {
                        "content": content,
                        "hash": hashlib.md5(content.encode()).hexdigest(),
                        "size": file_path.stat().st_size
                    }
                except (UnicodeDecodeError, PermissionError):
                    # 跳过二进制文件或无权限文件
                    files_info[str(relative_path)] = {
                        "content": "[BINARY_FILE]",
                        "hash": "binary",
                        "size": file_path.stat().st_size
                    }
        
        return files_info
    
    def _should_ignore_file(self, file_path: Path) -> bool:
        """判断是否应该忽略文件"""
        ignore_patterns = [
            ".git", "__pycache__", ".pytest_cache", "node_modules",
            ".codeagent_memory", "*.pyc", "*.pyo", "*.pyd", ".DS_Store"
        ]
        
        path_str = str(file_path)
        for pattern in ignore_patterns:
            if pattern in path_str:
                return True
        
        return False
    
    def _backup_files(self, checkpoint_dir: Path, files_info: Dict):
        """备份文件到检查点目录"""
        files_dir = checkpoint_dir / "files"
        files_dir.mkdir(exist_ok=True)
        
        for file_path, info in files_info.items():
            if info["content"] != "[BINARY_FILE]":
                backup_file = files_dir / file_path
                backup_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(info["content"])
    
    def list_checkpoints(self) -> List[Dict]:
        """列出所有检查点"""
        checkpoints = []
        
        for checkpoint_dir in self.checkpoints_dir.iterdir():
            if checkpoint_dir.is_dir():
                checkpoint_file = checkpoint_dir / "checkpoint.json"
                if checkpoint_file.exists():
                    checkpoint_data = self.memory_manager._load_json(checkpoint_file)
                    checkpoints.append(checkpoint_data)
        
        return sorted(checkpoints, key=lambda x: x["timestamp"], reverse=True)
    
    def restore_checkpoint(self, checkpoint_id: str) -> bool:
        """恢复检查点"""
        checkpoint_dir = self.checkpoints_dir / checkpoint_id
        checkpoint_file = checkpoint_dir / "checkpoint.json"
        
        if not checkpoint_file.exists():
            return False
        
        checkpoint_data = self.memory_manager._load_json(checkpoint_file)
        files_dir = checkpoint_dir / "files"
        
        if not files_dir.exists():
            return False
        
        # 恢复文件
        for file_path in files_dir.rglob("*"):
            if file_path.is_file():
                relative_path = file_path.relative_to(files_dir)
                target_path = Path.cwd() / relative_path
                target_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(file_path, 'r', encoding='utf-8') as src:
                    content = src.read()
                
                with open(target_path, 'w', encoding='utf-8') as dst:
                    dst.write(content)
        
        return True

class CodeGenerationTool(Tool):
    """代码生成工具 - 基于自然语言生成代码"""

    name = "code_generation"
    description = "根据自然语言描述生成代码，支持多种编程语言"
    inputs = {
        "description": {
            "type": "string",
            "description": "代码功能的自然语言描述"
        },
        "language": {
            "type": "string",
            "description": "编程语言（python, javascript, java, cpp 等）",
            "nullable": True
        },
        "context": {
            "type": "string",
            "description": "相关上下文信息",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, memory_manager: MemoryManager):
        super().__init__()
        self.memory_manager = memory_manager

    def forward(self, description: str, language: str = "python", context: str = "") -> str:
        """生成代码"""
        # 搜索相关历史经验
        similar_conversations = self.memory_manager.search_conversations(description, limit=3)

        # 获取用户偏好
        user_preferences = self.memory_manager.retrieve_knowledge("user_preferences") or {}

        # 构建增强的提示
        enhanced_prompt = f"""
基于以下描述生成 {language} 代码：

需求描述：{description}

上下文信息：{context}

用户偏好：{json.dumps(user_preferences, ensure_ascii=False, indent=2)}

相关历史经验：
{json.dumps(similar_conversations, ensure_ascii=False, indent=2)}

请生成清晰、可读、符合最佳实践的代码，并包含必要的注释。
"""

        # 这里应该调用 LLM 生成代码，暂时返回模板
        generated_code = f"""
# 根据描述生成的 {language} 代码
# 需求：{description}

# TODO: 实际的代码生成逻辑
def generated_function():
    '''
    {description}
    '''
    pass
"""

        # 存储生成的代码模式
        self.memory_manager.store_knowledge(
            "code_patterns",
            f"{language}_{hashlib.md5(description.encode()).hexdigest()[:8]}",
            {
                "description": description,
                "language": language,
                "code": generated_code,
                "context": context
            }
        )

        return generated_code

class FileOperationTool(Tool):
    """文件操作工具 - 处理文件读写、创建、删除等操作"""

    name = "file_operation"
    description = "执行文件操作：创建、读取、写入、删除、移动文件"
    inputs = {
        "operation": {
            "type": "string",
            "description": "操作类型：create, read, write, delete, move, list"
        },
        "file_path": {
            "type": "string",
            "description": "文件路径"
        },
        "content": {
            "type": "string",
            "description": "文件内容（用于 write 操作）",
            "nullable": True
        },
        "target_path": {
            "type": "string",
            "description": "目标路径（用于 move 操作）",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, memory_manager: MemoryManager):
        super().__init__()
        self.memory_manager = memory_manager

    def forward(self, operation: str, file_path: str, content: str = "", target_path: str = "") -> str:
        """执行文件操作"""
        try:
            path = Path(file_path)

            if operation == "create":
                path.parent.mkdir(parents=True, exist_ok=True)
                path.touch()
                return f"文件 {file_path} 创建成功"

            elif operation == "read":
                if not path.exists():
                    return f"错误：文件 {file_path} 不存在"

                with open(path, 'r', encoding='utf-8') as f:
                    file_content = f.read()

                # 存储文件访问记录
                self.memory_manager.store_knowledge(
                    "file_access",
                    str(path),
                    {"last_read": datetime.now().isoformat(), "size": len(file_content)}
                )

                return file_content

            elif operation == "write":
                path.parent.mkdir(parents=True, exist_ok=True)

                with open(path, 'w', encoding='utf-8') as f:
                    f.write(content)

                # 存储文件修改记录
                self.memory_manager.store_knowledge(
                    "file_modifications",
                    str(path),
                    {
                        "last_modified": datetime.now().isoformat(),
                        "content_hash": hashlib.md5(content.encode()).hexdigest()
                    }
                )

                return f"内容已写入文件 {file_path}"

            elif operation == "delete":
                if path.exists():
                    if path.is_file():
                        path.unlink()
                        return f"文件 {file_path} 删除成功"
                    elif path.is_dir():
                        import shutil
                        shutil.rmtree(path)
                        return f"目录 {file_path} 删除成功"
                else:
                    return f"错误：路径 {file_path} 不存在"

            elif operation == "move":
                if not path.exists():
                    return f"错误：源文件 {file_path} 不存在"

                target = Path(target_path)
                target.parent.mkdir(parents=True, exist_ok=True)
                path.rename(target)
                return f"文件从 {file_path} 移动到 {target_path}"

            elif operation == "list":
                if path.is_dir():
                    files = [str(p) for p in path.iterdir()]
                    return f"目录 {file_path} 内容：\n" + "\n".join(files)
                else:
                    return f"错误：{file_path} 不是目录"

            else:
                return f"不支持的操作：{operation}"

        except Exception as e:
            error_msg = f"文件操作失败：{str(e)}"

            # 记录错误
            self.memory_manager.store_knowledge(
                "operation_errors",
                f"file_op_{int(time.time())}",
                {
                    "operation": operation,
                    "file_path": file_path,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            )

            return error_msg

class CommandExecutionTool(Tool):
    """命令执行工具 - 安全执行系统命令"""

    name = "command_execution"
    description = "执行系统命令，支持安全检查和结果记录"
    inputs = {
        "command": {
            "type": "string",
            "description": "要执行的命令"
        },
        "working_dir": {
            "type": "string",
            "description": "工作目录",
            "nullable": True
        },
        "timeout": {
            "type": "number",
            "description": "超时时间（秒）",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, memory_manager: MemoryManager):
        super().__init__()
        self.memory_manager = memory_manager

        # 危险命令黑名单
        self.dangerous_commands = [
            "rm -rf", "del /f", "format", "fdisk", "mkfs",
            "shutdown", "reboot", "halt", "poweroff",
            "chmod 777", "chown", "sudo rm", "dd if="
        ]

    def _is_safe_command(self, command: str) -> bool:
        """检查命令是否安全"""
        command_lower = command.lower()

        for dangerous in self.dangerous_commands:
            if dangerous in command_lower:
                return False

        return True

    def forward(self, command: str, working_dir: str = "", timeout: float = 30.0) -> str:
        """执行命令"""
        # 安全检查
        if not self._is_safe_command(command):
            error_msg = f"危险命令被阻止：{command}"
            self.memory_manager.store_knowledge(
                "security_blocks",
                f"cmd_{int(time.time())}",
                {
                    "command": command,
                    "reason": "dangerous_command",
                    "timestamp": datetime.now().isoformat()
                }
            )
            return error_msg

        try:
            # 设置工作目录
            cwd = Path(working_dir) if working_dir else Path.cwd()

            # 执行命令
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )

            # 构建结果
            output = f"命令执行完成\n"
            output += f"返回码: {result.returncode}\n"

            if result.stdout:
                output += f"标准输出:\n{result.stdout}\n"

            if result.stderr:
                output += f"标准错误:\n{result.stderr}\n"

            # 记录命令执行
            self.memory_manager.store_knowledge(
                "command_history",
                f"cmd_{int(time.time())}",
                {
                    "command": command,
                    "working_dir": str(cwd),
                    "return_code": result.returncode,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "timestamp": datetime.now().isoformat()
                }
            )

            return output

        except subprocess.TimeoutExpired:
            return f"命令执行超时（{timeout}秒）：{command}"

        except Exception as e:
            error_msg = f"命令执行失败：{str(e)}"

            # 记录错误
            self.memory_manager.store_knowledge(
                "operation_errors",
                f"cmd_error_{int(time.time())}",
                {
                    "command": command,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            )

            return error_msg
