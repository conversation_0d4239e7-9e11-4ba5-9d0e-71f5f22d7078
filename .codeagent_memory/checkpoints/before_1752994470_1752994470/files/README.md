# smolagents 学习项目

这是一个基于 Hugging Face smolagents 库的 Agent 开发学习项目。

## 项目结构

```
.
├── README.md                 # 项目说明
├── requirements.txt          # 依赖列表
├── learn.py                 # 主学习文件
├── examples/                # 示例代码
│   ├── basic_agent.py       # 基础 Agent 示例
│   ├── custom_tools.py      # 自定义工具示例
│   ├── multi_agent.py       # 多 Agent 协作示例
│   └── secure_execution.py  # 安全执行环境示例
├── tools/                   # 自定义工具
│   └── __init__.py
└── config/                  # 配置文件
    └── agent_config.py
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

1. 设置环境变量（如果使用外部模型）：
```bash
export HUGGINGFACE_TOKEN="your_token_here"
# 或者其他模型提供商的 API key
```

2. 运行基础示例：
```bash
python examples/basic_agent.py
```

## smolagents 核心概念

### Agent 类型
- **CodeAgent**: 将动作写成 Python 代码片段
- **ToolCallingAgent**: 使用传统的工具调用方式

### 核心特性
- 🧑‍💻 代码优先的 Agent 设计
- 🛠️ 丰富的工具生态系统
- 🌐 支持多种模型提供商
- 🔒 安全的代码执行环境
- 🤗 Hub 集成

## 学习路径

1. [基础 Agent 使用](examples/basic_agent.py)
2. [创建自定义工具](examples/custom_tools.py)
3. [多 Agent 协作](examples/multi_agent.py)
4. [安全执行环境](examples/secure_execution.py)
