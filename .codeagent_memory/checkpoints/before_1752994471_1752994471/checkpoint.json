{"id": "before_1752994471_1752994471", "name": "before_1752994471", "description": "执行前检查点: 生成一个简单的 Web API 项目结构...", "timestamp": "2025-07-20T14:54:31.056292", "files": {"PROJECT_SUMMARY.md": {"content": "# smolagents 学习项目总结\n\n## 🎯 项目概述\n\n这是一个基于 Hugging Face smolagents 库的完整 Agent 开发学习项目。项目展示了如何使用 smolagents 构建智能代理系统，包括基础使用、自定义工具开发、多 Agent 协作和安全执行环境配置。\n\n## 📁 项目结构\n\n```\nagent/\n├── README.md                 # 项目说明文档\n├── PROJECT_SUMMARY.md        # 项目总结（本文件）\n├── learn.py                 # 主学习文件和项目概览\n├── simple_demo.py           # 简单演示（不依赖外部模型）\n├── test_smolagents.py       # 基础功能测试\n├── requirements.txt         # 项目依赖\n├── config/\n│   └── agent_config.py      # Agent 配置文件\n├── examples/                # 示例代码\n│   ├── basic_agent.py       # 基础 Agent 使用示例\n│   ├── custom_tools.py      # 自定义工具使用示例\n│   ├── multi_agent.py       # 多 Agent 协作示例\n│   └── secure_execution.py  # 安全执行环境示例\n└── tools/                   # 自定义工具实现\n    ├── __init__.py\n    └── custom_tools.py      # 自定义工具类定义\n```\n\n## 🛠️ 实现的功能\n\n### 1. 基础 Agent 功能\n- ✅ CodeAgent 创建和配置\n- ✅ 基础工具使用（PythonInterpreterTool, WebSearchTool）\n- ✅ 简单任务执行和多步推理\n- ✅ 模型配置和提供商选择\n\n### 2. 自定义工具开发\n- ✅ WeatherTool: 模拟天气查询工具\n- ✅ DataAnalysisTool: 数据统计分析工具\n- ✅ TimerTool: 计时器和时间管理工具\n- ✅ FileManagerTool: 文件操作管理工具\n- ✅ 工具参数验证和错误处理\n\n### 3. 多 Agent 协作\n- ✅ Agent 专业化设计（数据分析师、天气专家、项目管理）\n- ✅ 任务分解和协调机制\n- ✅ Agent 间通信模式展示\n- ✅ 实际应用场景分析\n\n### 4. 安全执行环境\n- ✅ 本地执行环境配置\n- ✅ Docker 容器执行环境\n- ✅ E2B 云端沙箱环境\n- ✅ 安全最佳实践指南\n- ✅ 执行环境对比分析\n\n## 🔧 技术特点\n\n### smolagents 核心优势\n1. **代码优先设计**: Agent 将动作写成 Python 代码片段，比传统 JSON 调用减少 30% 步骤\n2. **模型无关**: 支持多种 LLM 提供商（HuggingFace, OpenAI, Anthropic 等）\n3. **工具生态**: 丰富的内置工具和简单的自定义工具开发\n4. **安全执行**: 多种沙箱环境选择（本地、Docker、E2B）\n5. **Hub 集成**: 可以分享和加载 Agent 到 HuggingFace Hub\n\n### 项目亮点\n- 📚 **完整的学习路径**: 从基础到高级的渐进式学习\n- 🛠️ **实用的工具示例**: 涵盖常见应用场景的自定义工具\n- 🤖 **多 Agent 架构**: 展示企业级多 Agent 系统设计\n- 🔒 **安全性考虑**: 全面的安全执行环境配置指南\n- 💡 **最佳实践**: 包含实际开发中的经验和建议\n\n## 🚀 快速开始\n\n### 1. 环境准备\n```bash\n# 安装依赖\npip install -r requirements.txt\n\n# 设置环境变量（可选）\nexport HUGGINGFACE_TOKEN=\"your_token_here\"\n```\n\n### 2. 运行示例\n```bash\n# 查看项目概览\npython learn.py\n\n# 运行简单演示（无需外部模型）\npython simple_demo.py\n\n# 测试基础功能\npython test_smolagents.py\n\n# 运行完整示例\npython examples/basic_agent.py\npython examples/custom_tools.py\npython examples/multi_agent.py\npython examples/secure_execution.py\n```\n\n## 📖 学习路径\n\n### 初学者\n1. 阅读 `README.md` 了解项目背景\n2. 运行 `simple_demo.py` 体验基础功能\n3. 学习 `examples/basic_agent.py` 了解 Agent 创建\n\n### 进阶用户\n1. 研究 `tools/custom_tools.py` 学习工具开发\n2. 运行 `examples/custom_tools.py` 体验自定义工具\n3. 探索 `examples/multi_agent.py` 了解协作模式\n\n### 高级用户\n1. 学习 `examples/secure_execution.py` 配置安全环境\n2. 修改 `config/agent_config.py` 自定义配置\n3. 开发自己的 Agent 应用\n\n## 🌟 扩展建议\n\n### 可以添加的功能\n- 🔄 Agent 工作流编排\n- 📊 性能监控和日志记录\n- 🌐 Web 界面和 API 服务\n- 🗄️ 数据库集成和持久化\n- 🔗 外部服务集成（API 调用）\n\n### 实际应用方向\n- 📝 智能文档处理系统\n- 🔍 自动化数据分析平台\n- 🎓 在线编程教育工具\n- 🏢 企业自动化助手\n- 🔬 科研数据处理工具\n\n## 💡 总结\n\n这个项目成功展示了 smolagents 作为一个现代 Agent 框架的强大能力。通过代码优先的设计理念、丰富的工具生态和灵活的执行环境，smolagents 为构建实用的 AI Agent 系统提供了优秀的基础。\n\n项目涵盖了从基础概念到高级应用的完整学习路径，为开发者提供了实用的参考和起点。无论是学习 Agent 开发还是构建实际应用，这个项目都能提供有价值的指导和示例。\n\n---\n\n🎉 **项目完成！** 希望这个学习项目能帮助您更好地理解和使用 smolagents 构建强大的 AI Agent 系统。\n", "hash": "8a38b1822b3c570f17911877e3a27285", "size": 5028}, "simple_demo.py": {"content": "\"\"\"\n简单的 smolagents 演示\n展示基本的工具使用，不依赖外部模型\n\"\"\"\n\nimport sys\nimport os\nsys.path.append(os.path.join(os.path.dirname(__file__), '.'))\n\nfrom tools.custom_tools import WeatherTool, DataAnalysisTool, TimerTool\n\ndef demo_custom_tools_directly():\n    \"\"\"直接演示自定义工具的功能\"\"\"\n    print(\"🛠️ === 直接测试自定义工具 ===\\n\")\n    \n    # 1. 测试天气工具\n    print(\"🌤️ 测试天气工具:\")\n    weather_tool = WeatherTool()\n    result1 = weather_tool.forward(\"北京\")\n    print(f\"  {result1}\")\n    result2 = weather_tool.forward(\"成都\")  # 不在预设列表中\n    print(f\"  {result2}\\n\")\n    \n    # 2. 测试数据分析工具\n    print(\"📊 测试数据分析工具:\")\n    data_tool = DataAnalysisTool()\n    test_data = \"[10, 20, 30, 40, 50, 60, 70, 80, 90, 100]\"\n    result3 = data_tool.forward(test_data, \"basic_stats\")\n    print(f\"  基础统计:\\n{result3}\")\n    \n    result4 = data_tool.forward(test_data, \"distribution\")\n    print(f\"  分布分析:\\n{result4}\")\n    \n    # 3. 测试计时器工具\n    print(\"⏱️ 测试计时器工具:\")\n    timer_tool = TimerTool()\n    result5 = timer_tool.forward(\"current\")\n    print(f\"  {result5}\")\n    result6 = timer_tool.forward(\"start\")\n    print(f\"  {result6}\")\n    result7 = timer_tool.forward(\"sleep\", 1)\n    print(f\"  {result7}\")\n    result8 = timer_tool.forward(\"stop\")\n    print(f\"  {result8}\\n\")\n\ndef show_smolagents_architecture():\n    \"\"\"展示 smolagents 的架构和概念\"\"\"\n    print(\"🏗️ === smolagents 架构概览 ===\\n\")\n    \n    architecture = \"\"\"\n    smolagents 核心组件:\n    \n    1. 🤖 Agent (智能代理)\n       ├── CodeAgent: 将动作写成 Python 代码\n       └── ToolCallingAgent: 传统的工具调用方式\n    \n    2. 🧠 Model (语言模型)\n       ├── InferenceClientModel: HF Inference API\n       ├── OpenAIServerModel: OpenAI 兼容服务器\n       ├── LiteLLMModel: 支持 100+ LLM\n       └── TransformersModel: 本地 transformers 模型\n    \n    3. 🛠️ Tools (工具)\n       ├── 内置工具: WebSearchTool, PythonInterpreterTool 等\n       └── 自定义工具: 继承 Tool 类实现\n    \n    4. 🔧 Executors (执行器)\n       ├── LocalPythonExecutor: 本地执行\n       ├── E2BExecutor: E2B 沙箱\n       └── DockerExecutor: Docker 容器\n    \n    工作流程:\n    用户请求 → Agent 分析 → 生成代码 → 调用工具 → 执行代码 → 返回结果\n    \"\"\"\n    \n    print(architecture)\n\ndef show_code_agent_example():\n    \"\"\"展示 CodeAgent 的代码生成示例\"\"\"\n    print(\"💻 === CodeAgent 代码生成示例 ===\\n\")\n    \n    example_code = '''\n    # CodeAgent 生成的代码示例\n    \n    # 1. 数据处理\n    data = [10, 25, 30, 45, 50]\n    mean = sum(data) / len(data)\n    print(f\"平均值: {mean}\")\n    \n    # 2. 工具调用\n    weather_result = weather_tool(\"北京\")\n    print(weather_result)\n    \n    # 3. 复杂逻辑\n    for city in [\"北京\", \"上海\", \"广州\"]:\n        weather = weather_tool(city)\n        analysis = data_analysis(f\"[{weather.temperature}]\", \"basic_stats\")\n        print(f\"{city}: {analysis}\")\n    \n    # 4. 最终答案\n    final_answer(\"分析完成，所有城市的天气数据已处理\")\n    '''\n    \n    print(\"CodeAgent 的优势:\")\n    print(\"✅ 更自然的代码表达\")\n    print(\"✅ 支持复杂的控制流程\")\n    print(\"✅ 可以组合多个工具调用\")\n    print(\"✅ 减少 30% 的步骤数\")\n    print(\"\\n示例代码:\")\n    print(example_code)\n\ndef show_project_structure():\n    \"\"\"展示项目结构和使用指南\"\"\"\n    print(\"📁 === 项目结构和使用指南 ===\\n\")\n    \n    structure = \"\"\"\n    项目文件说明:\n    \n    📄 learn.py                 # 主学习文件\n    📄 requirements.txt         # 依赖列表\n    📄 README.md               # 项目说明\n    \n    📁 examples/               # 示例代码\n    ├── basic_agent.py         # 基础 Agent 使用\n    ├── custom_tools.py        # 自定义工具示例\n    ├── multi_agent.py         # 多 Agent 协作 (待创建)\n    └── secure_execution.py    # 安全执行环境 (待创建)\n    \n    📁 tools/                  # 自定义工具\n    ├── __init__.py\n    └── custom_tools.py        # 工具实现\n    \n    📁 config/                 # 配置文件\n    └── agent_config.py        # Agent 配置\n    \n    使用步骤:\n    1. 安装依赖: pip install -r requirements.txt\n    2. 设置环境变量 (可选): export HUGGINGFACE_TOKEN=\"your_token\"\n    3. 运行示例: python examples/basic_agent.py\n    4. 创建自定义工具: 参考 tools/custom_tools.py\n    5. 构建自己的 Agent: 参考 examples/ 中的示例\n    \"\"\"\n    \n    print(structure)\n\nif __name__ == \"__main__\":\n    print(\"🤖 欢迎使用 smolagents 学习项目！\\n\")\n    \n    # 直接测试自定义工具\n    demo_custom_tools_directly()\n    \n    # 展示架构\n    show_smolagents_architecture()\n    \n    # 展示代码示例\n    show_code_agent_example()\n    \n    # 展示项目结构\n    show_project_structure()\n    \n    print(\"✅ 演示完成！\")\n    print(\"\\n💡 下一步:\")\n    print(\"1. 设置 HUGGINGFACE_TOKEN 环境变量\")\n    print(\"2. 运行 python examples/basic_agent.py\")\n    print(\"3. 尝试修改和创建自己的工具\")\n    print(\"4. 探索多 Agent 协作功能\")\n", "hash": "81fe6edd59a317e7336cf99f8cc416b1", "size": 5428}, "requirements.txt": {"content": "# smolagents 核心库和工具包\nsmolagents[toolkit]\n\n# 额外的依赖\nrequests\npandas\nnumpy\nmatplotlib\npillow\n\n# 可选的模型提供商\n# openai\n# anthropic\n# litellm\n\n# 安全执行环境（可选）\n# e2b\n# docker\n", "hash": "519cc65714b131ed39db518dbfda0121", "size": 222}, "test_smolagents.py": {"content": "\"\"\"\n简单的 smolagents 测试\n验证基本功能是否正常工作\n\"\"\"\n\nimport os\nfrom smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool\n\ndef test_basic_functionality():\n    \"\"\"测试基本功能\"\"\"\n    print(\"🧪 测试 smolagents 基本功能...\\n\")\n    \n    try:\n        # 创建模型 - 使用 HF Inference API\n        model = InferenceClientModel(\n            model_id=\"microsoft/DialoGPT-medium\",  # 使用一个更简单的模型进行测试\n            token=os.environ.get(\"HUGGINGFACE_TOKEN\"),\n            provider=\"hf-inference\"  # 明确指定 HF 推理提供商\n        )\n        print(\"✅ 模型创建成功\")\n        \n        # 创建工具\n        tools = [PythonInterpreterTool()]\n        print(\"✅ 工具创建成功\")\n        \n        # 创建 Agent\n        agent = CodeAgent(\n            tools=tools,\n            model=model,\n            stream_outputs=False,  # 关闭流输出以便测试\n        )\n        print(\"✅ Agent 创建成功\")\n        \n        # 测试简单任务\n        print(\"\\n🔍 测试简单计算任务...\")\n        result = agent.run(\"计算 2 + 3 * 4 的结果\")\n        print(f\"结果: {result}\")\n        \n        print(\"\\n✅ 所有测试通过！smolagents 工作正常。\")\n        return True\n        \n    except Exception as e:\n        print(f\"❌ 测试失败: {e}\")\n        return False\n\nif __name__ == \"__main__\":\n    success = test_basic_functionality()\n    if success:\n        print(\"\\n🎉 可以继续运行完整的示例了！\")\n    else:\n        print(\"\\n💡 请检查:\")\n        print(\"1. 网络连接\")\n        print(\"2. smolagents 安装\")\n        print(\"3. 环境变量设置\")\n", "hash": "14a12b8eab85a40d32a57a93daf4006d", "size": 1672}, "README.md": {"content": "# smolagents 学习项目\n\n这是一个基于 Hugging Face smolagents 库的 Agent 开发学习项目。\n\n## 项目结构\n\n```\n.\n├── README.md                 # 项目说明\n├── requirements.txt          # 依赖列表\n├── learn.py                 # 主学习文件\n├── examples/                # 示例代码\n│   ├── basic_agent.py       # 基础 Agent 示例\n│   ├── custom_tools.py      # 自定义工具示例\n│   ├── multi_agent.py       # 多 Agent 协作示例\n│   └── secure_execution.py  # 安全执行环境示例\n├── tools/                   # 自定义工具\n│   └── __init__.py\n└── config/                  # 配置文件\n    └── agent_config.py\n```\n\n## 安装依赖\n\n```bash\npip install -r requirements.txt\n```\n\n## 快速开始\n\n1. 设置环境变量（如果使用外部模型）：\n```bash\nexport HUGGINGFACE_TOKEN=\"your_token_here\"\n# 或者其他模型提供商的 API key\n```\n\n2. 运行基础示例：\n```bash\npython examples/basic_agent.py\n```\n\n## smolagents 核心概念\n\n### Agent 类型\n- **CodeAgent**: 将动作写成 Python 代码片段\n- **ToolCallingAgent**: 使用传统的工具调用方式\n\n### 核心特性\n- 🧑‍💻 代码优先的 Agent 设计\n- 🛠️ 丰富的工具生态系统\n- 🌐 支持多种模型提供商\n- 🔒 安全的代码执行环境\n- 🤗 Hub 集成\n\n## 学习路径\n\n1. [基础 Agent 使用](examples/basic_agent.py)\n2. [创建自定义工具](examples/custom_tools.py)\n3. [多 Agent 协作](examples/multi_agent.py)\n4. [安全执行环境](examples/secure_execution.py)\n", "hash": "3a6a2da00be1a34eef341f134cfbe39e", "size": 1611}, "learn.py": {"content": "\"\"\"\nsmolagents 学习项目\n基于 Hugging Face smolagents 库的 Agent 开发示例\n\n这是一个完整的 smolagents 学习项目，包含：\n- 基础 Agent 使用\n- 自定义工具开发\n- 多 Agent 协作\n- 安全执行环境配置\n\"\"\"\n\n# 主学习文件 - 项目概览和快速开始指南\n\ndef main():\n    \"\"\"主函数 - 展示项目概览\"\"\"\n    print(\"🤖 欢迎来到 smolagents 学习项目！\\n\")\n\n    print(\"📚 项目内容:\")\n    print(\"1. 基础 Agent 示例 - examples/basic_agent.py\")\n    print(\"2. 自定义工具开发 - examples/custom_tools.py\")\n    print(\"3. 多 Agent 协作 - examples/multi_agent.py\")\n    print(\"4. 安全执行环境 - examples/secure_execution.py\")\n    print(\"5. 简单演示 - simple_demo.py\")\n\n    print(\"\\n🛠️ 自定义工具:\")\n    print(\"- WeatherTool: 天气查询工具\")\n    print(\"- DataAnalysisTool: 数据分析工具\")\n    print(\"- TimerTool: 计时器工具\")\n    print(\"- FileManagerTool: 文件管理工具\")\n\n    print(\"\\n🚀 快速开始:\")\n    print(\"1. 运行简单演示: python simple_demo.py\")\n    print(\"2. 测试自定义工具: python examples/custom_tools.py\")\n    print(\"3. 体验多 Agent 协作: python examples/multi_agent.py\")\n\n    print(\"\\n💡 提示:\")\n    print(\"- 设置 HUGGINGFACE_TOKEN 环境变量以使用外部模型\")\n    print(\"- 查看 README.md 了解详细说明\")\n    print(\"- 参考 config/agent_config.py 进行配置\")\n\ndef show_smolagents_overview():\n    \"\"\"展示 smolagents 概览\"\"\"\n    print(\"\\n🏗️ smolagents 架构:\")\n    print(\"Agent (智能代理) + Model (语言模型) + Tools (工具) + Executor (执行器)\")\n\n    print(\"\\n🔥 核心特性:\")\n    print(\"✨ 代码优先的 Agent 设计\")\n    print(\"🧑‍💻 支持多种语言模型\")\n    print(\"🛠️ 丰富的工具生态\")\n    print(\"🔒 安全的执行环境\")\n    print(\"🤗 Hub 集成\")\n\nif __name__ == \"__main__\":\n    main()\n    show_smolagents_overview()", "hash": "a80fe5d86ef75658d4381014f1f2785c", "size": 1939}, "codeAgent.py": {"content": "\"\"\"\nCodeAgent - 基于 smolagents 的智能代码编写助手\n集成 DeepSeek 模型，实现从自然语言到代码生成的全流程自动化\n\n主要功能：\n- 自然语言理解与代码生成\n- 文件操作与项目管理\n- 命令执行与环境管理\n- 任务分解与多工具协同\n- 记忆管理与检查点机制\n\"\"\"\n\nimport os\nimport json\nimport time\nimport hashlib\nimport subprocess\nfrom datetime import datetime\nfrom typing import Dict, List, Any, Optional\nfrom pathlib import Path\n\nfrom smolagents import CodeAgent, Tool, LiteLLMModel\nfrom smolagents import PythonInterpreterTool\n\nclass MemoryManager:\n    \"\"\"记忆管理系统 - 实现持久化记忆与检索\"\"\"\n    \n    def __init__(self, memory_dir: str = \".codeagent_memory\"):\n        self.memory_dir = Path(memory_dir)\n        self.memory_dir.mkdir(exist_ok=True)\n        \n        # 记忆文件路径\n        self.conversation_file = self.memory_dir / \"conversations.json\"\n        self.knowledge_file = self.memory_dir / \"knowledge_base.json\"\n        self.checkpoints_dir = self.memory_dir / \"checkpoints\"\n        self.checkpoints_dir.mkdir(exist_ok=True)\n        \n        # 初始化记忆存储\n        self._init_memory_files()\n    \n    def _init_memory_files(self):\n        \"\"\"初始化记忆文件\"\"\"\n        if not self.conversation_file.exists():\n            self._save_json(self.conversation_file, {\"conversations\": []})\n        \n        if not self.knowledge_file.exists():\n            self._save_json(self.knowledge_file, {\n                \"code_patterns\": {},\n                \"project_structures\": {},\n                \"user_preferences\": {},\n                \"learned_solutions\": {}\n            })\n    \n    def _save_json(self, file_path: Path, data: Dict):\n        \"\"\"保存 JSON 数据\"\"\"\n        with open(file_path, 'w', encoding='utf-8') as f:\n            json.dump(data, f, ensure_ascii=False, indent=2)\n    \n    def _load_json(self, file_path: Path) -> Dict:\n        \"\"\"加载 JSON 数据\"\"\"\n        try:\n            with open(file_path, 'r', encoding='utf-8') as f:\n                return json.load(f)\n        except (FileNotFoundError, json.JSONDecodeError):\n            return {}\n    \n    def store_conversation(self, user_input: str, agent_response: str, context: Dict = None):\n        \"\"\"存储对话记录\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        conversation_entry = {\n            \"timestamp\": datetime.now().isoformat(),\n            \"user_input\": user_input,\n            \"agent_response\": agent_response,\n            \"context\": context or {}\n        }\n        \n        conversations[\"conversations\"].append(conversation_entry)\n        \n        # 保持最近 100 条对话\n        if len(conversations[\"conversations\"]) > 100:\n            conversations[\"conversations\"] = conversations[\"conversations\"][-100:]\n        \n        self._save_json(self.conversation_file, conversations)\n    \n    def store_knowledge(self, category: str, key: str, value: Any):\n        \"\"\"存储知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            knowledge[category] = {}\n        \n        knowledge[category][key] = {\n            \"value\": value,\n            \"timestamp\": datetime.now().isoformat()\n        }\n        \n        self._save_json(self.knowledge_file, knowledge)\n    \n    def retrieve_knowledge(self, category: str, key: str = None) -> Any:\n        \"\"\"检索知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            return None\n        \n        if key is None:\n            return knowledge[category]\n        \n        return knowledge[category].get(key, {}).get(\"value\")\n    \n    def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:\n        \"\"\"搜索相关对话\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        relevant_conversations = []\n        query_lower = query.lower()\n        \n        for conv in conversations.get(\"conversations\", []):\n            if (query_lower in conv[\"user_input\"].lower() or \n                query_lower in conv[\"agent_response\"].lower()):\n                relevant_conversations.append(conv)\n        \n        return relevant_conversations[-limit:]\n\nclass CheckpointManager:\n    \"\"\"检查点管理系统 - 实现工作区状态管理\"\"\"\n    \n    def __init__(self, memory_manager: MemoryManager):\n        self.memory_manager = memory_manager\n        self.checkpoints_dir = memory_manager.checkpoints_dir\n    \n    def create_checkpoint(self, name: str, description: str = \"\") -> str:\n        \"\"\"创建检查点\"\"\"\n        checkpoint_id = f\"{name}_{int(time.time())}\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_dir.mkdir(exist_ok=True)\n        \n        # 保存当前工作目录状态\n        current_files = self._get_current_files()\n        \n        checkpoint_data = {\n            \"id\": checkpoint_id,\n            \"name\": name,\n            \"description\": description,\n            \"timestamp\": datetime.now().isoformat(),\n            \"files\": current_files,\n            \"working_directory\": str(Path.cwd())\n        }\n        \n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        self.memory_manager._save_json(checkpoint_file, checkpoint_data)\n        \n        # 复制重要文件\n        self._backup_files(checkpoint_dir, current_files)\n        \n        return checkpoint_id\n    \n    def _get_current_files(self) -> Dict[str, str]:\n        \"\"\"获取当前目录的文件信息\"\"\"\n        files_info = {}\n        current_dir = Path.cwd()\n        \n        for file_path in current_dir.rglob(\"*\"):\n            if file_path.is_file() and not self._should_ignore_file(file_path):\n                relative_path = file_path.relative_to(current_dir)\n                try:\n                    with open(file_path, 'r', encoding='utf-8') as f:\n                        content = f.read()\n                    files_info[str(relative_path)] = {\n                        \"content\": content,\n                        \"hash\": hashlib.md5(content.encode()).hexdigest(),\n                        \"size\": file_path.stat().st_size\n                    }\n                except (UnicodeDecodeError, PermissionError):\n                    # 跳过二进制文件或无权限文件\n                    files_info[str(relative_path)] = {\n                        \"content\": \"[BINARY_FILE]\",\n                        \"hash\": \"binary\",\n                        \"size\": file_path.stat().st_size\n                    }\n        \n        return files_info\n    \n    def _should_ignore_file(self, file_path: Path) -> bool:\n        \"\"\"判断是否应该忽略文件\"\"\"\n        ignore_patterns = [\n            \".git\", \"__pycache__\", \".pytest_cache\", \"node_modules\",\n            \".codeagent_memory\", \"*.pyc\", \"*.pyo\", \"*.pyd\", \".DS_Store\"\n        ]\n        \n        path_str = str(file_path)\n        for pattern in ignore_patterns:\n            if pattern in path_str:\n                return True\n        \n        return False\n    \n    def _backup_files(self, checkpoint_dir: Path, files_info: Dict):\n        \"\"\"备份文件到检查点目录\"\"\"\n        files_dir = checkpoint_dir / \"files\"\n        files_dir.mkdir(exist_ok=True)\n        \n        for file_path, info in files_info.items():\n            if info[\"content\"] != \"[BINARY_FILE]\":\n                backup_file = files_dir / file_path\n                backup_file.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(backup_file, 'w', encoding='utf-8') as f:\n                    f.write(info[\"content\"])\n    \n    def list_checkpoints(self) -> List[Dict]:\n        \"\"\"列出所有检查点\"\"\"\n        checkpoints = []\n        \n        for checkpoint_dir in self.checkpoints_dir.iterdir():\n            if checkpoint_dir.is_dir():\n                checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n                if checkpoint_file.exists():\n                    checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n                    checkpoints.append(checkpoint_data)\n        \n        return sorted(checkpoints, key=lambda x: x[\"timestamp\"], reverse=True)\n    \n    def restore_checkpoint(self, checkpoint_id: str) -> bool:\n        \"\"\"恢复检查点\"\"\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        \n        if not checkpoint_file.exists():\n            return False\n        \n        checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n        files_dir = checkpoint_dir / \"files\"\n        \n        if not files_dir.exists():\n            return False\n        \n        # 恢复文件\n        for file_path in files_dir.rglob(\"*\"):\n            if file_path.is_file():\n                relative_path = file_path.relative_to(files_dir)\n                target_path = Path.cwd() / relative_path\n                target_path.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(file_path, 'r', encoding='utf-8') as src:\n                    content = src.read()\n                \n                with open(target_path, 'w', encoding='utf-8') as dst:\n                    dst.write(content)\n        \n        return True\n\nclass CodeGenerationTool(Tool):\n    \"\"\"代码生成工具 - 基于自然语言生成代码\"\"\"\n\n    name = \"code_generation\"\n    description = \"根据自然语言描述生成代码，支持多种编程语言\"\n    inputs = {\n        \"description\": {\n            \"type\": \"string\",\n            \"description\": \"代码功能的自然语言描述\"\n        },\n        \"language\": {\n            \"type\": \"string\",\n            \"description\": \"编程语言（python, javascript, java, cpp 等）\",\n            \"nullable\": True\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"相关上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, description: str, language: str = \"python\", context: str = \"\") -> str:\n        \"\"\"生成代码\"\"\"\n        # 搜索相关历史经验\n        similar_conversations = self.memory_manager.search_conversations(description, limit=3)\n\n        # 获取用户偏好\n        user_preferences = self.memory_manager.retrieve_knowledge(\"user_preferences\") or {}\n\n        # 构建增强的提示\n        enhanced_prompt = f\"\"\"\n基于以下描述生成 {language} 代码：\n\n需求描述：{description}\n\n上下文信息：{context}\n\n用户偏好：{json.dumps(user_preferences, ensure_ascii=False, indent=2)}\n\n相关历史经验：\n{json.dumps(similar_conversations, ensure_ascii=False, indent=2)}\n\n请生成清晰、可读、符合最佳实践的代码，并包含必要的注释。\n\"\"\"\n\n        # 这里应该调用 LLM 生成代码，暂时返回模板\n        generated_code = f\"\"\"\n# 根据描述生成的 {language} 代码\n# 需求：{description}\n\n# TODO: 实际的代码生成逻辑\ndef generated_function():\n    '''\n    {description}\n    '''\n    pass\n\"\"\"\n\n        # 存储生成的代码模式\n        self.memory_manager.store_knowledge(\n            \"code_patterns\",\n            f\"{language}_{hashlib.md5(description.encode()).hexdigest()[:8]}\",\n            {\n                \"description\": description,\n                \"language\": language,\n                \"code\": generated_code,\n                \"context\": context\n            }\n        )\n\n        return generated_code\n\nclass FileOperationTool(Tool):\n    \"\"\"文件操作工具 - 处理文件读写、创建、删除等操作\"\"\"\n\n    name = \"file_operation\"\n    description = \"执行文件操作：创建、读取、写入、删除、移动文件\"\n    inputs = {\n        \"operation\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：create, read, write, delete, move, list\"\n        },\n        \"file_path\": {\n            \"type\": \"string\",\n            \"description\": \"文件路径\"\n        },\n        \"content\": {\n            \"type\": \"string\",\n            \"description\": \"文件内容（用于 write 操作）\",\n            \"nullable\": True\n        },\n        \"target_path\": {\n            \"type\": \"string\",\n            \"description\": \"目标路径（用于 move 操作）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, operation: str, file_path: str, content: str = \"\", target_path: str = \"\") -> str:\n        \"\"\"执行文件操作\"\"\"\n        try:\n            path = Path(file_path)\n\n            if operation == \"create\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n                path.touch()\n                return f\"文件 {file_path} 创建成功\"\n\n            elif operation == \"read\":\n                if not path.exists():\n                    return f\"错误：文件 {file_path} 不存在\"\n\n                with open(path, 'r', encoding='utf-8') as f:\n                    file_content = f.read()\n\n                # 存储文件访问记录\n                self.memory_manager.store_knowledge(\n                    \"file_access\",\n                    str(path),\n                    {\"last_read\": datetime.now().isoformat(), \"size\": len(file_content)}\n                )\n\n                return file_content\n\n            elif operation == \"write\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n\n                with open(path, 'w', encoding='utf-8') as f:\n                    f.write(content)\n\n                # 存储文件修改记录\n                self.memory_manager.store_knowledge(\n                    \"file_modifications\",\n                    str(path),\n                    {\n                        \"last_modified\": datetime.now().isoformat(),\n                        \"content_hash\": hashlib.md5(content.encode()).hexdigest()\n                    }\n                )\n\n                return f\"内容已写入文件 {file_path}\"\n\n            elif operation == \"delete\":\n                if path.exists():\n                    if path.is_file():\n                        path.unlink()\n                        return f\"文件 {file_path} 删除成功\"\n                    elif path.is_dir():\n                        import shutil\n                        shutil.rmtree(path)\n                        return f\"目录 {file_path} 删除成功\"\n                else:\n                    return f\"错误：路径 {file_path} 不存在\"\n\n            elif operation == \"move\":\n                if not path.exists():\n                    return f\"错误：源文件 {file_path} 不存在\"\n\n                target = Path(target_path)\n                target.parent.mkdir(parents=True, exist_ok=True)\n                path.rename(target)\n                return f\"文件从 {file_path} 移动到 {target_path}\"\n\n            elif operation == \"list\":\n                if path.is_dir():\n                    files = [str(p) for p in path.iterdir()]\n                    return f\"目录 {file_path} 内容：\\n\" + \"\\n\".join(files)\n                else:\n                    return f\"错误：{file_path} 不是目录\"\n\n            else:\n                return f\"不支持的操作：{operation}\"\n\n        except Exception as e:\n            error_msg = f\"文件操作失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"file_op_{int(time.time())}\",\n                {\n                    \"operation\": operation,\n                    \"file_path\": file_path,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\nclass CommandExecutionTool(Tool):\n    \"\"\"命令执行工具 - 安全执行系统命令\"\"\"\n\n    name = \"command_execution\"\n    description = \"执行系统命令，支持安全检查和结果记录\"\n    inputs = {\n        \"command\": {\n            \"type\": \"string\",\n            \"description\": \"要执行的命令\"\n        },\n        \"working_dir\": {\n            \"type\": \"string\",\n            \"description\": \"工作目录\",\n            \"nullable\": True\n        },\n        \"timeout\": {\n            \"type\": \"number\",\n            \"description\": \"超时时间（秒）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n        # 危险命令黑名单\n        self.dangerous_commands = [\n            \"rm -rf\", \"del /f\", \"format\", \"fdisk\", \"mkfs\",\n            \"shutdown\", \"reboot\", \"halt\", \"poweroff\",\n            \"chmod 777\", \"chown\", \"sudo rm\", \"dd if=\"\n        ]\n\n    def _is_safe_command(self, command: str) -> bool:\n        \"\"\"检查命令是否安全\"\"\"\n        command_lower = command.lower()\n\n        for dangerous in self.dangerous_commands:\n            if dangerous in command_lower:\n                return False\n\n        return True\n\n    def forward(self, command: str, working_dir: str = \"\", timeout: float = 30.0) -> str:\n        \"\"\"执行命令\"\"\"\n        # 安全检查\n        if not self._is_safe_command(command):\n            error_msg = f\"危险命令被阻止：{command}\"\n            self.memory_manager.store_knowledge(\n                \"security_blocks\",\n                f\"cmd_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"reason\": \"dangerous_command\",\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n            return error_msg\n\n        try:\n            # 设置工作目录\n            cwd = Path(working_dir) if working_dir else Path.cwd()\n\n            # 执行命令\n            result = subprocess.run(\n                command,\n                shell=True,\n                cwd=cwd,\n                capture_output=True,\n                text=True,\n                timeout=timeout\n            )\n\n            # 构建结果\n            output = f\"命令执行完成\\n\"\n            output += f\"返回码: {result.returncode}\\n\"\n\n            if result.stdout:\n                output += f\"标准输出:\\n{result.stdout}\\n\"\n\n            if result.stderr:\n                output += f\"标准错误:\\n{result.stderr}\\n\"\n\n            # 记录命令执行\n            self.memory_manager.store_knowledge(\n                \"command_history\",\n                f\"cmd_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"working_dir\": str(cwd),\n                    \"return_code\": result.returncode,\n                    \"stdout\": result.stdout,\n                    \"stderr\": result.stderr,\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return output\n\n        except subprocess.TimeoutExpired:\n            return f\"命令执行超时（{timeout}秒）：{command}\"\n\n        except Exception as e:\n            error_msg = f\"命令执行失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"cmd_error_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\nclass TaskDecompositionTool(Tool):\n    \"\"\"任务分解工具 - 将复杂任务分解为子任务\"\"\"\n\n    name = \"task_decomposition\"\n    description = \"将复杂任务分解为可执行的子任务序列\"\n    inputs = {\n        \"task_description\": {\n            \"type\": \"string\",\n            \"description\": \"复杂任务的描述\"\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"任务上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, task_description: str, context: str = \"\") -> str:\n        \"\"\"分解任务\"\"\"\n        # 搜索相似任务的历史分解\n        similar_tasks = self.memory_manager.search_conversations(task_description, limit=3)\n\n        # 获取已知的项目结构模式\n        project_patterns = self.memory_manager.retrieve_knowledge(\"project_structures\") or {}\n\n        # 基于规则的任务分解（简化版本）\n        subtasks = self._decompose_task(task_description, context)\n\n        # 格式化输出\n        result = f\"任务分解结果：{task_description}\\n\\n\"\n        result += \"子任务序列：\\n\"\n\n        for i, subtask in enumerate(subtasks, 1):\n            result += f\"{i}. {subtask['name']}\\n\"\n            result += f\"   描述: {subtask['description']}\\n\"\n            result += f\"   工具: {subtask['tools']}\\n\"\n            result += f\"   预估时间: {subtask['estimated_time']}\\n\\n\"\n\n        # 存储任务分解结果\n        self.memory_manager.store_knowledge(\n            \"task_decompositions\",\n            hashlib.md5(task_description.encode()).hexdigest()[:8],\n            {\n                \"original_task\": task_description,\n                \"context\": context,\n                \"subtasks\": subtasks,\n                \"timestamp\": datetime.now().isoformat()\n            }\n        )\n\n        return result\n\n    def _decompose_task(self, task_description: str, context: str) -> List[Dict]:\n        \"\"\"基于规则的任务分解\"\"\"\n        subtasks = []\n\n        # 分析任务类型\n        task_lower = task_description.lower()\n\n        if \"创建\" in task_lower or \"开发\" in task_lower:\n            if \"网站\" in task_lower or \"web\" in task_lower:\n                subtasks.extend(self._web_development_tasks())\n            elif \"api\" in task_lower:\n                subtasks.extend(self._api_development_tasks())\n            elif \"数据分析\" in task_lower:\n                subtasks.extend(self._data_analysis_tasks())\n            else:\n                subtasks.extend(self._general_development_tasks())\n\n        elif \"分析\" in task_lower:\n            subtasks.extend(self._analysis_tasks())\n\n        elif \"测试\" in task_lower:\n            subtasks.extend(self._testing_tasks())\n\n        else:\n            # 通用任务分解\n            subtasks.extend(self._general_tasks(task_description))\n\n        return subtasks\n\n    def _web_development_tasks(self) -> List[Dict]:\n        \"\"\"Web 开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"项目初始化\",\n                \"description\": \"创建项目目录结构和配置文件\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"前端开发\",\n                \"description\": \"创建HTML、CSS、JavaScript文件\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"后端开发\",\n                \"description\": \"实现服务器端逻辑\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"45分钟\"\n            },\n            {\n                \"name\": \"测试和部署\",\n                \"description\": \"运行测试并部署应用\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _api_development_tasks(self) -> List[Dict]:\n        \"\"\"API 开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"API设计\",\n                \"description\": \"设计API接口和数据模型\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"20分钟\"\n            },\n            {\n                \"name\": \"实现API端点\",\n                \"description\": \"编写API路由和处理函数\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"40分钟\"\n            },\n            {\n                \"name\": \"添加文档\",\n                \"description\": \"生成API文档\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _data_analysis_tasks(self) -> List[Dict]:\n        \"\"\"数据分析任务分解\"\"\"\n        return [\n            {\n                \"name\": \"数据收集\",\n                \"description\": \"获取和加载数据\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"数据清洗\",\n                \"description\": \"处理缺失值和异常数据\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"25分钟\"\n            },\n            {\n                \"name\": \"数据分析\",\n                \"description\": \"执行统计分析和可视化\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"生成报告\",\n                \"description\": \"创建分析报告\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"20分钟\"\n            }\n        ]\n\n    def _general_development_tasks(self) -> List[Dict]:\n        \"\"\"通用开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"需求分析\",\n                \"description\": \"分析和理解需求\",\n                \"tools\": [],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"代码实现\",\n                \"description\": \"编写核心功能代码\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"40分钟\"\n            },\n            {\n                \"name\": \"测试验证\",\n                \"description\": \"测试功能正确性\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _analysis_tasks(self) -> List[Dict]:\n        \"\"\"分析任务分解\"\"\"\n        return [\n            {\n                \"name\": \"数据收集\",\n                \"description\": \"收集相关数据和信息\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"数据处理\",\n                \"description\": \"清洗和预处理数据\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"20分钟\"\n            },\n            {\n                \"name\": \"分析计算\",\n                \"description\": \"执行分析算法\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"25分钟\"\n            },\n            {\n                \"name\": \"结果展示\",\n                \"description\": \"可视化和报告生成\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"20分钟\"\n            }\n        ]\n\n    def _testing_tasks(self) -> List[Dict]:\n        \"\"\"测试任务分解\"\"\"\n        return [\n            {\n                \"name\": \"测试计划\",\n                \"description\": \"制定测试策略和计划\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"编写测试\",\n                \"description\": \"创建测试用例和测试代码\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"执行测试\",\n                \"description\": \"运行测试并收集结果\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"测试报告\",\n                \"description\": \"生成测试报告\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            }\n        ]\n\n    def _general_tasks(self, task_description: str) -> List[Dict]:\n        \"\"\"通用任务分解\"\"\"\n        return [\n            {\n                \"name\": \"任务准备\",\n                \"description\": f\"准备执行任务：{task_description}\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"5分钟\"\n            },\n            {\n                \"name\": \"主要执行\",\n                \"description\": \"执行主要任务逻辑\",\n                \"tools\": [\"code_generation\", \"file_operation\", \"command_execution\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"结果整理\",\n                \"description\": \"整理和验证结果\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            }\n        ]\n\nclass CheckpointTool(Tool):\n    \"\"\"检查点管理工具 - 管理工作区状态检查点\"\"\"\n\n    name = \"checkpoint_management\"\n    description = \"创建、列出、恢复工作区检查点\"\n    inputs = {\n        \"operation\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：create, list, restore\"\n        },\n        \"checkpoint_name\": {\n            \"type\": \"string\",\n            \"description\": \"检查点名称\",\n            \"nullable\": True\n        },\n        \"description\": {\n            \"type\": \"string\",\n            \"description\": \"检查点描述\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, checkpoint_manager: CheckpointManager):\n        super().__init__()\n        self.checkpoint_manager = checkpoint_manager\n\n    def forward(self, operation: str, checkpoint_name: str = \"\", description: str = \"\") -> str:\n        \"\"\"执行检查点操作\"\"\"\n        try:\n            if operation == \"create\":\n                if not checkpoint_name:\n                    checkpoint_name = f\"auto_{int(time.time())}\"\n\n                checkpoint_id = self.checkpoint_manager.create_checkpoint(\n                    checkpoint_name, description\n                )\n                return f\"检查点创建成功：{checkpoint_id}\"\n\n            elif operation == \"list\":\n                checkpoints = self.checkpoint_manager.list_checkpoints()\n\n                if not checkpoints:\n                    return \"没有找到检查点\"\n\n                result = \"可用检查点：\\n\"\n                for cp in checkpoints:\n                    result += f\"- {cp['id']}: {cp['name']}\\n\"\n                    result += f\"  描述: {cp['description']}\\n\"\n                    result += f\"  时间: {cp['timestamp']}\\n\\n\"\n\n                return result\n\n            elif operation == \"restore\":\n                if not checkpoint_name:\n                    return \"错误：需要指定检查点名称\"\n\n                success = self.checkpoint_manager.restore_checkpoint(checkpoint_name)\n\n                if success:\n                    return f\"检查点恢复成功：{checkpoint_name}\"\n                else:\n                    return f\"检查点恢复失败：{checkpoint_name}\"\n\n            else:\n                return f\"不支持的操作：{operation}\"\n\n        except Exception as e:\n            return f\"检查点操作失败：{str(e)}\"\n\nclass IntelligentCodeAgent:\n    \"\"\"智能代码编写助手 - 主要的 Agent 类\"\"\"\n\n    def __init__(self, api_key: str = None, model_name: str = \"deepseek-chat\"):\n        \"\"\"初始化 CodeAgent\"\"\"\n\n        # 初始化记忆和检查点管理\n        self.memory_manager = MemoryManager()\n        self.checkpoint_manager = CheckpointManager(self.memory_manager)\n\n        # 初始化工具\n        self.tools = [\n            PythonInterpreterTool(),\n            CodeGenerationTool(self.memory_manager),\n            FileOperationTool(self.memory_manager),\n            CommandExecutionTool(self.memory_manager),\n            TaskDecompositionTool(self.memory_manager),\n            CheckpointTool(self.checkpoint_manager)\n        ]\n\n        # 初始化模型\n        self.model = self._setup_model(api_key, model_name)\n\n        # 创建 smolagents CodeAgent\n        self.agent = CodeAgent(\n            tools=self.tools,\n            model=self.model,\n            stream_outputs=True\n        )\n\n        # 系统提示词\n        self.system_prompt = self._build_system_prompt()\n\n        print(\"🤖 智能代码助手已初始化完成！\")\n        print(\"💡 支持功能：代码生成、文件操作、命令执行、任务分解、记忆管理\")\n\n    def _setup_model(self, api_key: str, model_name: str):\n        \"\"\"设置模型\"\"\"\n        try:\n            # 使用 LiteLLM 支持 DeepSeek\n            model = LiteLLMModel(\n                model_id=model_name,\n                api_key=api_key or os.environ.get(\"DEEPSEEK_API_KEY\"),\n                api_base=\"https://api.deepseek.com/v1\",\n                temperature=0.1,\n                max_tokens=4000\n            )\n            return model\n        except Exception as e:\n            print(f\"⚠️ 模型初始化失败，使用默认配置: {e}\")\n            # 回退到默认模型\n            from smolagents import InferenceClientModel\n            return InferenceClientModel(\n                model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n                provider=\"auto\"\n            )\n\n    def _build_system_prompt(self) -> str:\n        \"\"\"构建系统提示词\"\"\"\n        return \"\"\"\n你是一个专业的代码编写助手，具有以下能力：\n\n🎯 核心功能：\n1. 代码生成：根据自然语言描述生成高质量代码\n2. 文件操作：创建、读取、修改、删除文件和目录\n3. 命令执行：安全执行系统命令和脚本\n4. 任务分解：将复杂任务分解为可执行的子任务\n5. 记忆管理：记住用户偏好和历史交互\n6. 检查点管理：创建和恢复工作区状态\n\n🛠️ 可用工具：\n- python_interpreter: Python 代码执行\n- code_generation: 代码生成\n- file_operation: 文件操作\n- command_execution: 命令执行\n- task_decomposition: 任务分解\n- checkpoint_management: 检查点管理\n\n💡 工作原则：\n1. 安全第一：避免执行危险命令\n2. 代码质量：生成清晰、可读、符合最佳实践的代码\n3. 用户体验：提供详细的解释和建议\n4. 持续学习：从交互中学习用户偏好\n5. 状态管理：适时创建检查点保护工作成果\n\n🔄 工作流程：\n1. 理解用户需求\n2. 分解复杂任务（如需要）\n3. 选择合适的工具\n4. 执行操作\n5. 验证结果\n6. 记录经验\n\n请始终以专业、友好的方式协助用户完成编程任务。\n\"\"\"\n\n    def run(self, user_input: str, auto_checkpoint: bool = True) -> str:\n        \"\"\"运行用户请求\"\"\"\n        try:\n            # 记录开始时间\n            start_time = time.time()\n\n            # 搜索相关历史记录\n            relevant_history = self.memory_manager.search_conversations(user_input, limit=3)\n\n            # 构建增强的输入\n            enhanced_input = self._enhance_input(user_input, relevant_history)\n\n            # 自动创建检查点（如果启用）\n            if auto_checkpoint:\n                checkpoint_id = self.checkpoint_manager.create_checkpoint(\n                    f\"before_{int(time.time())}\",\n                    f\"执行前检查点: {user_input[:50]}...\"\n                )\n                print(f\"📍 已创建检查点: {checkpoint_id}\")\n\n            # 执行任务\n            result = self.agent.run(enhanced_input)\n\n            # 计算执行时间\n            execution_time = time.time() - start_time\n\n            # 存储对话记录\n            self.memory_manager.store_conversation(\n                user_input,\n                result,\n                {\n                    \"execution_time\": execution_time,\n                    \"tools_used\": [tool.name for tool in self.tools],\n                    \"checkpoint_created\": auto_checkpoint\n                }\n            )\n\n            # 学习用户偏好\n            self._learn_from_interaction(user_input, result)\n\n            print(f\"⏱️ 执行时间: {execution_time:.2f}秒\")\n\n            return result\n\n        except Exception as e:\n            error_msg = f\"执行失败: {str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"execution_errors\",\n                f\"error_{int(time.time())}\",\n                {\n                    \"user_input\": user_input,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\n    def _enhance_input(self, user_input: str, relevant_history: List[Dict]) -> str:\n        \"\"\"增强用户输入\"\"\"\n        enhanced = f\"{self.system_prompt}\\n\\n\"\n\n        if relevant_history:\n            enhanced += \"📚 相关历史记录:\\n\"\n            for i, conv in enumerate(relevant_history, 1):\n                enhanced += f\"{i}. 用户: {conv['user_input'][:100]}...\\n\"\n                enhanced += f\"   助手: {conv['agent_response'][:100]}...\\n\\n\"\n\n        # 获取用户偏好\n        user_prefs = self.memory_manager.retrieve_knowledge(\"user_preferences\") or {}\n        if user_prefs:\n            enhanced += f\"👤 用户偏好: {json.dumps(user_prefs, ensure_ascii=False)}\\n\\n\"\n\n        enhanced += f\"🎯 当前任务: {user_input}\\n\\n\"\n        enhanced += \"请根据上述信息和你的能力，高效完成用户的请求。\"\n\n        return enhanced\n\n    def _learn_from_interaction(self, user_input: str, result: str):\n        \"\"\"从交互中学习\"\"\"\n        # 分析用户偏好\n        input_lower = user_input.lower()\n\n        # 编程语言偏好\n        languages = [\"python\", \"javascript\", \"java\", \"cpp\", \"go\", \"rust\"]\n        for lang in languages:\n            if lang in input_lower:\n                self.memory_manager.store_knowledge(\n                    \"user_preferences\",\n                    \"preferred_language\",\n                    lang\n                )\n                break\n\n        # 代码风格偏好\n        if \"注释\" in user_input or \"comment\" in input_lower:\n            self.memory_manager.store_knowledge(\n                \"user_preferences\",\n                \"wants_comments\",\n                True\n            )\n\n        if \"测试\" in user_input or \"test\" in input_lower:\n            self.memory_manager.store_knowledge(\n                \"user_preferences\",\n                \"wants_tests\",\n                True\n            )\n\n    def get_status(self) -> Dict:\n        \"\"\"获取助手状态\"\"\"\n        conversations = self.memory_manager._load_json(self.memory_manager.conversation_file)\n        checkpoints = self.checkpoint_manager.list_checkpoints()\n\n        return {\n            \"conversations_count\": len(conversations.get(\"conversations\", [])),\n            \"checkpoints_count\": len(checkpoints),\n            \"tools_count\": len(self.tools),\n            \"memory_dir\": str(self.memory_manager.memory_dir),\n            \"model_info\": {\n                \"type\": type(self.model).__name__,\n                \"model_id\": getattr(self.model, 'model_id', 'unknown')\n            }\n        }\n\n    def clear_memory(self, confirm: bool = False):\n        \"\"\"清除记忆（谨慎使用）\"\"\"\n        if not confirm:\n            print(\"⚠️ 此操作将清除所有记忆数据，请使用 clear_memory(confirm=True) 确认\")\n            return\n\n        import shutil\n        if self.memory_manager.memory_dir.exists():\n            shutil.rmtree(self.memory_manager.memory_dir)\n            self.memory_manager._init_memory_files()\n            print(\"🗑️ 记忆数据已清除\")\n\n    def export_memory(self, export_path: str = \"memory_export.json\"):\n        \"\"\"导出记忆数据\"\"\"\n        conversations = self.memory_manager._load_json(self.memory_manager.conversation_file)\n        knowledge = self.memory_manager._load_json(self.memory_manager.knowledge_file)\n        checkpoints = self.checkpoint_manager.list_checkpoints()\n\n        export_data = {\n            \"conversations\": conversations,\n            \"knowledge\": knowledge,\n            \"checkpoints\": checkpoints,\n            \"export_time\": datetime.now().isoformat()\n        }\n\n        with open(export_path, 'w', encoding='utf-8') as f:\n            json.dump(export_data, f, ensure_ascii=False, indent=2)\n\n        print(f\"📤 记忆数据已导出到: {export_path}\")\n\n# 示例使用和测试函数\ndef demo_code_agent():\n    \"\"\"演示 CodeAgent 的使用\"\"\"\n    print(\"🚀 CodeAgent 演示开始\\n\")\n\n    # 创建 CodeAgent 实例\n    agent = IntelligentCodeAgent()\n\n    # 显示状态\n    status = agent.get_status()\n    print(f\"📊 助手状态: {json.dumps(status, ensure_ascii=False, indent=2)}\\n\")\n\n    # 示例任务\n    demo_tasks = [\n        \"创建一个简单的 Python 函数来计算斐波那契数列\",\n        \"分析当前目录的文件结构\",\n        \"创建一个检查点\",\n        \"生成一个简单的 Web API 项目结构\"\n    ]\n\n    for i, task in enumerate(demo_tasks, 1):\n        print(f\"🎯 任务 {i}: {task}\")\n        try:\n            result = agent.run(task)\n            print(f\"✅ 结果: {result[:200]}...\\n\")\n        except Exception as e:\n            print(f\"❌ 错误: {e}\\n\")\n\n    print(\"🎉 演示完成！\")\n\nif __name__ == \"__main__\":\n    # 运行演示\n    demo_code_agent()\n", "hash": "af5a777e16bcd31d2932ad202a5984ce", "size": 41605}, "tools/__init__.py": {"content": "# 自定义工具模块\n", "hash": "54f60e536d64b6dc1c682e41c4436e10", "size": 24}, "tools/custom_tools.py": {"content": "\"\"\"\n自定义工具实现\n展示如何创建自己的工具来扩展 Agent 能力\n\"\"\"\n\nimport json\nimport time\nimport random\nfrom typing import Dict, List, Any\nfrom smolagents import Tool\n\nclass WeatherTool(Tool):\n    \"\"\"模拟天气查询工具\"\"\"\n    \n    name = \"weather_tool\"\n    description = \"获取指定城市的天气信息（模拟数据）\"\n    inputs = {\n        \"city\": {\n            \"type\": \"string\", \n            \"description\": \"要查询天气的城市名称\"\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, city: str) -> str:\n        \"\"\"模拟天气查询\"\"\"\n        # 模拟一些城市的天气数据\n        weather_data = {\n            \"北京\": {\"temperature\": 15, \"condition\": \"晴天\", \"humidity\": 45},\n            \"上海\": {\"temperature\": 18, \"condition\": \"多云\", \"humidity\": 60},\n            \"广州\": {\"temperature\": 25, \"condition\": \"小雨\", \"humidity\": 75},\n            \"深圳\": {\"temperature\": 26, \"condition\": \"晴天\", \"humidity\": 55},\n            \"杭州\": {\"temperature\": 20, \"condition\": \"阴天\", \"humidity\": 65},\n        }\n        \n        # 如果城市不在预设列表中，生成随机数据\n        if city not in weather_data:\n            weather_data[city] = {\n                \"temperature\": random.randint(10, 30),\n                \"condition\": random.choice([\"晴天\", \"多云\", \"阴天\", \"小雨\", \"大雨\"]),\n                \"humidity\": random.randint(30, 90)\n            }\n        \n        data = weather_data[city]\n        return f\"{city}的天气：温度 {data['temperature']}°C，{data['condition']}，湿度 {data['humidity']}%\"\n\nclass FileManagerTool(Tool):\n    \"\"\"文件管理工具\"\"\"\n    \n    name = \"file_manager\"\n    description = \"管理文件：创建、读取、写入文本文件\"\n    inputs = {\n        \"action\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：'create', 'read', 'write', 'list'\"\n        },\n        \"filename\": {\n            \"type\": \"string\",\n            \"description\": \"文件名（对于 list 操作可选）\",\n            \"nullable\": True\n        },\n        \"content\": {\n            \"type\": \"string\",\n            \"description\": \"文件内容（仅用于 write 操作）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, action: str, filename: str = \"\", content: str = \"\") -> str:\n        \"\"\"执行文件操作\"\"\"\n        try:\n            if action == \"create\":\n                with open(filename, 'w', encoding='utf-8') as f:\n                    f.write(content or \"\")\n                return f\"文件 {filename} 创建成功\"\n            \n            elif action == \"read\":\n                with open(filename, 'r', encoding='utf-8') as f:\n                    content = f.read()\n                return f\"文件 {filename} 内容：\\n{content}\"\n            \n            elif action == \"write\":\n                with open(filename, 'w', encoding='utf-8') as f:\n                    f.write(content)\n                return f\"内容已写入文件 {filename}\"\n            \n            elif action == \"list\":\n                import os\n                files = os.listdir('.')\n                return f\"当前目录文件：{', '.join(files)}\"\n            \n            else:\n                return f\"不支持的操作：{action}\"\n                \n        except Exception as e:\n            return f\"文件操作失败：{str(e)}\"\n\nclass DataAnalysisTool(Tool):\n    \"\"\"数据分析工具\"\"\"\n    \n    name = \"data_analysis\"\n    description = \"对数据进行基础统计分析\"\n    inputs = {\n        \"data\": {\n            \"type\": \"string\",\n            \"description\": \"要分析的数据，JSON 格式的数字列表\"\n        },\n        \"analysis_type\": {\n            \"type\": \"string\",\n            \"description\": \"分析类型：'basic_stats', 'distribution', 'correlation'\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, data: str, analysis_type: str = \"basic_stats\") -> str:\n        \"\"\"执行数据分析\"\"\"\n        try:\n            # 解析数据\n            numbers = json.loads(data)\n            if not isinstance(numbers, list) or not all(isinstance(x, (int, float)) for x in numbers):\n                return \"错误：数据必须是数字列表\"\n            \n            if analysis_type == \"basic_stats\":\n                mean = sum(numbers) / len(numbers)\n                sorted_nums = sorted(numbers)\n                n = len(sorted_nums)\n                median = sorted_nums[n//2] if n % 2 == 1 else (sorted_nums[n//2-1] + sorted_nums[n//2]) / 2\n                \n                variance = sum((x - mean) ** 2 for x in numbers) / len(numbers)\n                std_dev = variance ** 0.5\n                \n                return f\"\"\"基础统计分析结果：\n平均值: {mean:.2f}\n中位数: {median:.2f}\n标准差: {std_dev:.2f}\n最小值: {min(numbers)}\n最大值: {max(numbers)}\n数据点数: {len(numbers)}\"\"\"\n            \n            elif analysis_type == \"distribution\":\n                # 简单的分布分析\n                ranges = {}\n                min_val, max_val = min(numbers), max(numbers)\n                range_size = (max_val - min_val) / 5  # 分成5个区间\n                \n                for i in range(5):\n                    start = min_val + i * range_size\n                    end = min_val + (i + 1) * range_size\n                    count = sum(1 for x in numbers if start <= x < end)\n                    ranges[f\"{start:.1f}-{end:.1f}\"] = count\n                \n                result = \"数据分布分析：\\n\"\n                for range_name, count in ranges.items():\n                    result += f\"{range_name}: {count} 个数据点\\n\"\n                return result\n            \n            else:\n                return f\"不支持的分析类型：{analysis_type}\"\n                \n        except Exception as e:\n            return f\"数据分析失败：{str(e)}\"\n\nclass TimerTool(Tool):\n    \"\"\"计时器工具\"\"\"\n    \n    name = \"timer\"\n    description = \"计时器功能：开始计时、停止计时、获取当前时间\"\n    inputs = {\n        \"action\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：'start', 'stop', 'current', 'sleep'\"\n        },\n        \"seconds\": {\n            \"type\": \"number\",\n            \"description\": \"睡眠时间（仅用于 sleep 操作）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n    \n    def __init__(self):\n        super().__init__()\n        self.start_time = None\n    \n    def forward(self, action: str, seconds: float = 0) -> str:\n        \"\"\"执行计时操作\"\"\"\n        if action == \"start\":\n            self.start_time = time.time()\n            return f\"计时器已启动：{time.strftime('%Y-%m-%d %H:%M:%S')}\"\n        \n        elif action == \"stop\":\n            if self.start_time is None:\n                return \"计时器未启动\"\n            elapsed = time.time() - self.start_time\n            self.start_time = None\n            return f\"计时结束，耗时：{elapsed:.2f} 秒\"\n        \n        elif action == \"current\":\n            return f\"当前时间：{time.strftime('%Y-%m-%d %H:%M:%S')}\"\n        \n        elif action == \"sleep\":\n            time.sleep(seconds)\n            return f\"已等待 {seconds} 秒\"\n        \n        else:\n            return f\"不支持的操作：{action}\"\n", "hash": "817ef0fc10ffa81860a27bf9883effdb", "size": 7376}, "config/agent_config.py": {"content": "\"\"\"\nAgent 配置文件\n包含各种模型和工具的配置选项\n\"\"\"\n\nimport os\n\n# 模型配置\nMODEL_CONFIGS = {\n    \"huggingface\": {\n        \"model_id\": \"Qwen/Qwen2.5-Coder-32B-Instruct\",\n        \"token\": os.environ.get(\"HUGGINGFACE_TOKEN\"),\n    },\n    \"openai\": {\n        \"model_id\": \"gpt-4o\",\n        \"api_key\": os.environ.get(\"OPENAI_API_KEY\"),\n    },\n    \"anthropic\": {\n        \"model_id\": \"claude-3-5-sonnet-latest\",\n        \"api_key\": os.environ.get(\"ANTHROPIC_API_KEY\"),\n    }\n}\n\n# Agent 配置\nAGENT_CONFIG = {\n    \"max_iterations\": 10,\n    \"stream_outputs\": True,\n    \"verbose\": True,\n}\n\n# 工具配置\nTOOL_CONFIG = {\n    \"web_search\": {\n        \"enabled\": True,\n    },\n    \"python_interpreter\": {\n        \"enabled\": True,\n        \"secure\": True,\n    }\n}\n", "hash": "768e773cd355336df7f373fa4153a773", "size": 768}, ".history/codeAgent_20250720143502.py": {"content": "\"\"\"\nCodeAgent - 基于 smolagents 的智能代码编写助手\n集成 DeepSeek 模型，实现从自然语言到代码生成的全流程自动化\n\n主要功能：\n- 自然语言理解与代码生成\n- 文件操作与项目管理\n- 命令执行与环境管理\n- 任务分解与多工具协同\n- 记忆管理与检查点机制\n\"\"\"\n\nimport os\nimport json\nimport time\nimport hashlib\nimport subprocess\nfrom datetime import datetime\nfrom typing import Dict, List, Any, Optional\nfrom pathlib import Path\n\nfrom smolagents import CodeAgent, Tool, LiteLLMModel\nfrom smolagents import PythonInterpreterTool\n\nclass MemoryManager:\n    \"\"\"记忆管理系统 - 实现持久化记忆与检索\"\"\"\n    \n    def __init__(self, memory_dir: str = \".codeagent_memory\"):\n        self.memory_dir = Path(memory_dir)\n        self.memory_dir.mkdir(exist_ok=True)\n        \n        # 记忆文件路径\n        self.conversation_file = self.memory_dir / \"conversations.json\"\n        self.knowledge_file = self.memory_dir / \"knowledge_base.json\"\n        self.checkpoints_dir = self.memory_dir / \"checkpoints\"\n        self.checkpoints_dir.mkdir(exist_ok=True)\n        \n        # 初始化记忆存储\n        self._init_memory_files()\n    \n    def _init_memory_files(self):\n        \"\"\"初始化记忆文件\"\"\"\n        if not self.conversation_file.exists():\n            self._save_json(self.conversation_file, {\"conversations\": []})\n        \n        if not self.knowledge_file.exists():\n            self._save_json(self.knowledge_file, {\n                \"code_patterns\": {},\n                \"project_structures\": {},\n                \"user_preferences\": {},\n                \"learned_solutions\": {}\n            })\n    \n    def _save_json(self, file_path: Path, data: Dict):\n        \"\"\"保存 JSON 数据\"\"\"\n        with open(file_path, 'w', encoding='utf-8') as f:\n            json.dump(data, f, ensure_ascii=False, indent=2)\n    \n    def _load_json(self, file_path: Path) -> Dict:\n        \"\"\"加载 JSON 数据\"\"\"\n        try:\n            with open(file_path, 'r', encoding='utf-8') as f:\n                return json.load(f)\n        except (FileNotFoundError, json.JSONDecodeError):\n            return {}\n    \n    def store_conversation(self, user_input: str, agent_response: str, context: Dict = None):\n        \"\"\"存储对话记录\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        conversation_entry = {\n            \"timestamp\": datetime.now().isoformat(),\n            \"user_input\": user_input,\n            \"agent_response\": agent_response,\n            \"context\": context or {}\n        }\n        \n        conversations[\"conversations\"].append(conversation_entry)\n        \n        # 保持最近 100 条对话\n        if len(conversations[\"conversations\"]) > 100:\n            conversations[\"conversations\"] = conversations[\"conversations\"][-100:]\n        \n        self._save_json(self.conversation_file, conversations)\n    \n    def store_knowledge(self, category: str, key: str, value: Any):\n        \"\"\"存储知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            knowledge[category] = {}\n        \n        knowledge[category][key] = {\n            \"value\": value,\n            \"timestamp\": datetime.now().isoformat()\n        }\n        \n        self._save_json(self.knowledge_file, knowledge)\n    \n    def retrieve_knowledge(self, category: str, key: str = None) -> Any:\n        \"\"\"检索知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            return None\n        \n        if key is None:\n            return knowledge[category]\n        \n        return knowledge[category].get(key, {}).get(\"value\")\n    \n    def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:\n        \"\"\"搜索相关对话\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        relevant_conversations = []\n        query_lower = query.lower()\n        \n        for conv in conversations.get(\"conversations\", []):\n            if (query_lower in conv[\"user_input\"].lower() or \n                query_lower in conv[\"agent_response\"].lower()):\n                relevant_conversations.append(conv)\n        \n        return relevant_conversations[-limit:]\n\nclass CheckpointManager:\n    \"\"\"检查点管理系统 - 实现工作区状态管理\"\"\"\n    \n    def __init__(self, memory_manager: MemoryManager):\n        self.memory_manager = memory_manager\n        self.checkpoints_dir = memory_manager.checkpoints_dir\n    \n    def create_checkpoint(self, name: str, description: str = \"\") -> str:\n        \"\"\"创建检查点\"\"\"\n        checkpoint_id = f\"{name}_{int(time.time())}\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_dir.mkdir(exist_ok=True)\n        \n        # 保存当前工作目录状态\n        current_files = self._get_current_files()\n        \n        checkpoint_data = {\n            \"id\": checkpoint_id,\n            \"name\": name,\n            \"description\": description,\n            \"timestamp\": datetime.now().isoformat(),\n            \"files\": current_files,\n            \"working_directory\": str(Path.cwd())\n        }\n        \n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        self.memory_manager._save_json(checkpoint_file, checkpoint_data)\n        \n        # 复制重要文件\n        self._backup_files(checkpoint_dir, current_files)\n        \n        return checkpoint_id\n    \n    def _get_current_files(self) -> Dict[str, str]:\n        \"\"\"获取当前目录的文件信息\"\"\"\n        files_info = {}\n        current_dir = Path.cwd()\n        \n        for file_path in current_dir.rglob(\"*\"):\n            if file_path.is_file() and not self._should_ignore_file(file_path):\n                relative_path = file_path.relative_to(current_dir)\n                try:\n                    with open(file_path, 'r', encoding='utf-8') as f:\n                        content = f.read()\n                    files_info[str(relative_path)] = {\n                        \"content\": content,\n                        \"hash\": hashlib.md5(content.encode()).hexdigest(),\n                        \"size\": file_path.stat().st_size\n                    }\n                except (UnicodeDecodeError, PermissionError):\n                    # 跳过二进制文件或无权限文件\n                    files_info[str(relative_path)] = {\n                        \"content\": \"[BINARY_FILE]\",\n                        \"hash\": \"binary\",\n                        \"size\": file_path.stat().st_size\n                    }\n        \n        return files_info\n    \n    def _should_ignore_file(self, file_path: Path) -> bool:\n        \"\"\"判断是否应该忽略文件\"\"\"\n        ignore_patterns = [\n            \".git\", \"__pycache__\", \".pytest_cache\", \"node_modules\",\n            \".codeagent_memory\", \"*.pyc\", \"*.pyo\", \"*.pyd\", \".DS_Store\"\n        ]\n        \n        path_str = str(file_path)\n        for pattern in ignore_patterns:\n            if pattern in path_str:\n                return True\n        \n        return False\n    \n    def _backup_files(self, checkpoint_dir: Path, files_info: Dict):\n        \"\"\"备份文件到检查点目录\"\"\"\n        files_dir = checkpoint_dir / \"files\"\n        files_dir.mkdir(exist_ok=True)\n        \n        for file_path, info in files_info.items():\n            if info[\"content\"] != \"[BINARY_FILE]\":\n                backup_file = files_dir / file_path\n                backup_file.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(backup_file, 'w', encoding='utf-8') as f:\n                    f.write(info[\"content\"])\n    \n    def list_checkpoints(self) -> List[Dict]:\n        \"\"\"列出所有检查点\"\"\"\n        checkpoints = []\n        \n        for checkpoint_dir in self.checkpoints_dir.iterdir():\n            if checkpoint_dir.is_dir():\n                checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n                if checkpoint_file.exists():\n                    checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n                    checkpoints.append(checkpoint_data)\n        \n        return sorted(checkpoints, key=lambda x: x[\"timestamp\"], reverse=True)\n    \n    def restore_checkpoint(self, checkpoint_id: str) -> bool:\n        \"\"\"恢复检查点\"\"\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        \n        if not checkpoint_file.exists():\n            return False\n        \n        checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n        files_dir = checkpoint_dir / \"files\"\n        \n        if not files_dir.exists():\n            return False\n        \n        # 恢复文件\n        for file_path in files_dir.rglob(\"*\"):\n            if file_path.is_file():\n                relative_path = file_path.relative_to(files_dir)\n                target_path = Path.cwd() / relative_path\n                target_path.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(file_path, 'r', encoding='utf-8') as src:\n                    content = src.read()\n                \n                with open(target_path, 'w', encoding='utf-8') as dst:\n                    dst.write(content)\n        \n        return True\n\nclass CodeGenerationTool(Tool):\n    \"\"\"代码生成工具 - 基于自然语言生成代码\"\"\"\n\n    name = \"code_generation\"\n    description = \"根据自然语言描述生成代码，支持多种编程语言\"\n    inputs = {\n        \"description\": {\n            \"type\": \"string\",\n            \"description\": \"代码功能的自然语言描述\"\n        },\n        \"language\": {\n            \"type\": \"string\",\n            \"description\": \"编程语言（python, javascript, java, cpp 等）\",\n            \"nullable\": True\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"相关上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, description: str, language: str = \"python\", context: str = \"\") -> str:\n        \"\"\"生成代码\"\"\"\n        # 搜索相关历史经验\n        similar_conversations = self.memory_manager.search_conversations(description, limit=3)\n\n        # 获取用户偏好\n        user_preferences = self.memory_manager.retrieve_knowledge(\"user_preferences\") or {}\n\n        # 构建增强的提示\n        enhanced_prompt = f\"\"\"\n基于以下描述生成 {language} 代码：\n\n需求描述：{description}\n\n上下文信息：{context}\n\n用户偏好：{json.dumps(user_preferences, ensure_ascii=False, indent=2)}\n\n相关历史经验：\n{json.dumps(similar_conversations, ensure_ascii=False, indent=2)}\n\n请生成清晰、可读、符合最佳实践的代码，并包含必要的注释。\n\"\"\"\n\n        # 这里应该调用 LLM 生成代码，暂时返回模板\n        generated_code = f\"\"\"\n# 根据描述生成的 {language} 代码\n# 需求：{description}\n\n# TODO: 实际的代码生成逻辑\ndef generated_function():\n    '''\n    {description}\n    '''\n    pass\n\"\"\"\n\n        # 存储生成的代码模式\n        self.memory_manager.store_knowledge(\n            \"code_patterns\",\n            f\"{language}_{hashlib.md5(description.encode()).hexdigest()[:8]}\",\n            {\n                \"description\": description,\n                \"language\": language,\n                \"code\": generated_code,\n                \"context\": context\n            }\n        )\n\n        return generated_code\n\nclass FileOperationTool(Tool):\n    \"\"\"文件操作工具 - 处理文件读写、创建、删除等操作\"\"\"\n\n    name = \"file_operation\"\n    description = \"执行文件操作：创建、读取、写入、删除、移动文件\"\n    inputs = {\n        \"operation\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：create, read, write, delete, move, list\"\n        },\n        \"file_path\": {\n            \"type\": \"string\",\n            \"description\": \"文件路径\"\n        },\n        \"content\": {\n            \"type\": \"string\",\n            \"description\": \"文件内容（用于 write 操作）\",\n            \"nullable\": True\n        },\n        \"target_path\": {\n            \"type\": \"string\",\n            \"description\": \"目标路径（用于 move 操作）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, operation: str, file_path: str, content: str = \"\", target_path: str = \"\") -> str:\n        \"\"\"执行文件操作\"\"\"\n        try:\n            path = Path(file_path)\n\n            if operation == \"create\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n                path.touch()\n                return f\"文件 {file_path} 创建成功\"\n\n            elif operation == \"read\":\n                if not path.exists():\n                    return f\"错误：文件 {file_path} 不存在\"\n\n                with open(path, 'r', encoding='utf-8') as f:\n                    file_content = f.read()\n\n                # 存储文件访问记录\n                self.memory_manager.store_knowledge(\n                    \"file_access\",\n                    str(path),\n                    {\"last_read\": datetime.now().isoformat(), \"size\": len(file_content)}\n                )\n\n                return file_content\n\n            elif operation == \"write\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n\n                with open(path, 'w', encoding='utf-8') as f:\n                    f.write(content)\n\n                # 存储文件修改记录\n                self.memory_manager.store_knowledge(\n                    \"file_modifications\",\n                    str(path),\n                    {\n                        \"last_modified\": datetime.now().isoformat(),\n                        \"content_hash\": hashlib.md5(content.encode()).hexdigest()\n                    }\n                )\n\n                return f\"内容已写入文件 {file_path}\"\n\n            elif operation == \"delete\":\n                if path.exists():\n                    if path.is_file():\n                        path.unlink()\n                        return f\"文件 {file_path} 删除成功\"\n                    elif path.is_dir():\n                        import shutil\n                        shutil.rmtree(path)\n                        return f\"目录 {file_path} 删除成功\"\n                else:\n                    return f\"错误：路径 {file_path} 不存在\"\n\n            elif operation == \"move\":\n                if not path.exists():\n                    return f\"错误：源文件 {file_path} 不存在\"\n\n                target = Path(target_path)\n                target.parent.mkdir(parents=True, exist_ok=True)\n                path.rename(target)\n                return f\"文件从 {file_path} 移动到 {target_path}\"\n\n            elif operation == \"list\":\n                if path.is_dir():\n                    files = [str(p) for p in path.iterdir()]\n                    return f\"目录 {file_path} 内容：\\n\" + \"\\n\".join(files)\n                else:\n                    return f\"错误：{file_path} 不是目录\"\n\n            else:\n                return f\"不支持的操作：{operation}\"\n\n        except Exception as e:\n            error_msg = f\"文件操作失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"file_op_{int(time.time())}\",\n                {\n                    \"operation\": operation,\n                    \"file_path\": file_path,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\nclass CommandExecutionTool(Tool):\n    \"\"\"命令执行工具 - 安全执行系统命令\"\"\"\n\n    name = \"command_execution\"\n    description = \"执行系统命令，支持安全检查和结果记录\"\n    inputs = {\n        \"command\": {\n            \"type\": \"string\",\n            \"description\": \"要执行的命令\"\n        },\n        \"working_dir\": {\n            \"type\": \"string\",\n            \"description\": \"工作目录\",\n            \"nullable\": True\n        },\n        \"timeout\": {\n            \"type\": \"number\",\n            \"description\": \"超时时间（秒）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n        # 危险命令黑名单\n        self.dangerous_commands = [\n            \"rm -rf\", \"del /f\", \"format\", \"fdisk\", \"mkfs\",\n            \"shutdown\", \"reboot\", \"halt\", \"poweroff\",\n            \"chmod 777\", \"chown\", \"sudo rm\", \"dd if=\"\n        ]\n\n    def _is_safe_command(self, command: str) -> bool:\n        \"\"\"检查命令是否安全\"\"\"\n        command_lower = command.lower()\n\n        for dangerous in self.dangerous_commands:\n            if dangerous in command_lower:\n                return False\n\n        return True\n\n    def forward(self, command: str, working_dir: str = \"\", timeout: float = 30.0) -> str:\n        \"\"\"执行命令\"\"\"\n        # 安全检查\n        if not self._is_safe_command(command):\n            error_msg = f\"危险命令被阻止：{command}\"\n            self.memory_manager.store_knowledge(\n                \"security_blocks\",\n                f\"cmd_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"reason\": \"dangerous_command\",\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n            return error_msg\n\n        try:\n            # 设置工作目录\n            cwd = Path(working_dir) if working_dir else Path.cwd()\n\n            # 执行命令\n            result = subprocess.run(\n                command,\n                shell=True,\n                cwd=cwd,\n                capture_output=True,\n                text=True,\n                timeout=timeout\n            )\n\n            # 构建结果\n            output = f\"命令执行完成\\n\"\n            output += f\"返回码: {result.returncode}\\n\"\n\n            if result.stdout:\n                output += f\"标准输出:\\n{result.stdout}\\n\"\n\n            if result.stderr:\n                output += f\"标准错误:\\n{result.stderr}\\n\"\n\n            # 记录命令执行\n            self.memory_manager.store_knowledge(\n                \"command_history\",\n                f\"cmd_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"working_dir\": str(cwd),\n                    \"return_code\": result.returncode,\n                    \"stdout\": result.stdout,\n                    \"stderr\": result.stderr,\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return output\n\n        except subprocess.TimeoutExpired:\n            return f\"命令执行超时（{timeout}秒）：{command}\"\n\n        except Exception as e:\n            error_msg = f\"命令执行失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"cmd_error_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\nclass TaskDecompositionTool(Tool):\n    \"\"\"任务分解工具 - 将复杂任务分解为子任务\"\"\"\n\n    name = \"task_decomposition\"\n    description = \"将复杂任务分解为可执行的子任务序列\"\n    inputs = {\n        \"task_description\": {\n            \"type\": \"string\",\n            \"description\": \"复杂任务的描述\"\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"任务上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, task_description: str, context: str = \"\") -> str:\n        \"\"\"分解任务\"\"\"\n        # 搜索相似任务的历史分解\n        similar_tasks = self.memory_manager.search_conversations(task_description, limit=3)\n\n        # 获取已知的项目结构模式\n        project_patterns = self.memory_manager.retrieve_knowledge(\"project_structures\") or {}\n\n        # 基于规则的任务分解（简化版本）\n        subtasks = self._decompose_task(task_description, context)\n\n        # 格式化输出\n        result = f\"任务分解结果：{task_description}\\n\\n\"\n        result += \"子任务序列：\\n\"\n\n        for i, subtask in enumerate(subtasks, 1):\n            result += f\"{i}. {subtask['name']}\\n\"\n            result += f\"   描述: {subtask['description']}\\n\"\n            result += f\"   工具: {subtask['tools']}\\n\"\n            result += f\"   预估时间: {subtask['estimated_time']}\\n\\n\"\n\n        # 存储任务分解结果\n        self.memory_manager.store_knowledge(\n            \"task_decompositions\",\n            hashlib.md5(task_description.encode()).hexdigest()[:8],\n            {\n                \"original_task\": task_description,\n                \"context\": context,\n                \"subtasks\": subtasks,\n                \"timestamp\": datetime.now().isoformat()\n            }\n        )\n\n        return result\n\n    def _decompose_task(self, task_description: str, context: str) -> List[Dict]:\n        \"\"\"基于规则的任务分解\"\"\"\n        subtasks = []\n\n        # 分析任务类型\n        task_lower = task_description.lower()\n\n        if \"创建\" in task_lower or \"开发\" in task_lower:\n            if \"网站\" in task_lower or \"web\" in task_lower:\n                subtasks.extend(self._web_development_tasks())\n            elif \"api\" in task_lower:\n                subtasks.extend(self._api_development_tasks())\n            elif \"数据分析\" in task_lower:\n                subtasks.extend(self._data_analysis_tasks())\n            else:\n                subtasks.extend(self._general_development_tasks())\n\n        elif \"分析\" in task_lower:\n            subtasks.extend(self._analysis_tasks())\n\n        elif \"测试\" in task_lower:\n            subtasks.extend(self._testing_tasks())\n\n        else:\n            # 通用任务分解\n            subtasks.extend(self._general_tasks(task_description))\n\n        return subtasks\n\n    def _web_development_tasks(self) -> List[Dict]:\n        \"\"\"Web 开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"项目初始化\",\n                \"description\": \"创建项目目录结构和配置文件\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"前端开发\",\n                \"description\": \"创建HTML、CSS、JavaScript文件\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"后端开发\",\n                \"description\": \"实现服务器端逻辑\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"45分钟\"\n            },\n            {\n                \"name\": \"测试和部署\",\n                \"description\": \"运行测试并部署应用\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _api_development_tasks(self) -> List[Dict]:\n        \"\"\"API 开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"API设计\",\n                \"description\": \"设计API接口和数据模型\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"20分钟\"\n            },\n            {\n                \"name\": \"实现API端点\",\n                \"description\": \"编写API路由和处理函数\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"40分钟\"\n            },\n            {\n                \"name\": \"添加文档\",\n                \"description\": \"生成API文档\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _data_analysis_tasks(self) -> List[Dict]:\n        \"\"\"数据分析任务分解\"\"\"\n        return [\n            {\n                \"name\": \"数据收集\",\n                \"description\": \"获取和加载数据\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"数据清洗\",\n                \"description\": \"处理缺失值和异常数据\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"25分钟\"\n            },\n            {\n                \"name\": \"数据分析\",\n                \"description\": \"执行统计分析和可视化\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"生成报告\",\n                \"description\": \"创建分析报告\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"20分钟\"\n            }\n        ]\n\n    def _general_development_tasks(self) -> List[Dict]:\n        \"\"\"通用开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"需求分析\",\n                \"description\": \"分析和理解需求\",\n                \"tools\": [],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"代码实现\",\n                \"description\": \"编写核心功能代码\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"40分钟\"\n            },\n            {\n                \"name\": \"测试验证\",\n                \"description\": \"测试功能正确性\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _analysis_tasks(self) -> List[Dict]:\n        \"\"\"分析任务分解\"\"\"\n        return [\n            {\n                \"name\": \"数据收集\",\n                \"description\": \"收集相关数据和信息\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"数据处理\",\n                \"description\": \"清洗和预处理数据\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"20分钟\"\n            },\n            {\n                \"name\": \"分析计算\",\n                \"description\": \"执行分析算法\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"25分钟\"\n            },\n            {\n                \"name\": \"结果展示\",\n                \"description\": \"可视化和报告生成\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"20分钟\"\n            }\n        ]\n\n    def _testing_tasks(self) -> List[Dict]:\n        \"\"\"测试任务分解\"\"\"\n        return [\n            {\n                \"name\": \"测试计划\",\n                \"description\": \"制定测试策略和计划\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"编写测试\",\n                \"description\": \"创建测试用例和测试代码\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"执行测试\",\n                \"description\": \"运行测试并收集结果\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"测试报告\",\n                \"description\": \"生成测试报告\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            }\n        ]\n\n    def _general_tasks(self, task_description: str) -> List[Dict]:\n        \"\"\"通用任务分解\"\"\"\n        return [\n            {\n                \"name\": \"任务准备\",\n                \"description\": f\"准备执行任务：{task_description}\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"5分钟\"\n            },\n            {\n                \"name\": \"主要执行\",\n                \"description\": \"执行主要任务逻辑\",\n                \"tools\": [\"code_generation\", \"file_operation\", \"command_execution\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"结果整理\",\n                \"description\": \"整理和验证结果\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            }\n        ]\n", "hash": "e1d2d91bc0937df7c58653b190e67452", "size": 29144}, ".history/codeAgent_20250720143600.py": {"content": "\"\"\"\nCodeAgent - 基于 smolagents 的智能代码编写助手\n集成 DeepSeek 模型，实现从自然语言到代码生成的全流程自动化\n\n主要功能：\n- 自然语言理解与代码生成\n- 文件操作与项目管理\n- 命令执行与环境管理\n- 任务分解与多工具协同\n- 记忆管理与检查点机制\n\"\"\"\n\nimport os\nimport json\nimport time\nimport hashlib\nimport subprocess\nfrom datetime import datetime\nfrom typing import Dict, List, Any, Optional\nfrom pathlib import Path\n\nfrom smolagents import CodeAgent, Tool, LiteLLMModel\nfrom smolagents import PythonInterpreterTool\n\nclass MemoryManager:\n    \"\"\"记忆管理系统 - 实现持久化记忆与检索\"\"\"\n    \n    def __init__(self, memory_dir: str = \".codeagent_memory\"):\n        self.memory_dir = Path(memory_dir)\n        self.memory_dir.mkdir(exist_ok=True)\n        \n        # 记忆文件路径\n        self.conversation_file = self.memory_dir / \"conversations.json\"\n        self.knowledge_file = self.memory_dir / \"knowledge_base.json\"\n        self.checkpoints_dir = self.memory_dir / \"checkpoints\"\n        self.checkpoints_dir.mkdir(exist_ok=True)\n        \n        # 初始化记忆存储\n        self._init_memory_files()\n    \n    def _init_memory_files(self):\n        \"\"\"初始化记忆文件\"\"\"\n        if not self.conversation_file.exists():\n            self._save_json(self.conversation_file, {\"conversations\": []})\n        \n        if not self.knowledge_file.exists():\n            self._save_json(self.knowledge_file, {\n                \"code_patterns\": {},\n                \"project_structures\": {},\n                \"user_preferences\": {},\n                \"learned_solutions\": {}\n            })\n    \n    def _save_json(self, file_path: Path, data: Dict):\n        \"\"\"保存 JSON 数据\"\"\"\n        with open(file_path, 'w', encoding='utf-8') as f:\n            json.dump(data, f, ensure_ascii=False, indent=2)\n    \n    def _load_json(self, file_path: Path) -> Dict:\n        \"\"\"加载 JSON 数据\"\"\"\n        try:\n            with open(file_path, 'r', encoding='utf-8') as f:\n                return json.load(f)\n        except (FileNotFoundError, json.JSONDecodeError):\n            return {}\n    \n    def store_conversation(self, user_input: str, agent_response: str, context: Dict = None):\n        \"\"\"存储对话记录\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        conversation_entry = {\n            \"timestamp\": datetime.now().isoformat(),\n            \"user_input\": user_input,\n            \"agent_response\": agent_response,\n            \"context\": context or {}\n        }\n        \n        conversations[\"conversations\"].append(conversation_entry)\n        \n        # 保持最近 100 条对话\n        if len(conversations[\"conversations\"]) > 100:\n            conversations[\"conversations\"] = conversations[\"conversations\"][-100:]\n        \n        self._save_json(self.conversation_file, conversations)\n    \n    def store_knowledge(self, category: str, key: str, value: Any):\n        \"\"\"存储知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            knowledge[category] = {}\n        \n        knowledge[category][key] = {\n            \"value\": value,\n            \"timestamp\": datetime.now().isoformat()\n        }\n        \n        self._save_json(self.knowledge_file, knowledge)\n    \n    def retrieve_knowledge(self, category: str, key: str = None) -> Any:\n        \"\"\"检索知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            return None\n        \n        if key is None:\n            return knowledge[category]\n        \n        return knowledge[category].get(key, {}).get(\"value\")\n    \n    def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:\n        \"\"\"搜索相关对话\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        relevant_conversations = []\n        query_lower = query.lower()\n        \n        for conv in conversations.get(\"conversations\", []):\n            if (query_lower in conv[\"user_input\"].lower() or \n                query_lower in conv[\"agent_response\"].lower()):\n                relevant_conversations.append(conv)\n        \n        return relevant_conversations[-limit:]\n\nclass CheckpointManager:\n    \"\"\"检查点管理系统 - 实现工作区状态管理\"\"\"\n    \n    def __init__(self, memory_manager: MemoryManager):\n        self.memory_manager = memory_manager\n        self.checkpoints_dir = memory_manager.checkpoints_dir\n    \n    def create_checkpoint(self, name: str, description: str = \"\") -> str:\n        \"\"\"创建检查点\"\"\"\n        checkpoint_id = f\"{name}_{int(time.time())}\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_dir.mkdir(exist_ok=True)\n        \n        # 保存当前工作目录状态\n        current_files = self._get_current_files()\n        \n        checkpoint_data = {\n            \"id\": checkpoint_id,\n            \"name\": name,\n            \"description\": description,\n            \"timestamp\": datetime.now().isoformat(),\n            \"files\": current_files,\n            \"working_directory\": str(Path.cwd())\n        }\n        \n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        self.memory_manager._save_json(checkpoint_file, checkpoint_data)\n        \n        # 复制重要文件\n        self._backup_files(checkpoint_dir, current_files)\n        \n        return checkpoint_id\n    \n    def _get_current_files(self) -> Dict[str, str]:\n        \"\"\"获取当前目录的文件信息\"\"\"\n        files_info = {}\n        current_dir = Path.cwd()\n        \n        for file_path in current_dir.rglob(\"*\"):\n            if file_path.is_file() and not self._should_ignore_file(file_path):\n                relative_path = file_path.relative_to(current_dir)\n                try:\n                    with open(file_path, 'r', encoding='utf-8') as f:\n                        content = f.read()\n                    files_info[str(relative_path)] = {\n                        \"content\": content,\n                        \"hash\": hashlib.md5(content.encode()).hexdigest(),\n                        \"size\": file_path.stat().st_size\n                    }\n                except (UnicodeDecodeError, PermissionError):\n                    # 跳过二进制文件或无权限文件\n                    files_info[str(relative_path)] = {\n                        \"content\": \"[BINARY_FILE]\",\n                        \"hash\": \"binary\",\n                        \"size\": file_path.stat().st_size\n                    }\n        \n        return files_info\n    \n    def _should_ignore_file(self, file_path: Path) -> bool:\n        \"\"\"判断是否应该忽略文件\"\"\"\n        ignore_patterns = [\n            \".git\", \"__pycache__\", \".pytest_cache\", \"node_modules\",\n            \".codeagent_memory\", \"*.pyc\", \"*.pyo\", \"*.pyd\", \".DS_Store\"\n        ]\n        \n        path_str = str(file_path)\n        for pattern in ignore_patterns:\n            if pattern in path_str:\n                return True\n        \n        return False\n    \n    def _backup_files(self, checkpoint_dir: Path, files_info: Dict):\n        \"\"\"备份文件到检查点目录\"\"\"\n        files_dir = checkpoint_dir / \"files\"\n        files_dir.mkdir(exist_ok=True)\n        \n        for file_path, info in files_info.items():\n            if info[\"content\"] != \"[BINARY_FILE]\":\n                backup_file = files_dir / file_path\n                backup_file.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(backup_file, 'w', encoding='utf-8') as f:\n                    f.write(info[\"content\"])\n    \n    def list_checkpoints(self) -> List[Dict]:\n        \"\"\"列出所有检查点\"\"\"\n        checkpoints = []\n        \n        for checkpoint_dir in self.checkpoints_dir.iterdir():\n            if checkpoint_dir.is_dir():\n                checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n                if checkpoint_file.exists():\n                    checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n                    checkpoints.append(checkpoint_data)\n        \n        return sorted(checkpoints, key=lambda x: x[\"timestamp\"], reverse=True)\n    \n    def restore_checkpoint(self, checkpoint_id: str) -> bool:\n        \"\"\"恢复检查点\"\"\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        \n        if not checkpoint_file.exists():\n            return False\n        \n        checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n        files_dir = checkpoint_dir / \"files\"\n        \n        if not files_dir.exists():\n            return False\n        \n        # 恢复文件\n        for file_path in files_dir.rglob(\"*\"):\n            if file_path.is_file():\n                relative_path = file_path.relative_to(files_dir)\n                target_path = Path.cwd() / relative_path\n                target_path.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(file_path, 'r', encoding='utf-8') as src:\n                    content = src.read()\n                \n                with open(target_path, 'w', encoding='utf-8') as dst:\n                    dst.write(content)\n        \n        return True\n\nclass CodeGenerationTool(Tool):\n    \"\"\"代码生成工具 - 基于自然语言生成代码\"\"\"\n\n    name = \"code_generation\"\n    description = \"根据自然语言描述生成代码，支持多种编程语言\"\n    inputs = {\n        \"description\": {\n            \"type\": \"string\",\n            \"description\": \"代码功能的自然语言描述\"\n        },\n        \"language\": {\n            \"type\": \"string\",\n            \"description\": \"编程语言（python, javascript, java, cpp 等）\",\n            \"nullable\": True\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"相关上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, description: str, language: str = \"python\", context: str = \"\") -> str:\n        \"\"\"生成代码\"\"\"\n        # 搜索相关历史经验\n        similar_conversations = self.memory_manager.search_conversations(description, limit=3)\n\n        # 获取用户偏好\n        user_preferences = self.memory_manager.retrieve_knowledge(\"user_preferences\") or {}\n\n        # 构建增强的提示\n        enhanced_prompt = f\"\"\"\n基于以下描述生成 {language} 代码：\n\n需求描述：{description}\n\n上下文信息：{context}\n\n用户偏好：{json.dumps(user_preferences, ensure_ascii=False, indent=2)}\n\n相关历史经验：\n{json.dumps(similar_conversations, ensure_ascii=False, indent=2)}\n\n请生成清晰、可读、符合最佳实践的代码，并包含必要的注释。\n\"\"\"\n\n        # 这里应该调用 LLM 生成代码，暂时返回模板\n        generated_code = f\"\"\"\n# 根据描述生成的 {language} 代码\n# 需求：{description}\n\n# TODO: 实际的代码生成逻辑\ndef generated_function():\n    '''\n    {description}\n    '''\n    pass\n\"\"\"\n\n        # 存储生成的代码模式\n        self.memory_manager.store_knowledge(\n            \"code_patterns\",\n            f\"{language}_{hashlib.md5(description.encode()).hexdigest()[:8]}\",\n            {\n                \"description\": description,\n                \"language\": language,\n                \"code\": generated_code,\n                \"context\": context\n            }\n        )\n\n        return generated_code\n\nclass FileOperationTool(Tool):\n    \"\"\"文件操作工具 - 处理文件读写、创建、删除等操作\"\"\"\n\n    name = \"file_operation\"\n    description = \"执行文件操作：创建、读取、写入、删除、移动文件\"\n    inputs = {\n        \"operation\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：create, read, write, delete, move, list\"\n        },\n        \"file_path\": {\n            \"type\": \"string\",\n            \"description\": \"文件路径\"\n        },\n        \"content\": {\n            \"type\": \"string\",\n            \"description\": \"文件内容（用于 write 操作）\",\n            \"nullable\": True\n        },\n        \"target_path\": {\n            \"type\": \"string\",\n            \"description\": \"目标路径（用于 move 操作）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, operation: str, file_path: str, content: str = \"\", target_path: str = \"\") -> str:\n        \"\"\"执行文件操作\"\"\"\n        try:\n            path = Path(file_path)\n\n            if operation == \"create\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n                path.touch()\n                return f\"文件 {file_path} 创建成功\"\n\n            elif operation == \"read\":\n                if not path.exists():\n                    return f\"错误：文件 {file_path} 不存在\"\n\n                with open(path, 'r', encoding='utf-8') as f:\n                    file_content = f.read()\n\n                # 存储文件访问记录\n                self.memory_manager.store_knowledge(\n                    \"file_access\",\n                    str(path),\n                    {\"last_read\": datetime.now().isoformat(), \"size\": len(file_content)}\n                )\n\n                return file_content\n\n            elif operation == \"write\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n\n                with open(path, 'w', encoding='utf-8') as f:\n                    f.write(content)\n\n                # 存储文件修改记录\n                self.memory_manager.store_knowledge(\n                    \"file_modifications\",\n                    str(path),\n                    {\n                        \"last_modified\": datetime.now().isoformat(),\n                        \"content_hash\": hashlib.md5(content.encode()).hexdigest()\n                    }\n                )\n\n                return f\"内容已写入文件 {file_path}\"\n\n            elif operation == \"delete\":\n                if path.exists():\n                    if path.is_file():\n                        path.unlink()\n                        return f\"文件 {file_path} 删除成功\"\n                    elif path.is_dir():\n                        import shutil\n                        shutil.rmtree(path)\n                        return f\"目录 {file_path} 删除成功\"\n                else:\n                    return f\"错误：路径 {file_path} 不存在\"\n\n            elif operation == \"move\":\n                if not path.exists():\n                    return f\"错误：源文件 {file_path} 不存在\"\n\n                target = Path(target_path)\n                target.parent.mkdir(parents=True, exist_ok=True)\n                path.rename(target)\n                return f\"文件从 {file_path} 移动到 {target_path}\"\n\n            elif operation == \"list\":\n                if path.is_dir():\n                    files = [str(p) for p in path.iterdir()]\n                    return f\"目录 {file_path} 内容：\\n\" + \"\\n\".join(files)\n                else:\n                    return f\"错误：{file_path} 不是目录\"\n\n            else:\n                return f\"不支持的操作：{operation}\"\n\n        except Exception as e:\n            error_msg = f\"文件操作失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"file_op_{int(time.time())}\",\n                {\n                    \"operation\": operation,\n                    \"file_path\": file_path,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\nclass CommandExecutionTool(Tool):\n    \"\"\"命令执行工具 - 安全执行系统命令\"\"\"\n\n    name = \"command_execution\"\n    description = \"执行系统命令，支持安全检查和结果记录\"\n    inputs = {\n        \"command\": {\n            \"type\": \"string\",\n            \"description\": \"要执行的命令\"\n        },\n        \"working_dir\": {\n            \"type\": \"string\",\n            \"description\": \"工作目录\",\n            \"nullable\": True\n        },\n        \"timeout\": {\n            \"type\": \"number\",\n            \"description\": \"超时时间（秒）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n        # 危险命令黑名单\n        self.dangerous_commands = [\n            \"rm -rf\", \"del /f\", \"format\", \"fdisk\", \"mkfs\",\n            \"shutdown\", \"reboot\", \"halt\", \"poweroff\",\n            \"chmod 777\", \"chown\", \"sudo rm\", \"dd if=\"\n        ]\n\n    def _is_safe_command(self, command: str) -> bool:\n        \"\"\"检查命令是否安全\"\"\"\n        command_lower = command.lower()\n\n        for dangerous in self.dangerous_commands:\n            if dangerous in command_lower:\n                return False\n\n        return True\n\n    def forward(self, command: str, working_dir: str = \"\", timeout: float = 30.0) -> str:\n        \"\"\"执行命令\"\"\"\n        # 安全检查\n        if not self._is_safe_command(command):\n            error_msg = f\"危险命令被阻止：{command}\"\n            self.memory_manager.store_knowledge(\n                \"security_blocks\",\n                f\"cmd_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"reason\": \"dangerous_command\",\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n            return error_msg\n\n        try:\n            # 设置工作目录\n            cwd = Path(working_dir) if working_dir else Path.cwd()\n\n            # 执行命令\n            result = subprocess.run(\n                command,\n                shell=True,\n                cwd=cwd,\n                capture_output=True,\n                text=True,\n                timeout=timeout\n            )\n\n            # 构建结果\n            output = f\"命令执行完成\\n\"\n            output += f\"返回码: {result.returncode}\\n\"\n\n            if result.stdout:\n                output += f\"标准输出:\\n{result.stdout}\\n\"\n\n            if result.stderr:\n                output += f\"标准错误:\\n{result.stderr}\\n\"\n\n            # 记录命令执行\n            self.memory_manager.store_knowledge(\n                \"command_history\",\n                f\"cmd_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"working_dir\": str(cwd),\n                    \"return_code\": result.returncode,\n                    \"stdout\": result.stdout,\n                    \"stderr\": result.stderr,\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return output\n\n        except subprocess.TimeoutExpired:\n            return f\"命令执行超时（{timeout}秒）：{command}\"\n\n        except Exception as e:\n            error_msg = f\"命令执行失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"cmd_error_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\nclass TaskDecompositionTool(Tool):\n    \"\"\"任务分解工具 - 将复杂任务分解为子任务\"\"\"\n\n    name = \"task_decomposition\"\n    description = \"将复杂任务分解为可执行的子任务序列\"\n    inputs = {\n        \"task_description\": {\n            \"type\": \"string\",\n            \"description\": \"复杂任务的描述\"\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"任务上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, task_description: str, context: str = \"\") -> str:\n        \"\"\"分解任务\"\"\"\n        # 搜索相似任务的历史分解\n        similar_tasks = self.memory_manager.search_conversations(task_description, limit=3)\n\n        # 获取已知的项目结构模式\n        project_patterns = self.memory_manager.retrieve_knowledge(\"project_structures\") or {}\n\n        # 基于规则的任务分解（简化版本）\n        subtasks = self._decompose_task(task_description, context)\n\n        # 格式化输出\n        result = f\"任务分解结果：{task_description}\\n\\n\"\n        result += \"子任务序列：\\n\"\n\n        for i, subtask in enumerate(subtasks, 1):\n            result += f\"{i}. {subtask['name']}\\n\"\n            result += f\"   描述: {subtask['description']}\\n\"\n            result += f\"   工具: {subtask['tools']}\\n\"\n            result += f\"   预估时间: {subtask['estimated_time']}\\n\\n\"\n\n        # 存储任务分解结果\n        self.memory_manager.store_knowledge(\n            \"task_decompositions\",\n            hashlib.md5(task_description.encode()).hexdigest()[:8],\n            {\n                \"original_task\": task_description,\n                \"context\": context,\n                \"subtasks\": subtasks,\n                \"timestamp\": datetime.now().isoformat()\n            }\n        )\n\n        return result\n\n    def _decompose_task(self, task_description: str, context: str) -> List[Dict]:\n        \"\"\"基于规则的任务分解\"\"\"\n        subtasks = []\n\n        # 分析任务类型\n        task_lower = task_description.lower()\n\n        if \"创建\" in task_lower or \"开发\" in task_lower:\n            if \"网站\" in task_lower or \"web\" in task_lower:\n                subtasks.extend(self._web_development_tasks())\n            elif \"api\" in task_lower:\n                subtasks.extend(self._api_development_tasks())\n            elif \"数据分析\" in task_lower:\n                subtasks.extend(self._data_analysis_tasks())\n            else:\n                subtasks.extend(self._general_development_tasks())\n\n        elif \"分析\" in task_lower:\n            subtasks.extend(self._analysis_tasks())\n\n        elif \"测试\" in task_lower:\n            subtasks.extend(self._testing_tasks())\n\n        else:\n            # 通用任务分解\n            subtasks.extend(self._general_tasks(task_description))\n\n        return subtasks\n\n    def _web_development_tasks(self) -> List[Dict]:\n        \"\"\"Web 开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"项目初始化\",\n                \"description\": \"创建项目目录结构和配置文件\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"前端开发\",\n                \"description\": \"创建HTML、CSS、JavaScript文件\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"后端开发\",\n                \"description\": \"实现服务器端逻辑\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"45分钟\"\n            },\n            {\n                \"name\": \"测试和部署\",\n                \"description\": \"运行测试并部署应用\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _api_development_tasks(self) -> List[Dict]:\n        \"\"\"API 开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"API设计\",\n                \"description\": \"设计API接口和数据模型\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"20分钟\"\n            },\n            {\n                \"name\": \"实现API端点\",\n                \"description\": \"编写API路由和处理函数\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"40分钟\"\n            },\n            {\n                \"name\": \"添加文档\",\n                \"description\": \"生成API文档\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _data_analysis_tasks(self) -> List[Dict]:\n        \"\"\"数据分析任务分解\"\"\"\n        return [\n            {\n                \"name\": \"数据收集\",\n                \"description\": \"获取和加载数据\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"数据清洗\",\n                \"description\": \"处理缺失值和异常数据\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"25分钟\"\n            },\n            {\n                \"name\": \"数据分析\",\n                \"description\": \"执行统计分析和可视化\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"生成报告\",\n                \"description\": \"创建分析报告\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"20分钟\"\n            }\n        ]\n\n    def _general_development_tasks(self) -> List[Dict]:\n        \"\"\"通用开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"需求分析\",\n                \"description\": \"分析和理解需求\",\n                \"tools\": [],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"代码实现\",\n                \"description\": \"编写核心功能代码\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"40分钟\"\n            },\n            {\n                \"name\": \"测试验证\",\n                \"description\": \"测试功能正确性\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _analysis_tasks(self) -> List[Dict]:\n        \"\"\"分析任务分解\"\"\"\n        return [\n            {\n                \"name\": \"数据收集\",\n                \"description\": \"收集相关数据和信息\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"数据处理\",\n                \"description\": \"清洗和预处理数据\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"20分钟\"\n            },\n            {\n                \"name\": \"分析计算\",\n                \"description\": \"执行分析算法\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"25分钟\"\n            },\n            {\n                \"name\": \"结果展示\",\n                \"description\": \"可视化和报告生成\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"20分钟\"\n            }\n        ]\n\n    def _testing_tasks(self) -> List[Dict]:\n        \"\"\"测试任务分解\"\"\"\n        return [\n            {\n                \"name\": \"测试计划\",\n                \"description\": \"制定测试策略和计划\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"编写测试\",\n                \"description\": \"创建测试用例和测试代码\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"执行测试\",\n                \"description\": \"运行测试并收集结果\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"测试报告\",\n                \"description\": \"生成测试报告\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            }\n        ]\n\n    def _general_tasks(self, task_description: str) -> List[Dict]:\n        \"\"\"通用任务分解\"\"\"\n        return [\n            {\n                \"name\": \"任务准备\",\n                \"description\": f\"准备执行任务：{task_description}\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"5分钟\"\n            },\n            {\n                \"name\": \"主要执行\",\n                \"description\": \"执行主要任务逻辑\",\n                \"tools\": [\"code_generation\", \"file_operation\", \"command_execution\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"结果整理\",\n                \"description\": \"整理和验证结果\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            }\n        ]\n\nclass CheckpointTool(Tool):\n    \"\"\"检查点管理工具 - 管理工作区状态检查点\"\"\"\n\n    name = \"checkpoint_management\"\n    description = \"创建、列出、恢复工作区检查点\"\n    inputs = {\n        \"operation\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：create, list, restore\"\n        },\n        \"checkpoint_name\": {\n            \"type\": \"string\",\n            \"description\": \"检查点名称\",\n            \"nullable\": True\n        },\n        \"description\": {\n            \"type\": \"string\",\n            \"description\": \"检查点描述\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, checkpoint_manager: CheckpointManager):\n        super().__init__()\n        self.checkpoint_manager = checkpoint_manager\n\n    def forward(self, operation: str, checkpoint_name: str = \"\", description: str = \"\") -> str:\n        \"\"\"执行检查点操作\"\"\"\n        try:\n            if operation == \"create\":\n                if not checkpoint_name:\n                    checkpoint_name = f\"auto_{int(time.time())}\"\n\n                checkpoint_id = self.checkpoint_manager.create_checkpoint(\n                    checkpoint_name, description\n                )\n                return f\"检查点创建成功：{checkpoint_id}\"\n\n            elif operation == \"list\":\n                checkpoints = self.checkpoint_manager.list_checkpoints()\n\n                if not checkpoints:\n                    return \"没有找到检查点\"\n\n                result = \"可用检查点：\\n\"\n                for cp in checkpoints:\n                    result += f\"- {cp['id']}: {cp['name']}\\n\"\n                    result += f\"  描述: {cp['description']}\\n\"\n                    result += f\"  时间: {cp['timestamp']}\\n\\n\"\n\n                return result\n\n            elif operation == \"restore\":\n                if not checkpoint_name:\n                    return \"错误：需要指定检查点名称\"\n\n                success = self.checkpoint_manager.restore_checkpoint(checkpoint_name)\n\n                if success:\n                    return f\"检查点恢复成功：{checkpoint_name}\"\n                else:\n                    return f\"检查点恢复失败：{checkpoint_name}\"\n\n            else:\n                return f\"不支持的操作：{operation}\"\n\n        except Exception as e:\n            return f\"检查点操作失败：{str(e)}\"\n", "hash": "0e351810eb40869cdcba0f25ec3bcce9", "size": 31548}, ".history/learn_20250720132146.py": {"content": "\"\"\"\nsmolagents 学习项目\n基于 Hugging Face smolagents 库的 Agent 开发示例\n\n这是一个完整的 smolagents 学习项目，包含：\n- 基础 Agent 使用\n- 自定义工具开发\n- 多 Agent 协作\n- 安全执行环境配置\n\"\"\"\n\nimport os\nimport sys\n\ndef main():\n    \"\"\"主函数 - 展示项目概览\"\"\"\n    print(\"🤖 欢迎来到 smolagents 学习项目！\\n\")\n\n    print(\"📚 项目内容:\")\n    print(\"1. 基础 Agent 示例 - examples/basic_agent.py\")\n    print(\"2. 自定义工具开发 - examples/custom_tools.py\")\n    print(\"3. 多 Agent 协作 - examples/multi_agent.py\")\n    print(\"4. 安全执行环境 - examples/secure_execution.py\")\n    print(\"5. 简单演示 - simple_demo.py\")\n\n    print(\"\\n🛠️ 自定义工具:\")\n    print(\"- WeatherTool: 天气查询工具\")\n    print(\"- DataAnalysisTool: 数据分析工具\")\n    print(\"- TimerTool: 计时器工具\")\n    print(\"- FileManagerTool: 文件管理工具\")\n\n    print(\"\\n🚀 快速开始:\")\n    print(\"1. 运行简单演示: python simple_demo.py\")\n    print(\"2. 测试自定义工具: python examples/custom_tools.py\")\n    print(\"3. 体验多 Agent 协作: python examples/multi_agent.py\")\n\n    print(\"\\n💡 提示:\")\n    print(\"- 设置 HUGGINGFACE_TOKEN 环境变量以使用外部模型\")\n    print(\"- 查看 README.md 了解详细说明\")\n    print(\"- 参考 config/agent_config.py 进行配置\")\n\ndef show_smolagents_overview():\n    \"\"\"展示 smolagents 概览\"\"\"\n    print(\"\\n🏗️ smolagents 架构:\")\n    print(\"Agent (智能代理) + Model (语言模型) + Tools (工具) + Executor (执行器)\")\n\n    print(\"\\n🔥 核心特性:\")\n    print(\"✨ 代码优先的 Agent 设计\")\n    print(\"🧑‍💻 支持多种语言模型\")\n    print(\"🛠️ 丰富的工具生态\")\n    print(\"🔒 安全的执行环境\")\n    print(\"🤗 Hub 集成\")\n\nif __name__ == \"__main__\":\n    main()\n    show_smolagents_overview()", "hash": "f2cb866e1986ec922245ea2df1026914", "size": 1906}, ".history/codeAgent_20250720143223.py": {"content": "\"\"\"\nCodeAgent - 基于 smolagents 的智能代码编写助手\n集成 DeepSeek 模型，实现从自然语言到代码生成的全流程自动化\n\n主要功能：\n- 自然语言理解与代码生成\n- 文件操作与项目管理\n- 命令执行与环境管理\n- 任务分解与多工具协同\n- 记忆管理与检查点机制\n\"\"\"\n\nimport os\nimport json\nimport time\nimport hashlib\nimport subprocess\nfrom datetime import datetime\nfrom typing import Dict, List, Any, Optional\nfrom pathlib import Path\n\nfrom smolagents import CodeAgent, Tool, LiteLLMModel\nfrom smolagents import PythonInterpreterTool\n\nclass MemoryManager:\n    \"\"\"记忆管理系统 - 实现持久化记忆与检索\"\"\"\n    \n    def __init__(self, memory_dir: str = \".codeagent_memory\"):\n        self.memory_dir = Path(memory_dir)\n        self.memory_dir.mkdir(exist_ok=True)\n        \n        # 记忆文件路径\n        self.conversation_file = self.memory_dir / \"conversations.json\"\n        self.knowledge_file = self.memory_dir / \"knowledge_base.json\"\n        self.checkpoints_dir = self.memory_dir / \"checkpoints\"\n        self.checkpoints_dir.mkdir(exist_ok=True)\n        \n        # 初始化记忆存储\n        self._init_memory_files()\n    \n    def _init_memory_files(self):\n        \"\"\"初始化记忆文件\"\"\"\n        if not self.conversation_file.exists():\n            self._save_json(self.conversation_file, {\"conversations\": []})\n        \n        if not self.knowledge_file.exists():\n            self._save_json(self.knowledge_file, {\n                \"code_patterns\": {},\n                \"project_structures\": {},\n                \"user_preferences\": {},\n                \"learned_solutions\": {}\n            })\n    \n    def _save_json(self, file_path: Path, data: Dict):\n        \"\"\"保存 JSON 数据\"\"\"\n        with open(file_path, 'w', encoding='utf-8') as f:\n            json.dump(data, f, ensure_ascii=False, indent=2)\n    \n    def _load_json(self, file_path: Path) -> Dict:\n        \"\"\"加载 JSON 数据\"\"\"\n        try:\n            with open(file_path, 'r', encoding='utf-8') as f:\n                return json.load(f)\n        except (FileNotFoundError, json.JSONDecodeError):\n            return {}\n    \n    def store_conversation(self, user_input: str, agent_response: str, context: Dict = None):\n        \"\"\"存储对话记录\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        conversation_entry = {\n            \"timestamp\": datetime.now().isoformat(),\n            \"user_input\": user_input,\n            \"agent_response\": agent_response,\n            \"context\": context or {}\n        }\n        \n        conversations[\"conversations\"].append(conversation_entry)\n        \n        # 保持最近 100 条对话\n        if len(conversations[\"conversations\"]) > 100:\n            conversations[\"conversations\"] = conversations[\"conversations\"][-100:]\n        \n        self._save_json(self.conversation_file, conversations)\n    \n    def store_knowledge(self, category: str, key: str, value: Any):\n        \"\"\"存储知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            knowledge[category] = {}\n        \n        knowledge[category][key] = {\n            \"value\": value,\n            \"timestamp\": datetime.now().isoformat()\n        }\n        \n        self._save_json(self.knowledge_file, knowledge)\n    \n    def retrieve_knowledge(self, category: str, key: str = None) -> Any:\n        \"\"\"检索知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            return None\n        \n        if key is None:\n            return knowledge[category]\n        \n        return knowledge[category].get(key, {}).get(\"value\")\n    \n    def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:\n        \"\"\"搜索相关对话\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        relevant_conversations = []\n        query_lower = query.lower()\n        \n        for conv in conversations.get(\"conversations\", []):\n            if (query_lower in conv[\"user_input\"].lower() or \n                query_lower in conv[\"agent_response\"].lower()):\n                relevant_conversations.append(conv)\n        \n        return relevant_conversations[-limit:]\n\nclass CheckpointManager:\n    \"\"\"检查点管理系统 - 实现工作区状态管理\"\"\"\n    \n    def __init__(self, memory_manager: MemoryManager):\n        self.memory_manager = memory_manager\n        self.checkpoints_dir = memory_manager.checkpoints_dir\n    \n    def create_checkpoint(self, name: str, description: str = \"\") -> str:\n        \"\"\"创建检查点\"\"\"\n        checkpoint_id = f\"{name}_{int(time.time())}\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_dir.mkdir(exist_ok=True)\n        \n        # 保存当前工作目录状态\n        current_files = self._get_current_files()\n        \n        checkpoint_data = {\n            \"id\": checkpoint_id,\n            \"name\": name,\n            \"description\": description,\n            \"timestamp\": datetime.now().isoformat(),\n            \"files\": current_files,\n            \"working_directory\": str(Path.cwd())\n        }\n        \n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        self.memory_manager._save_json(checkpoint_file, checkpoint_data)\n        \n        # 复制重要文件\n        self._backup_files(checkpoint_dir, current_files)\n        \n        return checkpoint_id\n    \n    def _get_current_files(self) -> Dict[str, str]:\n        \"\"\"获取当前目录的文件信息\"\"\"\n        files_info = {}\n        current_dir = Path.cwd()\n        \n        for file_path in current_dir.rglob(\"*\"):\n            if file_path.is_file() and not self._should_ignore_file(file_path):\n                relative_path = file_path.relative_to(current_dir)\n                try:\n                    with open(file_path, 'r', encoding='utf-8') as f:\n                        content = f.read()\n                    files_info[str(relative_path)] = {\n                        \"content\": content,\n                        \"hash\": hashlib.md5(content.encode()).hexdigest(),\n                        \"size\": file_path.stat().st_size\n                    }\n                except (UnicodeDecodeError, PermissionError):\n                    # 跳过二进制文件或无权限文件\n                    files_info[str(relative_path)] = {\n                        \"content\": \"[BINARY_FILE]\",\n                        \"hash\": \"binary\",\n                        \"size\": file_path.stat().st_size\n                    }\n        \n        return files_info\n    \n    def _should_ignore_file(self, file_path: Path) -> bool:\n        \"\"\"判断是否应该忽略文件\"\"\"\n        ignore_patterns = [\n            \".git\", \"__pycache__\", \".pytest_cache\", \"node_modules\",\n            \".codeagent_memory\", \"*.pyc\", \"*.pyo\", \"*.pyd\", \".DS_Store\"\n        ]\n        \n        path_str = str(file_path)\n        for pattern in ignore_patterns:\n            if pattern in path_str:\n                return True\n        \n        return False\n    \n    def _backup_files(self, checkpoint_dir: Path, files_info: Dict):\n        \"\"\"备份文件到检查点目录\"\"\"\n        files_dir = checkpoint_dir / \"files\"\n        files_dir.mkdir(exist_ok=True)\n        \n        for file_path, info in files_info.items():\n            if info[\"content\"] != \"[BINARY_FILE]\":\n                backup_file = files_dir / file_path\n                backup_file.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(backup_file, 'w', encoding='utf-8') as f:\n                    f.write(info[\"content\"])\n    \n    def list_checkpoints(self) -> List[Dict]:\n        \"\"\"列出所有检查点\"\"\"\n        checkpoints = []\n        \n        for checkpoint_dir in self.checkpoints_dir.iterdir():\n            if checkpoint_dir.is_dir():\n                checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n                if checkpoint_file.exists():\n                    checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n                    checkpoints.append(checkpoint_data)\n        \n        return sorted(checkpoints, key=lambda x: x[\"timestamp\"], reverse=True)\n    \n    def restore_checkpoint(self, checkpoint_id: str) -> bool:\n        \"\"\"恢复检查点\"\"\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        \n        if not checkpoint_file.exists():\n            return False\n        \n        checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n        files_dir = checkpoint_dir / \"files\"\n        \n        if not files_dir.exists():\n            return False\n        \n        # 恢复文件\n        for file_path in files_dir.rglob(\"*\"):\n            if file_path.is_file():\n                relative_path = file_path.relative_to(files_dir)\n                target_path = Path.cwd() / relative_path\n                target_path.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(file_path, 'r', encoding='utf-8') as src:\n                    content = src.read()\n                \n                with open(target_path, 'w', encoding='utf-8') as dst:\n                    dst.write(content)\n        \n        return True\n", "hash": "c9c078b76725e1975d9744efda1cc5e4", "size": 9306}, ".history/codeAgent_20250720143353.py": {"content": "\"\"\"\nCodeAgent - 基于 smolagents 的智能代码编写助手\n集成 DeepSeek 模型，实现从自然语言到代码生成的全流程自动化\n\n主要功能：\n- 自然语言理解与代码生成\n- 文件操作与项目管理\n- 命令执行与环境管理\n- 任务分解与多工具协同\n- 记忆管理与检查点机制\n\"\"\"\n\nimport os\nimport json\nimport time\nimport hashlib\nimport subprocess\nfrom datetime import datetime\nfrom typing import Dict, List, Any, Optional\nfrom pathlib import Path\n\nfrom smolagents import CodeAgent, Tool, LiteLLMModel\nfrom smolagents import PythonInterpreterTool\n\nclass MemoryManager:\n    \"\"\"记忆管理系统 - 实现持久化记忆与检索\"\"\"\n    \n    def __init__(self, memory_dir: str = \".codeagent_memory\"):\n        self.memory_dir = Path(memory_dir)\n        self.memory_dir.mkdir(exist_ok=True)\n        \n        # 记忆文件路径\n        self.conversation_file = self.memory_dir / \"conversations.json\"\n        self.knowledge_file = self.memory_dir / \"knowledge_base.json\"\n        self.checkpoints_dir = self.memory_dir / \"checkpoints\"\n        self.checkpoints_dir.mkdir(exist_ok=True)\n        \n        # 初始化记忆存储\n        self._init_memory_files()\n    \n    def _init_memory_files(self):\n        \"\"\"初始化记忆文件\"\"\"\n        if not self.conversation_file.exists():\n            self._save_json(self.conversation_file, {\"conversations\": []})\n        \n        if not self.knowledge_file.exists():\n            self._save_json(self.knowledge_file, {\n                \"code_patterns\": {},\n                \"project_structures\": {},\n                \"user_preferences\": {},\n                \"learned_solutions\": {}\n            })\n    \n    def _save_json(self, file_path: Path, data: Dict):\n        \"\"\"保存 JSON 数据\"\"\"\n        with open(file_path, 'w', encoding='utf-8') as f:\n            json.dump(data, f, ensure_ascii=False, indent=2)\n    \n    def _load_json(self, file_path: Path) -> Dict:\n        \"\"\"加载 JSON 数据\"\"\"\n        try:\n            with open(file_path, 'r', encoding='utf-8') as f:\n                return json.load(f)\n        except (FileNotFoundError, json.JSONDecodeError):\n            return {}\n    \n    def store_conversation(self, user_input: str, agent_response: str, context: Dict = None):\n        \"\"\"存储对话记录\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        conversation_entry = {\n            \"timestamp\": datetime.now().isoformat(),\n            \"user_input\": user_input,\n            \"agent_response\": agent_response,\n            \"context\": context or {}\n        }\n        \n        conversations[\"conversations\"].append(conversation_entry)\n        \n        # 保持最近 100 条对话\n        if len(conversations[\"conversations\"]) > 100:\n            conversations[\"conversations\"] = conversations[\"conversations\"][-100:]\n        \n        self._save_json(self.conversation_file, conversations)\n    \n    def store_knowledge(self, category: str, key: str, value: Any):\n        \"\"\"存储知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            knowledge[category] = {}\n        \n        knowledge[category][key] = {\n            \"value\": value,\n            \"timestamp\": datetime.now().isoformat()\n        }\n        \n        self._save_json(self.knowledge_file, knowledge)\n    \n    def retrieve_knowledge(self, category: str, key: str = None) -> Any:\n        \"\"\"检索知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            return None\n        \n        if key is None:\n            return knowledge[category]\n        \n        return knowledge[category].get(key, {}).get(\"value\")\n    \n    def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:\n        \"\"\"搜索相关对话\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        relevant_conversations = []\n        query_lower = query.lower()\n        \n        for conv in conversations.get(\"conversations\", []):\n            if (query_lower in conv[\"user_input\"].lower() or \n                query_lower in conv[\"agent_response\"].lower()):\n                relevant_conversations.append(conv)\n        \n        return relevant_conversations[-limit:]\n\nclass CheckpointManager:\n    \"\"\"检查点管理系统 - 实现工作区状态管理\"\"\"\n    \n    def __init__(self, memory_manager: MemoryManager):\n        self.memory_manager = memory_manager\n        self.checkpoints_dir = memory_manager.checkpoints_dir\n    \n    def create_checkpoint(self, name: str, description: str = \"\") -> str:\n        \"\"\"创建检查点\"\"\"\n        checkpoint_id = f\"{name}_{int(time.time())}\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_dir.mkdir(exist_ok=True)\n        \n        # 保存当前工作目录状态\n        current_files = self._get_current_files()\n        \n        checkpoint_data = {\n            \"id\": checkpoint_id,\n            \"name\": name,\n            \"description\": description,\n            \"timestamp\": datetime.now().isoformat(),\n            \"files\": current_files,\n            \"working_directory\": str(Path.cwd())\n        }\n        \n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        self.memory_manager._save_json(checkpoint_file, checkpoint_data)\n        \n        # 复制重要文件\n        self._backup_files(checkpoint_dir, current_files)\n        \n        return checkpoint_id\n    \n    def _get_current_files(self) -> Dict[str, str]:\n        \"\"\"获取当前目录的文件信息\"\"\"\n        files_info = {}\n        current_dir = Path.cwd()\n        \n        for file_path in current_dir.rglob(\"*\"):\n            if file_path.is_file() and not self._should_ignore_file(file_path):\n                relative_path = file_path.relative_to(current_dir)\n                try:\n                    with open(file_path, 'r', encoding='utf-8') as f:\n                        content = f.read()\n                    files_info[str(relative_path)] = {\n                        \"content\": content,\n                        \"hash\": hashlib.md5(content.encode()).hexdigest(),\n                        \"size\": file_path.stat().st_size\n                    }\n                except (UnicodeDecodeError, PermissionError):\n                    # 跳过二进制文件或无权限文件\n                    files_info[str(relative_path)] = {\n                        \"content\": \"[BINARY_FILE]\",\n                        \"hash\": \"binary\",\n                        \"size\": file_path.stat().st_size\n                    }\n        \n        return files_info\n    \n    def _should_ignore_file(self, file_path: Path) -> bool:\n        \"\"\"判断是否应该忽略文件\"\"\"\n        ignore_patterns = [\n            \".git\", \"__pycache__\", \".pytest_cache\", \"node_modules\",\n            \".codeagent_memory\", \"*.pyc\", \"*.pyo\", \"*.pyd\", \".DS_Store\"\n        ]\n        \n        path_str = str(file_path)\n        for pattern in ignore_patterns:\n            if pattern in path_str:\n                return True\n        \n        return False\n    \n    def _backup_files(self, checkpoint_dir: Path, files_info: Dict):\n        \"\"\"备份文件到检查点目录\"\"\"\n        files_dir = checkpoint_dir / \"files\"\n        files_dir.mkdir(exist_ok=True)\n        \n        for file_path, info in files_info.items():\n            if info[\"content\"] != \"[BINARY_FILE]\":\n                backup_file = files_dir / file_path\n                backup_file.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(backup_file, 'w', encoding='utf-8') as f:\n                    f.write(info[\"content\"])\n    \n    def list_checkpoints(self) -> List[Dict]:\n        \"\"\"列出所有检查点\"\"\"\n        checkpoints = []\n        \n        for checkpoint_dir in self.checkpoints_dir.iterdir():\n            if checkpoint_dir.is_dir():\n                checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n                if checkpoint_file.exists():\n                    checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n                    checkpoints.append(checkpoint_data)\n        \n        return sorted(checkpoints, key=lambda x: x[\"timestamp\"], reverse=True)\n    \n    def restore_checkpoint(self, checkpoint_id: str) -> bool:\n        \"\"\"恢复检查点\"\"\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        \n        if not checkpoint_file.exists():\n            return False\n        \n        checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n        files_dir = checkpoint_dir / \"files\"\n        \n        if not files_dir.exists():\n            return False\n        \n        # 恢复文件\n        for file_path in files_dir.rglob(\"*\"):\n            if file_path.is_file():\n                relative_path = file_path.relative_to(files_dir)\n                target_path = Path.cwd() / relative_path\n                target_path.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(file_path, 'r', encoding='utf-8') as src:\n                    content = src.read()\n                \n                with open(target_path, 'w', encoding='utf-8') as dst:\n                    dst.write(content)\n        \n        return True\n\nclass CodeGenerationTool(Tool):\n    \"\"\"代码生成工具 - 基于自然语言生成代码\"\"\"\n\n    name = \"code_generation\"\n    description = \"根据自然语言描述生成代码，支持多种编程语言\"\n    inputs = {\n        \"description\": {\n            \"type\": \"string\",\n            \"description\": \"代码功能的自然语言描述\"\n        },\n        \"language\": {\n            \"type\": \"string\",\n            \"description\": \"编程语言（python, javascript, java, cpp 等）\",\n            \"nullable\": True\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"相关上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, description: str, language: str = \"python\", context: str = \"\") -> str:\n        \"\"\"生成代码\"\"\"\n        # 搜索相关历史经验\n        similar_conversations = self.memory_manager.search_conversations(description, limit=3)\n\n        # 获取用户偏好\n        user_preferences = self.memory_manager.retrieve_knowledge(\"user_preferences\") or {}\n\n        # 构建增强的提示\n        enhanced_prompt = f\"\"\"\n基于以下描述生成 {language} 代码：\n\n需求描述：{description}\n\n上下文信息：{context}\n\n用户偏好：{json.dumps(user_preferences, ensure_ascii=False, indent=2)}\n\n相关历史经验：\n{json.dumps(similar_conversations, ensure_ascii=False, indent=2)}\n\n请生成清晰、可读、符合最佳实践的代码，并包含必要的注释。\n\"\"\"\n\n        # 这里应该调用 LLM 生成代码，暂时返回模板\n        generated_code = f\"\"\"\n# 根据描述生成的 {language} 代码\n# 需求：{description}\n\n# TODO: 实际的代码生成逻辑\ndef generated_function():\n    '''\n    {description}\n    '''\n    pass\n\"\"\"\n\n        # 存储生成的代码模式\n        self.memory_manager.store_knowledge(\n            \"code_patterns\",\n            f\"{language}_{hashlib.md5(description.encode()).hexdigest()[:8]}\",\n            {\n                \"description\": description,\n                \"language\": language,\n                \"code\": generated_code,\n                \"context\": context\n            }\n        )\n\n        return generated_code\n\nclass FileOperationTool(Tool):\n    \"\"\"文件操作工具 - 处理文件读写、创建、删除等操作\"\"\"\n\n    name = \"file_operation\"\n    description = \"执行文件操作：创建、读取、写入、删除、移动文件\"\n    inputs = {\n        \"operation\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：create, read, write, delete, move, list\"\n        },\n        \"file_path\": {\n            \"type\": \"string\",\n            \"description\": \"文件路径\"\n        },\n        \"content\": {\n            \"type\": \"string\",\n            \"description\": \"文件内容（用于 write 操作）\",\n            \"nullable\": True\n        },\n        \"target_path\": {\n            \"type\": \"string\",\n            \"description\": \"目标路径（用于 move 操作）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, operation: str, file_path: str, content: str = \"\", target_path: str = \"\") -> str:\n        \"\"\"执行文件操作\"\"\"\n        try:\n            path = Path(file_path)\n\n            if operation == \"create\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n                path.touch()\n                return f\"文件 {file_path} 创建成功\"\n\n            elif operation == \"read\":\n                if not path.exists():\n                    return f\"错误：文件 {file_path} 不存在\"\n\n                with open(path, 'r', encoding='utf-8') as f:\n                    file_content = f.read()\n\n                # 存储文件访问记录\n                self.memory_manager.store_knowledge(\n                    \"file_access\",\n                    str(path),\n                    {\"last_read\": datetime.now().isoformat(), \"size\": len(file_content)}\n                )\n\n                return file_content\n\n            elif operation == \"write\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n\n                with open(path, 'w', encoding='utf-8') as f:\n                    f.write(content)\n\n                # 存储文件修改记录\n                self.memory_manager.store_knowledge(\n                    \"file_modifications\",\n                    str(path),\n                    {\n                        \"last_modified\": datetime.now().isoformat(),\n                        \"content_hash\": hashlib.md5(content.encode()).hexdigest()\n                    }\n                )\n\n                return f\"内容已写入文件 {file_path}\"\n\n            elif operation == \"delete\":\n                if path.exists():\n                    if path.is_file():\n                        path.unlink()\n                        return f\"文件 {file_path} 删除成功\"\n                    elif path.is_dir():\n                        import shutil\n                        shutil.rmtree(path)\n                        return f\"目录 {file_path} 删除成功\"\n                else:\n                    return f\"错误：路径 {file_path} 不存在\"\n\n            elif operation == \"move\":\n                if not path.exists():\n                    return f\"错误：源文件 {file_path} 不存在\"\n\n                target = Path(target_path)\n                target.parent.mkdir(parents=True, exist_ok=True)\n                path.rename(target)\n                return f\"文件从 {file_path} 移动到 {target_path}\"\n\n            elif operation == \"list\":\n                if path.is_dir():\n                    files = [str(p) for p in path.iterdir()]\n                    return f\"目录 {file_path} 内容：\\n\" + \"\\n\".join(files)\n                else:\n                    return f\"错误：{file_path} 不是目录\"\n\n            else:\n                return f\"不支持的操作：{operation}\"\n\n        except Exception as e:\n            error_msg = f\"文件操作失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"file_op_{int(time.time())}\",\n                {\n                    \"operation\": operation,\n                    \"file_path\": file_path,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n", "hash": "a8bb1fdd3d17e0657dc69fa1d52b4f41", "size": 15937}, ".history/learn_20250520160121.py": {"content": "", "hash": "d41d8cd98f00b204e9800998ecf8427e", "size": 0}, ".history/test_smolagents_20250720131655.py": {"content": "\"\"\"\n简单的 smolagents 测试\n验证基本功能是否正常工作\n\"\"\"\n\nimport os\nfrom smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool\n\ndef test_basic_functionality():\n    \"\"\"测试基本功能\"\"\"\n    print(\"🧪 测试 smolagents 基本功能...\\n\")\n    \n    try:\n        # 创建模型\n        model = InferenceClientModel(\n            model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n            token=os.environ.get(\"HUGGINGFACE_TOKEN\")\n        )\n        print(\"✅ 模型创建成功\")\n        \n        # 创建工具\n        tools = [PythonInterpreterTool()]\n        print(\"✅ 工具创建成功\")\n        \n        # 创建 Agent\n        agent = CodeAgent(\n            tools=tools,\n            model=model,\n            stream_outputs=False,  # 关闭流输出以便测试\n        )\n        print(\"✅ Agent 创建成功\")\n        \n        # 测试简单任务\n        print(\"\\n🔍 测试简单计算任务...\")\n        result = agent.run(\"计算 2 + 3 * 4 的结果\")\n        print(f\"结果: {result}\")\n        \n        print(\"\\n✅ 所有测试通过！smolagents 工作正常。\")\n        return True\n        \n    except Exception as e:\n        print(f\"❌ 测试失败: {e}\")\n        return False\n\nif __name__ == \"__main__\":\n    success = test_basic_functionality()\n    if success:\n        print(\"\\n🎉 可以继续运行完整的示例了！\")\n    else:\n        print(\"\\n💡 请检查:\")\n        print(\"1. 网络连接\")\n        print(\"2. smolagents 安装\")\n        print(\"3. 环境变量设置\")\n", "hash": "affde7f1cb05147fd82676be71a3dcfd", "size": 1534}, ".history/codeAgent_20250720143327.py": {"content": "\"\"\"\nCodeAgent - 基于 smolagents 的智能代码编写助手\n集成 DeepSeek 模型，实现从自然语言到代码生成的全流程自动化\n\n主要功能：\n- 自然语言理解与代码生成\n- 文件操作与项目管理\n- 命令执行与环境管理\n- 任务分解与多工具协同\n- 记忆管理与检查点机制\n\"\"\"\n\nimport os\nimport json\nimport time\nimport hashlib\nimport subprocess\nfrom datetime import datetime\nfrom typing import Dict, List, Any, Optional\nfrom pathlib import Path\n\nfrom smolagents import CodeAgent, Tool, LiteLLMModel\nfrom smolagents import PythonInterpreterTool\n\nclass MemoryManager:\n    \"\"\"记忆管理系统 - 实现持久化记忆与检索\"\"\"\n    \n    def __init__(self, memory_dir: str = \".codeagent_memory\"):\n        self.memory_dir = Path(memory_dir)\n        self.memory_dir.mkdir(exist_ok=True)\n        \n        # 记忆文件路径\n        self.conversation_file = self.memory_dir / \"conversations.json\"\n        self.knowledge_file = self.memory_dir / \"knowledge_base.json\"\n        self.checkpoints_dir = self.memory_dir / \"checkpoints\"\n        self.checkpoints_dir.mkdir(exist_ok=True)\n        \n        # 初始化记忆存储\n        self._init_memory_files()\n    \n    def _init_memory_files(self):\n        \"\"\"初始化记忆文件\"\"\"\n        if not self.conversation_file.exists():\n            self._save_json(self.conversation_file, {\"conversations\": []})\n        \n        if not self.knowledge_file.exists():\n            self._save_json(self.knowledge_file, {\n                \"code_patterns\": {},\n                \"project_structures\": {},\n                \"user_preferences\": {},\n                \"learned_solutions\": {}\n            })\n    \n    def _save_json(self, file_path: Path, data: Dict):\n        \"\"\"保存 JSON 数据\"\"\"\n        with open(file_path, 'w', encoding='utf-8') as f:\n            json.dump(data, f, ensure_ascii=False, indent=2)\n    \n    def _load_json(self, file_path: Path) -> Dict:\n        \"\"\"加载 JSON 数据\"\"\"\n        try:\n            with open(file_path, 'r', encoding='utf-8') as f:\n                return json.load(f)\n        except (FileNotFoundError, json.JSONDecodeError):\n            return {}\n    \n    def store_conversation(self, user_input: str, agent_response: str, context: Dict = None):\n        \"\"\"存储对话记录\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        conversation_entry = {\n            \"timestamp\": datetime.now().isoformat(),\n            \"user_input\": user_input,\n            \"agent_response\": agent_response,\n            \"context\": context or {}\n        }\n        \n        conversations[\"conversations\"].append(conversation_entry)\n        \n        # 保持最近 100 条对话\n        if len(conversations[\"conversations\"]) > 100:\n            conversations[\"conversations\"] = conversations[\"conversations\"][-100:]\n        \n        self._save_json(self.conversation_file, conversations)\n    \n    def store_knowledge(self, category: str, key: str, value: Any):\n        \"\"\"存储知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            knowledge[category] = {}\n        \n        knowledge[category][key] = {\n            \"value\": value,\n            \"timestamp\": datetime.now().isoformat()\n        }\n        \n        self._save_json(self.knowledge_file, knowledge)\n    \n    def retrieve_knowledge(self, category: str, key: str = None) -> Any:\n        \"\"\"检索知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            return None\n        \n        if key is None:\n            return knowledge[category]\n        \n        return knowledge[category].get(key, {}).get(\"value\")\n    \n    def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:\n        \"\"\"搜索相关对话\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        relevant_conversations = []\n        query_lower = query.lower()\n        \n        for conv in conversations.get(\"conversations\", []):\n            if (query_lower in conv[\"user_input\"].lower() or \n                query_lower in conv[\"agent_response\"].lower()):\n                relevant_conversations.append(conv)\n        \n        return relevant_conversations[-limit:]\n\nclass CheckpointManager:\n    \"\"\"检查点管理系统 - 实现工作区状态管理\"\"\"\n    \n    def __init__(self, memory_manager: MemoryManager):\n        self.memory_manager = memory_manager\n        self.checkpoints_dir = memory_manager.checkpoints_dir\n    \n    def create_checkpoint(self, name: str, description: str = \"\") -> str:\n        \"\"\"创建检查点\"\"\"\n        checkpoint_id = f\"{name}_{int(time.time())}\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_dir.mkdir(exist_ok=True)\n        \n        # 保存当前工作目录状态\n        current_files = self._get_current_files()\n        \n        checkpoint_data = {\n            \"id\": checkpoint_id,\n            \"name\": name,\n            \"description\": description,\n            \"timestamp\": datetime.now().isoformat(),\n            \"files\": current_files,\n            \"working_directory\": str(Path.cwd())\n        }\n        \n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        self.memory_manager._save_json(checkpoint_file, checkpoint_data)\n        \n        # 复制重要文件\n        self._backup_files(checkpoint_dir, current_files)\n        \n        return checkpoint_id\n    \n    def _get_current_files(self) -> Dict[str, str]:\n        \"\"\"获取当前目录的文件信息\"\"\"\n        files_info = {}\n        current_dir = Path.cwd()\n        \n        for file_path in current_dir.rglob(\"*\"):\n            if file_path.is_file() and not self._should_ignore_file(file_path):\n                relative_path = file_path.relative_to(current_dir)\n                try:\n                    with open(file_path, 'r', encoding='utf-8') as f:\n                        content = f.read()\n                    files_info[str(relative_path)] = {\n                        \"content\": content,\n                        \"hash\": hashlib.md5(content.encode()).hexdigest(),\n                        \"size\": file_path.stat().st_size\n                    }\n                except (UnicodeDecodeError, PermissionError):\n                    # 跳过二进制文件或无权限文件\n                    files_info[str(relative_path)] = {\n                        \"content\": \"[BINARY_FILE]\",\n                        \"hash\": \"binary\",\n                        \"size\": file_path.stat().st_size\n                    }\n        \n        return files_info\n    \n    def _should_ignore_file(self, file_path: Path) -> bool:\n        \"\"\"判断是否应该忽略文件\"\"\"\n        ignore_patterns = [\n            \".git\", \"__pycache__\", \".pytest_cache\", \"node_modules\",\n            \".codeagent_memory\", \"*.pyc\", \"*.pyo\", \"*.pyd\", \".DS_Store\"\n        ]\n        \n        path_str = str(file_path)\n        for pattern in ignore_patterns:\n            if pattern in path_str:\n                return True\n        \n        return False\n    \n    def _backup_files(self, checkpoint_dir: Path, files_info: Dict):\n        \"\"\"备份文件到检查点目录\"\"\"\n        files_dir = checkpoint_dir / \"files\"\n        files_dir.mkdir(exist_ok=True)\n        \n        for file_path, info in files_info.items():\n            if info[\"content\"] != \"[BINARY_FILE]\":\n                backup_file = files_dir / file_path\n                backup_file.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(backup_file, 'w', encoding='utf-8') as f:\n                    f.write(info[\"content\"])\n    \n    def list_checkpoints(self) -> List[Dict]:\n        \"\"\"列出所有检查点\"\"\"\n        checkpoints = []\n        \n        for checkpoint_dir in self.checkpoints_dir.iterdir():\n            if checkpoint_dir.is_dir():\n                checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n                if checkpoint_file.exists():\n                    checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n                    checkpoints.append(checkpoint_data)\n        \n        return sorted(checkpoints, key=lambda x: x[\"timestamp\"], reverse=True)\n    \n    def restore_checkpoint(self, checkpoint_id: str) -> bool:\n        \"\"\"恢复检查点\"\"\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        \n        if not checkpoint_file.exists():\n            return False\n        \n        checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n        files_dir = checkpoint_dir / \"files\"\n        \n        if not files_dir.exists():\n            return False\n        \n        # 恢复文件\n        for file_path in files_dir.rglob(\"*\"):\n            if file_path.is_file():\n                relative_path = file_path.relative_to(files_dir)\n                target_path = Path.cwd() / relative_path\n                target_path.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(file_path, 'r', encoding='utf-8') as src:\n                    content = src.read()\n                \n                with open(target_path, 'w', encoding='utf-8') as dst:\n                    dst.write(content)\n        \n        return True\n\nclass CodeGenerationTool(Tool):\n    \"\"\"代码生成工具 - 基于自然语言生成代码\"\"\"\n\n    name = \"code_generation\"\n    description = \"根据自然语言描述生成代码，支持多种编程语言\"\n    inputs = {\n        \"description\": {\n            \"type\": \"string\",\n            \"description\": \"代码功能的自然语言描述\"\n        },\n        \"language\": {\n            \"type\": \"string\",\n            \"description\": \"编程语言（python, javascript, java, cpp 等）\",\n            \"nullable\": True\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"相关上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, description: str, language: str = \"python\", context: str = \"\") -> str:\n        \"\"\"生成代码\"\"\"\n        # 搜索相关历史经验\n        similar_conversations = self.memory_manager.search_conversations(description, limit=3)\n\n        # 获取用户偏好\n        user_preferences = self.memory_manager.retrieve_knowledge(\"user_preferences\") or {}\n\n        # 构建增强的提示\n        enhanced_prompt = f\"\"\"\n基于以下描述生成 {language} 代码：\n\n需求描述：{description}\n\n上下文信息：{context}\n\n用户偏好：{json.dumps(user_preferences, ensure_ascii=False, indent=2)}\n\n相关历史经验：\n{json.dumps(similar_conversations, ensure_ascii=False, indent=2)}\n\n请生成清晰、可读、符合最佳实践的代码，并包含必要的注释。\n\"\"\"\n\n        # 这里应该调用 LLM 生成代码，暂时返回模板\n        generated_code = f\"\"\"\n# 根据描述生成的 {language} 代码\n# 需求：{description}\n\n# TODO: 实际的代码生成逻辑\ndef generated_function():\n    '''\n    {description}\n    '''\n    pass\n\"\"\"\n\n        # 存储生成的代码模式\n        self.memory_manager.store_knowledge(\n            \"code_patterns\",\n            f\"{language}_{hashlib.md5(description.encode()).hexdigest()[:8]}\",\n            {\n                \"description\": description,\n                \"language\": language,\n                \"code\": generated_code,\n                \"context\": context\n            }\n        )\n\n        return generated_code\n", "hash": "d46dce8601e736e739d0acd8a56d0548", "size": 11627}, ".history/learn_20250720131057.py": {"content": "\"\"\"\nsmolagents 学习项目\n基于 Hugging Face smolagents 库的 Agent 开发示例\n\"\"\"\n\n# 这个文件将包含我们的 smolagents 学习示例\nprint(\"欢迎来到 smolagents 学习项目！\")", "hash": "cafb7551fcb60740af9c5a15894394da", "size": 195}, ".history/codeAgent_20250720143418.py": {"content": "\"\"\"\nCodeAgent - 基于 smolagents 的智能代码编写助手\n集成 DeepSeek 模型，实现从自然语言到代码生成的全流程自动化\n\n主要功能：\n- 自然语言理解与代码生成\n- 文件操作与项目管理\n- 命令执行与环境管理\n- 任务分解与多工具协同\n- 记忆管理与检查点机制\n\"\"\"\n\nimport os\nimport json\nimport time\nimport hashlib\nimport subprocess\nfrom datetime import datetime\nfrom typing import Dict, List, Any, Optional\nfrom pathlib import Path\n\nfrom smolagents import CodeAgent, Tool, LiteLLMModel\nfrom smolagents import PythonInterpreterTool\n\nclass MemoryManager:\n    \"\"\"记忆管理系统 - 实现持久化记忆与检索\"\"\"\n    \n    def __init__(self, memory_dir: str = \".codeagent_memory\"):\n        self.memory_dir = Path(memory_dir)\n        self.memory_dir.mkdir(exist_ok=True)\n        \n        # 记忆文件路径\n        self.conversation_file = self.memory_dir / \"conversations.json\"\n        self.knowledge_file = self.memory_dir / \"knowledge_base.json\"\n        self.checkpoints_dir = self.memory_dir / \"checkpoints\"\n        self.checkpoints_dir.mkdir(exist_ok=True)\n        \n        # 初始化记忆存储\n        self._init_memory_files()\n    \n    def _init_memory_files(self):\n        \"\"\"初始化记忆文件\"\"\"\n        if not self.conversation_file.exists():\n            self._save_json(self.conversation_file, {\"conversations\": []})\n        \n        if not self.knowledge_file.exists():\n            self._save_json(self.knowledge_file, {\n                \"code_patterns\": {},\n                \"project_structures\": {},\n                \"user_preferences\": {},\n                \"learned_solutions\": {}\n            })\n    \n    def _save_json(self, file_path: Path, data: Dict):\n        \"\"\"保存 JSON 数据\"\"\"\n        with open(file_path, 'w', encoding='utf-8') as f:\n            json.dump(data, f, ensure_ascii=False, indent=2)\n    \n    def _load_json(self, file_path: Path) -> Dict:\n        \"\"\"加载 JSON 数据\"\"\"\n        try:\n            with open(file_path, 'r', encoding='utf-8') as f:\n                return json.load(f)\n        except (FileNotFoundError, json.JSONDecodeError):\n            return {}\n    \n    def store_conversation(self, user_input: str, agent_response: str, context: Dict = None):\n        \"\"\"存储对话记录\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        conversation_entry = {\n            \"timestamp\": datetime.now().isoformat(),\n            \"user_input\": user_input,\n            \"agent_response\": agent_response,\n            \"context\": context or {}\n        }\n        \n        conversations[\"conversations\"].append(conversation_entry)\n        \n        # 保持最近 100 条对话\n        if len(conversations[\"conversations\"]) > 100:\n            conversations[\"conversations\"] = conversations[\"conversations\"][-100:]\n        \n        self._save_json(self.conversation_file, conversations)\n    \n    def store_knowledge(self, category: str, key: str, value: Any):\n        \"\"\"存储知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            knowledge[category] = {}\n        \n        knowledge[category][key] = {\n            \"value\": value,\n            \"timestamp\": datetime.now().isoformat()\n        }\n        \n        self._save_json(self.knowledge_file, knowledge)\n    \n    def retrieve_knowledge(self, category: str, key: str = None) -> Any:\n        \"\"\"检索知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            return None\n        \n        if key is None:\n            return knowledge[category]\n        \n        return knowledge[category].get(key, {}).get(\"value\")\n    \n    def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:\n        \"\"\"搜索相关对话\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        relevant_conversations = []\n        query_lower = query.lower()\n        \n        for conv in conversations.get(\"conversations\", []):\n            if (query_lower in conv[\"user_input\"].lower() or \n                query_lower in conv[\"agent_response\"].lower()):\n                relevant_conversations.append(conv)\n        \n        return relevant_conversations[-limit:]\n\nclass CheckpointManager:\n    \"\"\"检查点管理系统 - 实现工作区状态管理\"\"\"\n    \n    def __init__(self, memory_manager: MemoryManager):\n        self.memory_manager = memory_manager\n        self.checkpoints_dir = memory_manager.checkpoints_dir\n    \n    def create_checkpoint(self, name: str, description: str = \"\") -> str:\n        \"\"\"创建检查点\"\"\"\n        checkpoint_id = f\"{name}_{int(time.time())}\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_dir.mkdir(exist_ok=True)\n        \n        # 保存当前工作目录状态\n        current_files = self._get_current_files()\n        \n        checkpoint_data = {\n            \"id\": checkpoint_id,\n            \"name\": name,\n            \"description\": description,\n            \"timestamp\": datetime.now().isoformat(),\n            \"files\": current_files,\n            \"working_directory\": str(Path.cwd())\n        }\n        \n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        self.memory_manager._save_json(checkpoint_file, checkpoint_data)\n        \n        # 复制重要文件\n        self._backup_files(checkpoint_dir, current_files)\n        \n        return checkpoint_id\n    \n    def _get_current_files(self) -> Dict[str, str]:\n        \"\"\"获取当前目录的文件信息\"\"\"\n        files_info = {}\n        current_dir = Path.cwd()\n        \n        for file_path in current_dir.rglob(\"*\"):\n            if file_path.is_file() and not self._should_ignore_file(file_path):\n                relative_path = file_path.relative_to(current_dir)\n                try:\n                    with open(file_path, 'r', encoding='utf-8') as f:\n                        content = f.read()\n                    files_info[str(relative_path)] = {\n                        \"content\": content,\n                        \"hash\": hashlib.md5(content.encode()).hexdigest(),\n                        \"size\": file_path.stat().st_size\n                    }\n                except (UnicodeDecodeError, PermissionError):\n                    # 跳过二进制文件或无权限文件\n                    files_info[str(relative_path)] = {\n                        \"content\": \"[BINARY_FILE]\",\n                        \"hash\": \"binary\",\n                        \"size\": file_path.stat().st_size\n                    }\n        \n        return files_info\n    \n    def _should_ignore_file(self, file_path: Path) -> bool:\n        \"\"\"判断是否应该忽略文件\"\"\"\n        ignore_patterns = [\n            \".git\", \"__pycache__\", \".pytest_cache\", \"node_modules\",\n            \".codeagent_memory\", \"*.pyc\", \"*.pyo\", \"*.pyd\", \".DS_Store\"\n        ]\n        \n        path_str = str(file_path)\n        for pattern in ignore_patterns:\n            if pattern in path_str:\n                return True\n        \n        return False\n    \n    def _backup_files(self, checkpoint_dir: Path, files_info: Dict):\n        \"\"\"备份文件到检查点目录\"\"\"\n        files_dir = checkpoint_dir / \"files\"\n        files_dir.mkdir(exist_ok=True)\n        \n        for file_path, info in files_info.items():\n            if info[\"content\"] != \"[BINARY_FILE]\":\n                backup_file = files_dir / file_path\n                backup_file.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(backup_file, 'w', encoding='utf-8') as f:\n                    f.write(info[\"content\"])\n    \n    def list_checkpoints(self) -> List[Dict]:\n        \"\"\"列出所有检查点\"\"\"\n        checkpoints = []\n        \n        for checkpoint_dir in self.checkpoints_dir.iterdir():\n            if checkpoint_dir.is_dir():\n                checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n                if checkpoint_file.exists():\n                    checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n                    checkpoints.append(checkpoint_data)\n        \n        return sorted(checkpoints, key=lambda x: x[\"timestamp\"], reverse=True)\n    \n    def restore_checkpoint(self, checkpoint_id: str) -> bool:\n        \"\"\"恢复检查点\"\"\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        \n        if not checkpoint_file.exists():\n            return False\n        \n        checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n        files_dir = checkpoint_dir / \"files\"\n        \n        if not files_dir.exists():\n            return False\n        \n        # 恢复文件\n        for file_path in files_dir.rglob(\"*\"):\n            if file_path.is_file():\n                relative_path = file_path.relative_to(files_dir)\n                target_path = Path.cwd() / relative_path\n                target_path.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(file_path, 'r', encoding='utf-8') as src:\n                    content = src.read()\n                \n                with open(target_path, 'w', encoding='utf-8') as dst:\n                    dst.write(content)\n        \n        return True\n\nclass CodeGenerationTool(Tool):\n    \"\"\"代码生成工具 - 基于自然语言生成代码\"\"\"\n\n    name = \"code_generation\"\n    description = \"根据自然语言描述生成代码，支持多种编程语言\"\n    inputs = {\n        \"description\": {\n            \"type\": \"string\",\n            \"description\": \"代码功能的自然语言描述\"\n        },\n        \"language\": {\n            \"type\": \"string\",\n            \"description\": \"编程语言（python, javascript, java, cpp 等）\",\n            \"nullable\": True\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"相关上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, description: str, language: str = \"python\", context: str = \"\") -> str:\n        \"\"\"生成代码\"\"\"\n        # 搜索相关历史经验\n        similar_conversations = self.memory_manager.search_conversations(description, limit=3)\n\n        # 获取用户偏好\n        user_preferences = self.memory_manager.retrieve_knowledge(\"user_preferences\") or {}\n\n        # 构建增强的提示\n        enhanced_prompt = f\"\"\"\n基于以下描述生成 {language} 代码：\n\n需求描述：{description}\n\n上下文信息：{context}\n\n用户偏好：{json.dumps(user_preferences, ensure_ascii=False, indent=2)}\n\n相关历史经验：\n{json.dumps(similar_conversations, ensure_ascii=False, indent=2)}\n\n请生成清晰、可读、符合最佳实践的代码，并包含必要的注释。\n\"\"\"\n\n        # 这里应该调用 LLM 生成代码，暂时返回模板\n        generated_code = f\"\"\"\n# 根据描述生成的 {language} 代码\n# 需求：{description}\n\n# TODO: 实际的代码生成逻辑\ndef generated_function():\n    '''\n    {description}\n    '''\n    pass\n\"\"\"\n\n        # 存储生成的代码模式\n        self.memory_manager.store_knowledge(\n            \"code_patterns\",\n            f\"{language}_{hashlib.md5(description.encode()).hexdigest()[:8]}\",\n            {\n                \"description\": description,\n                \"language\": language,\n                \"code\": generated_code,\n                \"context\": context\n            }\n        )\n\n        return generated_code\n\nclass FileOperationTool(Tool):\n    \"\"\"文件操作工具 - 处理文件读写、创建、删除等操作\"\"\"\n\n    name = \"file_operation\"\n    description = \"执行文件操作：创建、读取、写入、删除、移动文件\"\n    inputs = {\n        \"operation\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：create, read, write, delete, move, list\"\n        },\n        \"file_path\": {\n            \"type\": \"string\",\n            \"description\": \"文件路径\"\n        },\n        \"content\": {\n            \"type\": \"string\",\n            \"description\": \"文件内容（用于 write 操作）\",\n            \"nullable\": True\n        },\n        \"target_path\": {\n            \"type\": \"string\",\n            \"description\": \"目标路径（用于 move 操作）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, operation: str, file_path: str, content: str = \"\", target_path: str = \"\") -> str:\n        \"\"\"执行文件操作\"\"\"\n        try:\n            path = Path(file_path)\n\n            if operation == \"create\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n                path.touch()\n                return f\"文件 {file_path} 创建成功\"\n\n            elif operation == \"read\":\n                if not path.exists():\n                    return f\"错误：文件 {file_path} 不存在\"\n\n                with open(path, 'r', encoding='utf-8') as f:\n                    file_content = f.read()\n\n                # 存储文件访问记录\n                self.memory_manager.store_knowledge(\n                    \"file_access\",\n                    str(path),\n                    {\"last_read\": datetime.now().isoformat(), \"size\": len(file_content)}\n                )\n\n                return file_content\n\n            elif operation == \"write\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n\n                with open(path, 'w', encoding='utf-8') as f:\n                    f.write(content)\n\n                # 存储文件修改记录\n                self.memory_manager.store_knowledge(\n                    \"file_modifications\",\n                    str(path),\n                    {\n                        \"last_modified\": datetime.now().isoformat(),\n                        \"content_hash\": hashlib.md5(content.encode()).hexdigest()\n                    }\n                )\n\n                return f\"内容已写入文件 {file_path}\"\n\n            elif operation == \"delete\":\n                if path.exists():\n                    if path.is_file():\n                        path.unlink()\n                        return f\"文件 {file_path} 删除成功\"\n                    elif path.is_dir():\n                        import shutil\n                        shutil.rmtree(path)\n                        return f\"目录 {file_path} 删除成功\"\n                else:\n                    return f\"错误：路径 {file_path} 不存在\"\n\n            elif operation == \"move\":\n                if not path.exists():\n                    return f\"错误：源文件 {file_path} 不存在\"\n\n                target = Path(target_path)\n                target.parent.mkdir(parents=True, exist_ok=True)\n                path.rename(target)\n                return f\"文件从 {file_path} 移动到 {target_path}\"\n\n            elif operation == \"list\":\n                if path.is_dir():\n                    files = [str(p) for p in path.iterdir()]\n                    return f\"目录 {file_path} 内容：\\n\" + \"\\n\".join(files)\n                else:\n                    return f\"错误：{file_path} 不是目录\"\n\n            else:\n                return f\"不支持的操作：{operation}\"\n\n        except Exception as e:\n            error_msg = f\"文件操作失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"file_op_{int(time.time())}\",\n                {\n                    \"operation\": operation,\n                    \"file_path\": file_path,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\nclass CommandExecutionTool(Tool):\n    \"\"\"命令执行工具 - 安全执行系统命令\"\"\"\n\n    name = \"command_execution\"\n    description = \"执行系统命令，支持安全检查和结果记录\"\n    inputs = {\n        \"command\": {\n            \"type\": \"string\",\n            \"description\": \"要执行的命令\"\n        },\n        \"working_dir\": {\n            \"type\": \"string\",\n            \"description\": \"工作目录\",\n            \"nullable\": True\n        },\n        \"timeout\": {\n            \"type\": \"number\",\n            \"description\": \"超时时间（秒）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n        # 危险命令黑名单\n        self.dangerous_commands = [\n            \"rm -rf\", \"del /f\", \"format\", \"fdisk\", \"mkfs\",\n            \"shutdown\", \"reboot\", \"halt\", \"poweroff\",\n            \"chmod 777\", \"chown\", \"sudo rm\", \"dd if=\"\n        ]\n\n    def _is_safe_command(self, command: str) -> bool:\n        \"\"\"检查命令是否安全\"\"\"\n        command_lower = command.lower()\n\n        for dangerous in self.dangerous_commands:\n            if dangerous in command_lower:\n                return False\n\n        return True\n\n    def forward(self, command: str, working_dir: str = \"\", timeout: float = 30.0) -> str:\n        \"\"\"执行命令\"\"\"\n        # 安全检查\n        if not self._is_safe_command(command):\n            error_msg = f\"危险命令被阻止：{command}\"\n            self.memory_manager.store_knowledge(\n                \"security_blocks\",\n                f\"cmd_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"reason\": \"dangerous_command\",\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n            return error_msg\n\n        try:\n            # 设置工作目录\n            cwd = Path(working_dir) if working_dir else Path.cwd()\n\n            # 执行命令\n            result = subprocess.run(\n                command,\n                shell=True,\n                cwd=cwd,\n                capture_output=True,\n                text=True,\n                timeout=timeout\n            )\n\n            # 构建结果\n            output = f\"命令执行完成\\n\"\n            output += f\"返回码: {result.returncode}\\n\"\n\n            if result.stdout:\n                output += f\"标准输出:\\n{result.stdout}\\n\"\n\n            if result.stderr:\n                output += f\"标准错误:\\n{result.stderr}\\n\"\n\n            # 记录命令执行\n            self.memory_manager.store_knowledge(\n                \"command_history\",\n                f\"cmd_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"working_dir\": str(cwd),\n                    \"return_code\": result.returncode,\n                    \"stdout\": result.stdout,\n                    \"stderr\": result.stderr,\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return output\n\n        except subprocess.TimeoutExpired:\n            return f\"命令执行超时（{timeout}秒）：{command}\"\n\n        except Exception as e:\n            error_msg = f\"命令执行失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"cmd_error_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n", "hash": "a0ff65096f1f1caa9aa1f2fa3bf47655", "size": 19624}, ".history/test_smolagents_20250720131734.py": {"content": "\"\"\"\n简单的 smolagents 测试\n验证基本功能是否正常工作\n\"\"\"\n\nimport os\nfrom smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool\n\ndef test_basic_functionality():\n    \"\"\"测试基本功能\"\"\"\n    print(\"🧪 测试 smolagents 基本功能...\\n\")\n    \n    try:\n        # 创建模型 - 使用 HF Inference API\n        model = InferenceClientModel(\n            model_id=\"microsoft/DialoGPT-medium\",  # 使用一个更简单的模型进行测试\n            token=os.environ.get(\"HUGGINGFACE_TOKEN\"),\n            provider=\"hf-inference\"  # 明确指定 HF 推理提供商\n        )\n        print(\"✅ 模型创建成功\")\n        \n        # 创建工具\n        tools = [PythonInterpreterTool()]\n        print(\"✅ 工具创建成功\")\n        \n        # 创建 Agent\n        agent = CodeAgent(\n            tools=tools,\n            model=model,\n            stream_outputs=False,  # 关闭流输出以便测试\n        )\n        print(\"✅ Agent 创建成功\")\n        \n        # 测试简单任务\n        print(\"\\n🔍 测试简单计算任务...\")\n        result = agent.run(\"计算 2 + 3 * 4 的结果\")\n        print(f\"结果: {result}\")\n        \n        print(\"\\n✅ 所有测试通过！smolagents 工作正常。\")\n        return True\n        \n    except Exception as e:\n        print(f\"❌ 测试失败: {e}\")\n        return False\n\nif __name__ == \"__main__\":\n    success = test_basic_functionality()\n    if success:\n        print(\"\\n🎉 可以继续运行完整的示例了！\")\n    else:\n        print(\"\\n💡 请检查:\")\n        print(\"1. 网络连接\")\n        print(\"2. smolagents 安装\")\n        print(\"3. 环境变量设置\")\n", "hash": "14a12b8eab85a40d32a57a93daf4006d", "size": 1672}, ".history/test_smolagents_20250720131632.py": {"content": "\"\"\"\n简单的 smolagents 测试\n验证基本功能是否正常工作\n\"\"\"\n\nimport os\nfrom smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool\n\ndef test_basic_functionality():\n    \"\"\"测试基本功能\"\"\"\n    print(\"🧪 测试 smolagents 基本功能...\\n\")\n    \n    try:\n        # 创建模型\n        model = InferenceClientModel(\n            model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n            token=os.environ.get(\"HUGGINGFACE_TOKEN\")\n        )\n        print(\"✅ 模型创建成功\")\n        \n        # 创建工具\n        tools = [PythonInterpreterTool()]\n        print(\"✅ 工具创建成功\")\n        \n        # 创建 Agent\n        agent = CodeAgent(\n            tools=tools,\n            model=model,\n            max_iterations=5,\n            stream_outputs=False,  # 关闭流输出以便测试\n            verbose=False\n        )\n        print(\"✅ Agent 创建成功\")\n        \n        # 测试简单任务\n        print(\"\\n🔍 测试简单计算任务...\")\n        result = agent.run(\"计算 2 + 3 * 4 的结果\")\n        print(f\"结果: {result}\")\n        \n        print(\"\\n✅ 所有测试通过！smolagents 工作正常。\")\n        return True\n        \n    except Exception as e:\n        print(f\"❌ 测试失败: {e}\")\n        return False\n\nif __name__ == \"__main__\":\n    success = test_basic_functionality()\n    if success:\n        print(\"\\n🎉 可以继续运行完整的示例了！\")\n    else:\n        print(\"\\n💡 请检查:\")\n        print(\"1. 网络连接\")\n        print(\"2. smolagents 安装\")\n        print(\"3. 环境变量设置\")\n", "hash": "f549b3d921914d1d684a1734d12ef03f", "size": 1590}, ".history/test_smolagents_20250720131713.py": {"content": "\"\"\"\n简单的 smolagents 测试\n验证基本功能是否正常工作\n\"\"\"\n\nimport os\nfrom smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool\n\ndef test_basic_functionality():\n    \"\"\"测试基本功能\"\"\"\n    print(\"🧪 测试 smolagents 基本功能...\\n\")\n    \n    try:\n        # 创建模型\n        model = InferenceClientModel(\n            model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n            token=os.environ.get(\"HUGGINGFACE_TOKEN\"),\n            provider=\"auto\"  # 自动选择可用的提供商\n        )\n        print(\"✅ 模型创建成功\")\n        \n        # 创建工具\n        tools = [PythonInterpreterTool()]\n        print(\"✅ 工具创建成功\")\n        \n        # 创建 Agent\n        agent = CodeAgent(\n            tools=tools,\n            model=model,\n            stream_outputs=False,  # 关闭流输出以便测试\n        )\n        print(\"✅ Agent 创建成功\")\n        \n        # 测试简单任务\n        print(\"\\n🔍 测试简单计算任务...\")\n        result = agent.run(\"计算 2 + 3 * 4 的结果\")\n        print(f\"结果: {result}\")\n        \n        print(\"\\n✅ 所有测试通过！smolagents 工作正常。\")\n        return True\n        \n    except Exception as e:\n        print(f\"❌ 测试失败: {e}\")\n        return False\n\nif __name__ == \"__main__\":\n    success = test_basic_functionality()\n    if success:\n        print(\"\\n🎉 可以继续运行完整的示例了！\")\n    else:\n        print(\"\\n💡 请检查:\")\n        print(\"1. 网络连接\")\n        print(\"2. smolagents 安装\")\n        print(\"3. 环境变量设置\")\n", "hash": "961250bd541e5966b98dfd043e4651d9", "size": 1597}, ".history/codeAgent_20250720143629.py": {"content": "\"\"\"\nCodeAgent - 基于 smolagents 的智能代码编写助手\n集成 DeepSeek 模型，实现从自然语言到代码生成的全流程自动化\n\n主要功能：\n- 自然语言理解与代码生成\n- 文件操作与项目管理\n- 命令执行与环境管理\n- 任务分解与多工具协同\n- 记忆管理与检查点机制\n\"\"\"\n\nimport os\nimport json\nimport time\nimport hashlib\nimport subprocess\nfrom datetime import datetime\nfrom typing import Dict, List, Any, Optional\nfrom pathlib import Path\n\nfrom smolagents import CodeAgent, Tool, LiteLLMModel\nfrom smolagents import PythonInterpreterTool\n\nclass MemoryManager:\n    \"\"\"记忆管理系统 - 实现持久化记忆与检索\"\"\"\n    \n    def __init__(self, memory_dir: str = \".codeagent_memory\"):\n        self.memory_dir = Path(memory_dir)\n        self.memory_dir.mkdir(exist_ok=True)\n        \n        # 记忆文件路径\n        self.conversation_file = self.memory_dir / \"conversations.json\"\n        self.knowledge_file = self.memory_dir / \"knowledge_base.json\"\n        self.checkpoints_dir = self.memory_dir / \"checkpoints\"\n        self.checkpoints_dir.mkdir(exist_ok=True)\n        \n        # 初始化记忆存储\n        self._init_memory_files()\n    \n    def _init_memory_files(self):\n        \"\"\"初始化记忆文件\"\"\"\n        if not self.conversation_file.exists():\n            self._save_json(self.conversation_file, {\"conversations\": []})\n        \n        if not self.knowledge_file.exists():\n            self._save_json(self.knowledge_file, {\n                \"code_patterns\": {},\n                \"project_structures\": {},\n                \"user_preferences\": {},\n                \"learned_solutions\": {}\n            })\n    \n    def _save_json(self, file_path: Path, data: Dict):\n        \"\"\"保存 JSON 数据\"\"\"\n        with open(file_path, 'w', encoding='utf-8') as f:\n            json.dump(data, f, ensure_ascii=False, indent=2)\n    \n    def _load_json(self, file_path: Path) -> Dict:\n        \"\"\"加载 JSON 数据\"\"\"\n        try:\n            with open(file_path, 'r', encoding='utf-8') as f:\n                return json.load(f)\n        except (FileNotFoundError, json.JSONDecodeError):\n            return {}\n    \n    def store_conversation(self, user_input: str, agent_response: str, context: Dict = None):\n        \"\"\"存储对话记录\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        conversation_entry = {\n            \"timestamp\": datetime.now().isoformat(),\n            \"user_input\": user_input,\n            \"agent_response\": agent_response,\n            \"context\": context or {}\n        }\n        \n        conversations[\"conversations\"].append(conversation_entry)\n        \n        # 保持最近 100 条对话\n        if len(conversations[\"conversations\"]) > 100:\n            conversations[\"conversations\"] = conversations[\"conversations\"][-100:]\n        \n        self._save_json(self.conversation_file, conversations)\n    \n    def store_knowledge(self, category: str, key: str, value: Any):\n        \"\"\"存储知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            knowledge[category] = {}\n        \n        knowledge[category][key] = {\n            \"value\": value,\n            \"timestamp\": datetime.now().isoformat()\n        }\n        \n        self._save_json(self.knowledge_file, knowledge)\n    \n    def retrieve_knowledge(self, category: str, key: str = None) -> Any:\n        \"\"\"检索知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            return None\n        \n        if key is None:\n            return knowledge[category]\n        \n        return knowledge[category].get(key, {}).get(\"value\")\n    \n    def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:\n        \"\"\"搜索相关对话\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        relevant_conversations = []\n        query_lower = query.lower()\n        \n        for conv in conversations.get(\"conversations\", []):\n            if (query_lower in conv[\"user_input\"].lower() or \n                query_lower in conv[\"agent_response\"].lower()):\n                relevant_conversations.append(conv)\n        \n        return relevant_conversations[-limit:]\n\nclass CheckpointManager:\n    \"\"\"检查点管理系统 - 实现工作区状态管理\"\"\"\n    \n    def __init__(self, memory_manager: MemoryManager):\n        self.memory_manager = memory_manager\n        self.checkpoints_dir = memory_manager.checkpoints_dir\n    \n    def create_checkpoint(self, name: str, description: str = \"\") -> str:\n        \"\"\"创建检查点\"\"\"\n        checkpoint_id = f\"{name}_{int(time.time())}\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_dir.mkdir(exist_ok=True)\n        \n        # 保存当前工作目录状态\n        current_files = self._get_current_files()\n        \n        checkpoint_data = {\n            \"id\": checkpoint_id,\n            \"name\": name,\n            \"description\": description,\n            \"timestamp\": datetime.now().isoformat(),\n            \"files\": current_files,\n            \"working_directory\": str(Path.cwd())\n        }\n        \n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        self.memory_manager._save_json(checkpoint_file, checkpoint_data)\n        \n        # 复制重要文件\n        self._backup_files(checkpoint_dir, current_files)\n        \n        return checkpoint_id\n    \n    def _get_current_files(self) -> Dict[str, str]:\n        \"\"\"获取当前目录的文件信息\"\"\"\n        files_info = {}\n        current_dir = Path.cwd()\n        \n        for file_path in current_dir.rglob(\"*\"):\n            if file_path.is_file() and not self._should_ignore_file(file_path):\n                relative_path = file_path.relative_to(current_dir)\n                try:\n                    with open(file_path, 'r', encoding='utf-8') as f:\n                        content = f.read()\n                    files_info[str(relative_path)] = {\n                        \"content\": content,\n                        \"hash\": hashlib.md5(content.encode()).hexdigest(),\n                        \"size\": file_path.stat().st_size\n                    }\n                except (UnicodeDecodeError, PermissionError):\n                    # 跳过二进制文件或无权限文件\n                    files_info[str(relative_path)] = {\n                        \"content\": \"[BINARY_FILE]\",\n                        \"hash\": \"binary\",\n                        \"size\": file_path.stat().st_size\n                    }\n        \n        return files_info\n    \n    def _should_ignore_file(self, file_path: Path) -> bool:\n        \"\"\"判断是否应该忽略文件\"\"\"\n        ignore_patterns = [\n            \".git\", \"__pycache__\", \".pytest_cache\", \"node_modules\",\n            \".codeagent_memory\", \"*.pyc\", \"*.pyo\", \"*.pyd\", \".DS_Store\"\n        ]\n        \n        path_str = str(file_path)\n        for pattern in ignore_patterns:\n            if pattern in path_str:\n                return True\n        \n        return False\n    \n    def _backup_files(self, checkpoint_dir: Path, files_info: Dict):\n        \"\"\"备份文件到检查点目录\"\"\"\n        files_dir = checkpoint_dir / \"files\"\n        files_dir.mkdir(exist_ok=True)\n        \n        for file_path, info in files_info.items():\n            if info[\"content\"] != \"[BINARY_FILE]\":\n                backup_file = files_dir / file_path\n                backup_file.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(backup_file, 'w', encoding='utf-8') as f:\n                    f.write(info[\"content\"])\n    \n    def list_checkpoints(self) -> List[Dict]:\n        \"\"\"列出所有检查点\"\"\"\n        checkpoints = []\n        \n        for checkpoint_dir in self.checkpoints_dir.iterdir():\n            if checkpoint_dir.is_dir():\n                checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n                if checkpoint_file.exists():\n                    checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n                    checkpoints.append(checkpoint_data)\n        \n        return sorted(checkpoints, key=lambda x: x[\"timestamp\"], reverse=True)\n    \n    def restore_checkpoint(self, checkpoint_id: str) -> bool:\n        \"\"\"恢复检查点\"\"\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        \n        if not checkpoint_file.exists():\n            return False\n        \n        checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n        files_dir = checkpoint_dir / \"files\"\n        \n        if not files_dir.exists():\n            return False\n        \n        # 恢复文件\n        for file_path in files_dir.rglob(\"*\"):\n            if file_path.is_file():\n                relative_path = file_path.relative_to(files_dir)\n                target_path = Path.cwd() / relative_path\n                target_path.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(file_path, 'r', encoding='utf-8') as src:\n                    content = src.read()\n                \n                with open(target_path, 'w', encoding='utf-8') as dst:\n                    dst.write(content)\n        \n        return True\n\nclass CodeGenerationTool(Tool):\n    \"\"\"代码生成工具 - 基于自然语言生成代码\"\"\"\n\n    name = \"code_generation\"\n    description = \"根据自然语言描述生成代码，支持多种编程语言\"\n    inputs = {\n        \"description\": {\n            \"type\": \"string\",\n            \"description\": \"代码功能的自然语言描述\"\n        },\n        \"language\": {\n            \"type\": \"string\",\n            \"description\": \"编程语言（python, javascript, java, cpp 等）\",\n            \"nullable\": True\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"相关上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, description: str, language: str = \"python\", context: str = \"\") -> str:\n        \"\"\"生成代码\"\"\"\n        # 搜索相关历史经验\n        similar_conversations = self.memory_manager.search_conversations(description, limit=3)\n\n        # 获取用户偏好\n        user_preferences = self.memory_manager.retrieve_knowledge(\"user_preferences\") or {}\n\n        # 构建增强的提示\n        enhanced_prompt = f\"\"\"\n基于以下描述生成 {language} 代码：\n\n需求描述：{description}\n\n上下文信息：{context}\n\n用户偏好：{json.dumps(user_preferences, ensure_ascii=False, indent=2)}\n\n相关历史经验：\n{json.dumps(similar_conversations, ensure_ascii=False, indent=2)}\n\n请生成清晰、可读、符合最佳实践的代码，并包含必要的注释。\n\"\"\"\n\n        # 这里应该调用 LLM 生成代码，暂时返回模板\n        generated_code = f\"\"\"\n# 根据描述生成的 {language} 代码\n# 需求：{description}\n\n# TODO: 实际的代码生成逻辑\ndef generated_function():\n    '''\n    {description}\n    '''\n    pass\n\"\"\"\n\n        # 存储生成的代码模式\n        self.memory_manager.store_knowledge(\n            \"code_patterns\",\n            f\"{language}_{hashlib.md5(description.encode()).hexdigest()[:8]}\",\n            {\n                \"description\": description,\n                \"language\": language,\n                \"code\": generated_code,\n                \"context\": context\n            }\n        )\n\n        return generated_code\n\nclass FileOperationTool(Tool):\n    \"\"\"文件操作工具 - 处理文件读写、创建、删除等操作\"\"\"\n\n    name = \"file_operation\"\n    description = \"执行文件操作：创建、读取、写入、删除、移动文件\"\n    inputs = {\n        \"operation\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：create, read, write, delete, move, list\"\n        },\n        \"file_path\": {\n            \"type\": \"string\",\n            \"description\": \"文件路径\"\n        },\n        \"content\": {\n            \"type\": \"string\",\n            \"description\": \"文件内容（用于 write 操作）\",\n            \"nullable\": True\n        },\n        \"target_path\": {\n            \"type\": \"string\",\n            \"description\": \"目标路径（用于 move 操作）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, operation: str, file_path: str, content: str = \"\", target_path: str = \"\") -> str:\n        \"\"\"执行文件操作\"\"\"\n        try:\n            path = Path(file_path)\n\n            if operation == \"create\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n                path.touch()\n                return f\"文件 {file_path} 创建成功\"\n\n            elif operation == \"read\":\n                if not path.exists():\n                    return f\"错误：文件 {file_path} 不存在\"\n\n                with open(path, 'r', encoding='utf-8') as f:\n                    file_content = f.read()\n\n                # 存储文件访问记录\n                self.memory_manager.store_knowledge(\n                    \"file_access\",\n                    str(path),\n                    {\"last_read\": datetime.now().isoformat(), \"size\": len(file_content)}\n                )\n\n                return file_content\n\n            elif operation == \"write\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n\n                with open(path, 'w', encoding='utf-8') as f:\n                    f.write(content)\n\n                # 存储文件修改记录\n                self.memory_manager.store_knowledge(\n                    \"file_modifications\",\n                    str(path),\n                    {\n                        \"last_modified\": datetime.now().isoformat(),\n                        \"content_hash\": hashlib.md5(content.encode()).hexdigest()\n                    }\n                )\n\n                return f\"内容已写入文件 {file_path}\"\n\n            elif operation == \"delete\":\n                if path.exists():\n                    if path.is_file():\n                        path.unlink()\n                        return f\"文件 {file_path} 删除成功\"\n                    elif path.is_dir():\n                        import shutil\n                        shutil.rmtree(path)\n                        return f\"目录 {file_path} 删除成功\"\n                else:\n                    return f\"错误：路径 {file_path} 不存在\"\n\n            elif operation == \"move\":\n                if not path.exists():\n                    return f\"错误：源文件 {file_path} 不存在\"\n\n                target = Path(target_path)\n                target.parent.mkdir(parents=True, exist_ok=True)\n                path.rename(target)\n                return f\"文件从 {file_path} 移动到 {target_path}\"\n\n            elif operation == \"list\":\n                if path.is_dir():\n                    files = [str(p) for p in path.iterdir()]\n                    return f\"目录 {file_path} 内容：\\n\" + \"\\n\".join(files)\n                else:\n                    return f\"错误：{file_path} 不是目录\"\n\n            else:\n                return f\"不支持的操作：{operation}\"\n\n        except Exception as e:\n            error_msg = f\"文件操作失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"file_op_{int(time.time())}\",\n                {\n                    \"operation\": operation,\n                    \"file_path\": file_path,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\nclass CommandExecutionTool(Tool):\n    \"\"\"命令执行工具 - 安全执行系统命令\"\"\"\n\n    name = \"command_execution\"\n    description = \"执行系统命令，支持安全检查和结果记录\"\n    inputs = {\n        \"command\": {\n            \"type\": \"string\",\n            \"description\": \"要执行的命令\"\n        },\n        \"working_dir\": {\n            \"type\": \"string\",\n            \"description\": \"工作目录\",\n            \"nullable\": True\n        },\n        \"timeout\": {\n            \"type\": \"number\",\n            \"description\": \"超时时间（秒）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n        # 危险命令黑名单\n        self.dangerous_commands = [\n            \"rm -rf\", \"del /f\", \"format\", \"fdisk\", \"mkfs\",\n            \"shutdown\", \"reboot\", \"halt\", \"poweroff\",\n            \"chmod 777\", \"chown\", \"sudo rm\", \"dd if=\"\n        ]\n\n    def _is_safe_command(self, command: str) -> bool:\n        \"\"\"检查命令是否安全\"\"\"\n        command_lower = command.lower()\n\n        for dangerous in self.dangerous_commands:\n            if dangerous in command_lower:\n                return False\n\n        return True\n\n    def forward(self, command: str, working_dir: str = \"\", timeout: float = 30.0) -> str:\n        \"\"\"执行命令\"\"\"\n        # 安全检查\n        if not self._is_safe_command(command):\n            error_msg = f\"危险命令被阻止：{command}\"\n            self.memory_manager.store_knowledge(\n                \"security_blocks\",\n                f\"cmd_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"reason\": \"dangerous_command\",\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n            return error_msg\n\n        try:\n            # 设置工作目录\n            cwd = Path(working_dir) if working_dir else Path.cwd()\n\n            # 执行命令\n            result = subprocess.run(\n                command,\n                shell=True,\n                cwd=cwd,\n                capture_output=True,\n                text=True,\n                timeout=timeout\n            )\n\n            # 构建结果\n            output = f\"命令执行完成\\n\"\n            output += f\"返回码: {result.returncode}\\n\"\n\n            if result.stdout:\n                output += f\"标准输出:\\n{result.stdout}\\n\"\n\n            if result.stderr:\n                output += f\"标准错误:\\n{result.stderr}\\n\"\n\n            # 记录命令执行\n            self.memory_manager.store_knowledge(\n                \"command_history\",\n                f\"cmd_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"working_dir\": str(cwd),\n                    \"return_code\": result.returncode,\n                    \"stdout\": result.stdout,\n                    \"stderr\": result.stderr,\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return output\n\n        except subprocess.TimeoutExpired:\n            return f\"命令执行超时（{timeout}秒）：{command}\"\n\n        except Exception as e:\n            error_msg = f\"命令执行失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"cmd_error_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\nclass TaskDecompositionTool(Tool):\n    \"\"\"任务分解工具 - 将复杂任务分解为子任务\"\"\"\n\n    name = \"task_decomposition\"\n    description = \"将复杂任务分解为可执行的子任务序列\"\n    inputs = {\n        \"task_description\": {\n            \"type\": \"string\",\n            \"description\": \"复杂任务的描述\"\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"任务上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, task_description: str, context: str = \"\") -> str:\n        \"\"\"分解任务\"\"\"\n        # 搜索相似任务的历史分解\n        similar_tasks = self.memory_manager.search_conversations(task_description, limit=3)\n\n        # 获取已知的项目结构模式\n        project_patterns = self.memory_manager.retrieve_knowledge(\"project_structures\") or {}\n\n        # 基于规则的任务分解（简化版本）\n        subtasks = self._decompose_task(task_description, context)\n\n        # 格式化输出\n        result = f\"任务分解结果：{task_description}\\n\\n\"\n        result += \"子任务序列：\\n\"\n\n        for i, subtask in enumerate(subtasks, 1):\n            result += f\"{i}. {subtask['name']}\\n\"\n            result += f\"   描述: {subtask['description']}\\n\"\n            result += f\"   工具: {subtask['tools']}\\n\"\n            result += f\"   预估时间: {subtask['estimated_time']}\\n\\n\"\n\n        # 存储任务分解结果\n        self.memory_manager.store_knowledge(\n            \"task_decompositions\",\n            hashlib.md5(task_description.encode()).hexdigest()[:8],\n            {\n                \"original_task\": task_description,\n                \"context\": context,\n                \"subtasks\": subtasks,\n                \"timestamp\": datetime.now().isoformat()\n            }\n        )\n\n        return result\n\n    def _decompose_task(self, task_description: str, context: str) -> List[Dict]:\n        \"\"\"基于规则的任务分解\"\"\"\n        subtasks = []\n\n        # 分析任务类型\n        task_lower = task_description.lower()\n\n        if \"创建\" in task_lower or \"开发\" in task_lower:\n            if \"网站\" in task_lower or \"web\" in task_lower:\n                subtasks.extend(self._web_development_tasks())\n            elif \"api\" in task_lower:\n                subtasks.extend(self._api_development_tasks())\n            elif \"数据分析\" in task_lower:\n                subtasks.extend(self._data_analysis_tasks())\n            else:\n                subtasks.extend(self._general_development_tasks())\n\n        elif \"分析\" in task_lower:\n            subtasks.extend(self._analysis_tasks())\n\n        elif \"测试\" in task_lower:\n            subtasks.extend(self._testing_tasks())\n\n        else:\n            # 通用任务分解\n            subtasks.extend(self._general_tasks(task_description))\n\n        return subtasks\n\n    def _web_development_tasks(self) -> List[Dict]:\n        \"\"\"Web 开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"项目初始化\",\n                \"description\": \"创建项目目录结构和配置文件\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"前端开发\",\n                \"description\": \"创建HTML、CSS、JavaScript文件\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"后端开发\",\n                \"description\": \"实现服务器端逻辑\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"45分钟\"\n            },\n            {\n                \"name\": \"测试和部署\",\n                \"description\": \"运行测试并部署应用\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _api_development_tasks(self) -> List[Dict]:\n        \"\"\"API 开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"API设计\",\n                \"description\": \"设计API接口和数据模型\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"20分钟\"\n            },\n            {\n                \"name\": \"实现API端点\",\n                \"description\": \"编写API路由和处理函数\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"40分钟\"\n            },\n            {\n                \"name\": \"添加文档\",\n                \"description\": \"生成API文档\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _data_analysis_tasks(self) -> List[Dict]:\n        \"\"\"数据分析任务分解\"\"\"\n        return [\n            {\n                \"name\": \"数据收集\",\n                \"description\": \"获取和加载数据\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"数据清洗\",\n                \"description\": \"处理缺失值和异常数据\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"25分钟\"\n            },\n            {\n                \"name\": \"数据分析\",\n                \"description\": \"执行统计分析和可视化\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"生成报告\",\n                \"description\": \"创建分析报告\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"20分钟\"\n            }\n        ]\n\n    def _general_development_tasks(self) -> List[Dict]:\n        \"\"\"通用开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"需求分析\",\n                \"description\": \"分析和理解需求\",\n                \"tools\": [],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"代码实现\",\n                \"description\": \"编写核心功能代码\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"40分钟\"\n            },\n            {\n                \"name\": \"测试验证\",\n                \"description\": \"测试功能正确性\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _analysis_tasks(self) -> List[Dict]:\n        \"\"\"分析任务分解\"\"\"\n        return [\n            {\n                \"name\": \"数据收集\",\n                \"description\": \"收集相关数据和信息\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"数据处理\",\n                \"description\": \"清洗和预处理数据\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"20分钟\"\n            },\n            {\n                \"name\": \"分析计算\",\n                \"description\": \"执行分析算法\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"25分钟\"\n            },\n            {\n                \"name\": \"结果展示\",\n                \"description\": \"可视化和报告生成\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"20分钟\"\n            }\n        ]\n\n    def _testing_tasks(self) -> List[Dict]:\n        \"\"\"测试任务分解\"\"\"\n        return [\n            {\n                \"name\": \"测试计划\",\n                \"description\": \"制定测试策略和计划\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"编写测试\",\n                \"description\": \"创建测试用例和测试代码\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"执行测试\",\n                \"description\": \"运行测试并收集结果\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"测试报告\",\n                \"description\": \"生成测试报告\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            }\n        ]\n\n    def _general_tasks(self, task_description: str) -> List[Dict]:\n        \"\"\"通用任务分解\"\"\"\n        return [\n            {\n                \"name\": \"任务准备\",\n                \"description\": f\"准备执行任务：{task_description}\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"5分钟\"\n            },\n            {\n                \"name\": \"主要执行\",\n                \"description\": \"执行主要任务逻辑\",\n                \"tools\": [\"code_generation\", \"file_operation\", \"command_execution\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"结果整理\",\n                \"description\": \"整理和验证结果\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            }\n        ]\n\nclass CheckpointTool(Tool):\n    \"\"\"检查点管理工具 - 管理工作区状态检查点\"\"\"\n\n    name = \"checkpoint_management\"\n    description = \"创建、列出、恢复工作区检查点\"\n    inputs = {\n        \"operation\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：create, list, restore\"\n        },\n        \"checkpoint_name\": {\n            \"type\": \"string\",\n            \"description\": \"检查点名称\",\n            \"nullable\": True\n        },\n        \"description\": {\n            \"type\": \"string\",\n            \"description\": \"检查点描述\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, checkpoint_manager: CheckpointManager):\n        super().__init__()\n        self.checkpoint_manager = checkpoint_manager\n\n    def forward(self, operation: str, checkpoint_name: str = \"\", description: str = \"\") -> str:\n        \"\"\"执行检查点操作\"\"\"\n        try:\n            if operation == \"create\":\n                if not checkpoint_name:\n                    checkpoint_name = f\"auto_{int(time.time())}\"\n\n                checkpoint_id = self.checkpoint_manager.create_checkpoint(\n                    checkpoint_name, description\n                )\n                return f\"检查点创建成功：{checkpoint_id}\"\n\n            elif operation == \"list\":\n                checkpoints = self.checkpoint_manager.list_checkpoints()\n\n                if not checkpoints:\n                    return \"没有找到检查点\"\n\n                result = \"可用检查点：\\n\"\n                for cp in checkpoints:\n                    result += f\"- {cp['id']}: {cp['name']}\\n\"\n                    result += f\"  描述: {cp['description']}\\n\"\n                    result += f\"  时间: {cp['timestamp']}\\n\\n\"\n\n                return result\n\n            elif operation == \"restore\":\n                if not checkpoint_name:\n                    return \"错误：需要指定检查点名称\"\n\n                success = self.checkpoint_manager.restore_checkpoint(checkpoint_name)\n\n                if success:\n                    return f\"检查点恢复成功：{checkpoint_name}\"\n                else:\n                    return f\"检查点恢复失败：{checkpoint_name}\"\n\n            else:\n                return f\"不支持的操作：{operation}\"\n\n        except Exception as e:\n            return f\"检查点操作失败：{str(e)}\"\n\nclass IntelligentCodeAgent:\n    \"\"\"智能代码编写助手 - 主要的 Agent 类\"\"\"\n\n    def __init__(self, api_key: str = None, model_name: str = \"deepseek-chat\"):\n        \"\"\"初始化 CodeAgent\"\"\"\n\n        # 初始化记忆和检查点管理\n        self.memory_manager = MemoryManager()\n        self.checkpoint_manager = CheckpointManager(self.memory_manager)\n\n        # 初始化工具\n        self.tools = [\n            PythonInterpreterTool(),\n            CodeGenerationTool(self.memory_manager),\n            FileOperationTool(self.memory_manager),\n            CommandExecutionTool(self.memory_manager),\n            TaskDecompositionTool(self.memory_manager),\n            CheckpointTool(self.checkpoint_manager)\n        ]\n\n        # 初始化模型\n        self.model = self._setup_model(api_key, model_name)\n\n        # 创建 smolagents CodeAgent\n        self.agent = CodeAgent(\n            tools=self.tools,\n            model=self.model,\n            stream_outputs=True\n        )\n\n        # 系统提示词\n        self.system_prompt = self._build_system_prompt()\n\n        print(\"🤖 智能代码助手已初始化完成！\")\n        print(\"💡 支持功能：代码生成、文件操作、命令执行、任务分解、记忆管理\")\n\n    def _setup_model(self, api_key: str, model_name: str):\n        \"\"\"设置模型\"\"\"\n        try:\n            # 使用 LiteLLM 支持 DeepSeek\n            model = LiteLLMModel(\n                model_id=model_name,\n                api_key=api_key or os.environ.get(\"DEEPSEEK_API_KEY\"),\n                api_base=\"https://api.deepseek.com/v1\",\n                temperature=0.1,\n                max_tokens=4000\n            )\n            return model\n        except Exception as e:\n            print(f\"⚠️ 模型初始化失败，使用默认配置: {e}\")\n            # 回退到默认模型\n            from smolagents import InferenceClientModel\n            return InferenceClientModel(\n                model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n                provider=\"auto\"\n            )\n\n    def _build_system_prompt(self) -> str:\n        \"\"\"构建系统提示词\"\"\"\n        return \"\"\"\n你是一个专业的代码编写助手，具有以下能力：\n\n🎯 核心功能：\n1. 代码生成：根据自然语言描述生成高质量代码\n2. 文件操作：创建、读取、修改、删除文件和目录\n3. 命令执行：安全执行系统命令和脚本\n4. 任务分解：将复杂任务分解为可执行的子任务\n5. 记忆管理：记住用户偏好和历史交互\n6. 检查点管理：创建和恢复工作区状态\n\n🛠️ 可用工具：\n- python_interpreter: Python 代码执行\n- code_generation: 代码生成\n- file_operation: 文件操作\n- command_execution: 命令执行\n- task_decomposition: 任务分解\n- checkpoint_management: 检查点管理\n\n💡 工作原则：\n1. 安全第一：避免执行危险命令\n2. 代码质量：生成清晰、可读、符合最佳实践的代码\n3. 用户体验：提供详细的解释和建议\n4. 持续学习：从交互中学习用户偏好\n5. 状态管理：适时创建检查点保护工作成果\n\n🔄 工作流程：\n1. 理解用户需求\n2. 分解复杂任务（如需要）\n3. 选择合适的工具\n4. 执行操作\n5. 验证结果\n6. 记录经验\n\n请始终以专业、友好的方式协助用户完成编程任务。\n\"\"\"\n", "hash": "1264b6de21f59241ba776b60bc183414", "size": 34885}, ".history/codeAgent_20250720143709.py": {"content": "\"\"\"\nCodeAgent - 基于 smolagents 的智能代码编写助手\n集成 DeepSeek 模型，实现从自然语言到代码生成的全流程自动化\n\n主要功能：\n- 自然语言理解与代码生成\n- 文件操作与项目管理\n- 命令执行与环境管理\n- 任务分解与多工具协同\n- 记忆管理与检查点机制\n\"\"\"\n\nimport os\nimport json\nimport time\nimport hashlib\nimport subprocess\nfrom datetime import datetime\nfrom typing import Dict, List, Any, Optional\nfrom pathlib import Path\n\nfrom smolagents import CodeAgent, Tool, LiteLLMModel\nfrom smolagents import PythonInterpreterTool\n\nclass MemoryManager:\n    \"\"\"记忆管理系统 - 实现持久化记忆与检索\"\"\"\n    \n    def __init__(self, memory_dir: str = \".codeagent_memory\"):\n        self.memory_dir = Path(memory_dir)\n        self.memory_dir.mkdir(exist_ok=True)\n        \n        # 记忆文件路径\n        self.conversation_file = self.memory_dir / \"conversations.json\"\n        self.knowledge_file = self.memory_dir / \"knowledge_base.json\"\n        self.checkpoints_dir = self.memory_dir / \"checkpoints\"\n        self.checkpoints_dir.mkdir(exist_ok=True)\n        \n        # 初始化记忆存储\n        self._init_memory_files()\n    \n    def _init_memory_files(self):\n        \"\"\"初始化记忆文件\"\"\"\n        if not self.conversation_file.exists():\n            self._save_json(self.conversation_file, {\"conversations\": []})\n        \n        if not self.knowledge_file.exists():\n            self._save_json(self.knowledge_file, {\n                \"code_patterns\": {},\n                \"project_structures\": {},\n                \"user_preferences\": {},\n                \"learned_solutions\": {}\n            })\n    \n    def _save_json(self, file_path: Path, data: Dict):\n        \"\"\"保存 JSON 数据\"\"\"\n        with open(file_path, 'w', encoding='utf-8') as f:\n            json.dump(data, f, ensure_ascii=False, indent=2)\n    \n    def _load_json(self, file_path: Path) -> Dict:\n        \"\"\"加载 JSON 数据\"\"\"\n        try:\n            with open(file_path, 'r', encoding='utf-8') as f:\n                return json.load(f)\n        except (FileNotFoundError, json.JSONDecodeError):\n            return {}\n    \n    def store_conversation(self, user_input: str, agent_response: str, context: Dict = None):\n        \"\"\"存储对话记录\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        conversation_entry = {\n            \"timestamp\": datetime.now().isoformat(),\n            \"user_input\": user_input,\n            \"agent_response\": agent_response,\n            \"context\": context or {}\n        }\n        \n        conversations[\"conversations\"].append(conversation_entry)\n        \n        # 保持最近 100 条对话\n        if len(conversations[\"conversations\"]) > 100:\n            conversations[\"conversations\"] = conversations[\"conversations\"][-100:]\n        \n        self._save_json(self.conversation_file, conversations)\n    \n    def store_knowledge(self, category: str, key: str, value: Any):\n        \"\"\"存储知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            knowledge[category] = {}\n        \n        knowledge[category][key] = {\n            \"value\": value,\n            \"timestamp\": datetime.now().isoformat()\n        }\n        \n        self._save_json(self.knowledge_file, knowledge)\n    \n    def retrieve_knowledge(self, category: str, key: str = None) -> Any:\n        \"\"\"检索知识\"\"\"\n        knowledge = self._load_json(self.knowledge_file)\n        \n        if category not in knowledge:\n            return None\n        \n        if key is None:\n            return knowledge[category]\n        \n        return knowledge[category].get(key, {}).get(\"value\")\n    \n    def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:\n        \"\"\"搜索相关对话\"\"\"\n        conversations = self._load_json(self.conversation_file)\n        \n        relevant_conversations = []\n        query_lower = query.lower()\n        \n        for conv in conversations.get(\"conversations\", []):\n            if (query_lower in conv[\"user_input\"].lower() or \n                query_lower in conv[\"agent_response\"].lower()):\n                relevant_conversations.append(conv)\n        \n        return relevant_conversations[-limit:]\n\nclass CheckpointManager:\n    \"\"\"检查点管理系统 - 实现工作区状态管理\"\"\"\n    \n    def __init__(self, memory_manager: MemoryManager):\n        self.memory_manager = memory_manager\n        self.checkpoints_dir = memory_manager.checkpoints_dir\n    \n    def create_checkpoint(self, name: str, description: str = \"\") -> str:\n        \"\"\"创建检查点\"\"\"\n        checkpoint_id = f\"{name}_{int(time.time())}\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_dir.mkdir(exist_ok=True)\n        \n        # 保存当前工作目录状态\n        current_files = self._get_current_files()\n        \n        checkpoint_data = {\n            \"id\": checkpoint_id,\n            \"name\": name,\n            \"description\": description,\n            \"timestamp\": datetime.now().isoformat(),\n            \"files\": current_files,\n            \"working_directory\": str(Path.cwd())\n        }\n        \n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        self.memory_manager._save_json(checkpoint_file, checkpoint_data)\n        \n        # 复制重要文件\n        self._backup_files(checkpoint_dir, current_files)\n        \n        return checkpoint_id\n    \n    def _get_current_files(self) -> Dict[str, str]:\n        \"\"\"获取当前目录的文件信息\"\"\"\n        files_info = {}\n        current_dir = Path.cwd()\n        \n        for file_path in current_dir.rglob(\"*\"):\n            if file_path.is_file() and not self._should_ignore_file(file_path):\n                relative_path = file_path.relative_to(current_dir)\n                try:\n                    with open(file_path, 'r', encoding='utf-8') as f:\n                        content = f.read()\n                    files_info[str(relative_path)] = {\n                        \"content\": content,\n                        \"hash\": hashlib.md5(content.encode()).hexdigest(),\n                        \"size\": file_path.stat().st_size\n                    }\n                except (UnicodeDecodeError, PermissionError):\n                    # 跳过二进制文件或无权限文件\n                    files_info[str(relative_path)] = {\n                        \"content\": \"[BINARY_FILE]\",\n                        \"hash\": \"binary\",\n                        \"size\": file_path.stat().st_size\n                    }\n        \n        return files_info\n    \n    def _should_ignore_file(self, file_path: Path) -> bool:\n        \"\"\"判断是否应该忽略文件\"\"\"\n        ignore_patterns = [\n            \".git\", \"__pycache__\", \".pytest_cache\", \"node_modules\",\n            \".codeagent_memory\", \"*.pyc\", \"*.pyo\", \"*.pyd\", \".DS_Store\"\n        ]\n        \n        path_str = str(file_path)\n        for pattern in ignore_patterns:\n            if pattern in path_str:\n                return True\n        \n        return False\n    \n    def _backup_files(self, checkpoint_dir: Path, files_info: Dict):\n        \"\"\"备份文件到检查点目录\"\"\"\n        files_dir = checkpoint_dir / \"files\"\n        files_dir.mkdir(exist_ok=True)\n        \n        for file_path, info in files_info.items():\n            if info[\"content\"] != \"[BINARY_FILE]\":\n                backup_file = files_dir / file_path\n                backup_file.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(backup_file, 'w', encoding='utf-8') as f:\n                    f.write(info[\"content\"])\n    \n    def list_checkpoints(self) -> List[Dict]:\n        \"\"\"列出所有检查点\"\"\"\n        checkpoints = []\n        \n        for checkpoint_dir in self.checkpoints_dir.iterdir():\n            if checkpoint_dir.is_dir():\n                checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n                if checkpoint_file.exists():\n                    checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n                    checkpoints.append(checkpoint_data)\n        \n        return sorted(checkpoints, key=lambda x: x[\"timestamp\"], reverse=True)\n    \n    def restore_checkpoint(self, checkpoint_id: str) -> bool:\n        \"\"\"恢复检查点\"\"\"\n        checkpoint_dir = self.checkpoints_dir / checkpoint_id\n        checkpoint_file = checkpoint_dir / \"checkpoint.json\"\n        \n        if not checkpoint_file.exists():\n            return False\n        \n        checkpoint_data = self.memory_manager._load_json(checkpoint_file)\n        files_dir = checkpoint_dir / \"files\"\n        \n        if not files_dir.exists():\n            return False\n        \n        # 恢复文件\n        for file_path in files_dir.rglob(\"*\"):\n            if file_path.is_file():\n                relative_path = file_path.relative_to(files_dir)\n                target_path = Path.cwd() / relative_path\n                target_path.parent.mkdir(parents=True, exist_ok=True)\n                \n                with open(file_path, 'r', encoding='utf-8') as src:\n                    content = src.read()\n                \n                with open(target_path, 'w', encoding='utf-8') as dst:\n                    dst.write(content)\n        \n        return True\n\nclass CodeGenerationTool(Tool):\n    \"\"\"代码生成工具 - 基于自然语言生成代码\"\"\"\n\n    name = \"code_generation\"\n    description = \"根据自然语言描述生成代码，支持多种编程语言\"\n    inputs = {\n        \"description\": {\n            \"type\": \"string\",\n            \"description\": \"代码功能的自然语言描述\"\n        },\n        \"language\": {\n            \"type\": \"string\",\n            \"description\": \"编程语言（python, javascript, java, cpp 等）\",\n            \"nullable\": True\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"相关上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, description: str, language: str = \"python\", context: str = \"\") -> str:\n        \"\"\"生成代码\"\"\"\n        # 搜索相关历史经验\n        similar_conversations = self.memory_manager.search_conversations(description, limit=3)\n\n        # 获取用户偏好\n        user_preferences = self.memory_manager.retrieve_knowledge(\"user_preferences\") or {}\n\n        # 构建增强的提示\n        enhanced_prompt = f\"\"\"\n基于以下描述生成 {language} 代码：\n\n需求描述：{description}\n\n上下文信息：{context}\n\n用户偏好：{json.dumps(user_preferences, ensure_ascii=False, indent=2)}\n\n相关历史经验：\n{json.dumps(similar_conversations, ensure_ascii=False, indent=2)}\n\n请生成清晰、可读、符合最佳实践的代码，并包含必要的注释。\n\"\"\"\n\n        # 这里应该调用 LLM 生成代码，暂时返回模板\n        generated_code = f\"\"\"\n# 根据描述生成的 {language} 代码\n# 需求：{description}\n\n# TODO: 实际的代码生成逻辑\ndef generated_function():\n    '''\n    {description}\n    '''\n    pass\n\"\"\"\n\n        # 存储生成的代码模式\n        self.memory_manager.store_knowledge(\n            \"code_patterns\",\n            f\"{language}_{hashlib.md5(description.encode()).hexdigest()[:8]}\",\n            {\n                \"description\": description,\n                \"language\": language,\n                \"code\": generated_code,\n                \"context\": context\n            }\n        )\n\n        return generated_code\n\nclass FileOperationTool(Tool):\n    \"\"\"文件操作工具 - 处理文件读写、创建、删除等操作\"\"\"\n\n    name = \"file_operation\"\n    description = \"执行文件操作：创建、读取、写入、删除、移动文件\"\n    inputs = {\n        \"operation\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：create, read, write, delete, move, list\"\n        },\n        \"file_path\": {\n            \"type\": \"string\",\n            \"description\": \"文件路径\"\n        },\n        \"content\": {\n            \"type\": \"string\",\n            \"description\": \"文件内容（用于 write 操作）\",\n            \"nullable\": True\n        },\n        \"target_path\": {\n            \"type\": \"string\",\n            \"description\": \"目标路径（用于 move 操作）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, operation: str, file_path: str, content: str = \"\", target_path: str = \"\") -> str:\n        \"\"\"执行文件操作\"\"\"\n        try:\n            path = Path(file_path)\n\n            if operation == \"create\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n                path.touch()\n                return f\"文件 {file_path} 创建成功\"\n\n            elif operation == \"read\":\n                if not path.exists():\n                    return f\"错误：文件 {file_path} 不存在\"\n\n                with open(path, 'r', encoding='utf-8') as f:\n                    file_content = f.read()\n\n                # 存储文件访问记录\n                self.memory_manager.store_knowledge(\n                    \"file_access\",\n                    str(path),\n                    {\"last_read\": datetime.now().isoformat(), \"size\": len(file_content)}\n                )\n\n                return file_content\n\n            elif operation == \"write\":\n                path.parent.mkdir(parents=True, exist_ok=True)\n\n                with open(path, 'w', encoding='utf-8') as f:\n                    f.write(content)\n\n                # 存储文件修改记录\n                self.memory_manager.store_knowledge(\n                    \"file_modifications\",\n                    str(path),\n                    {\n                        \"last_modified\": datetime.now().isoformat(),\n                        \"content_hash\": hashlib.md5(content.encode()).hexdigest()\n                    }\n                )\n\n                return f\"内容已写入文件 {file_path}\"\n\n            elif operation == \"delete\":\n                if path.exists():\n                    if path.is_file():\n                        path.unlink()\n                        return f\"文件 {file_path} 删除成功\"\n                    elif path.is_dir():\n                        import shutil\n                        shutil.rmtree(path)\n                        return f\"目录 {file_path} 删除成功\"\n                else:\n                    return f\"错误：路径 {file_path} 不存在\"\n\n            elif operation == \"move\":\n                if not path.exists():\n                    return f\"错误：源文件 {file_path} 不存在\"\n\n                target = Path(target_path)\n                target.parent.mkdir(parents=True, exist_ok=True)\n                path.rename(target)\n                return f\"文件从 {file_path} 移动到 {target_path}\"\n\n            elif operation == \"list\":\n                if path.is_dir():\n                    files = [str(p) for p in path.iterdir()]\n                    return f\"目录 {file_path} 内容：\\n\" + \"\\n\".join(files)\n                else:\n                    return f\"错误：{file_path} 不是目录\"\n\n            else:\n                return f\"不支持的操作：{operation}\"\n\n        except Exception as e:\n            error_msg = f\"文件操作失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"file_op_{int(time.time())}\",\n                {\n                    \"operation\": operation,\n                    \"file_path\": file_path,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\nclass CommandExecutionTool(Tool):\n    \"\"\"命令执行工具 - 安全执行系统命令\"\"\"\n\n    name = \"command_execution\"\n    description = \"执行系统命令，支持安全检查和结果记录\"\n    inputs = {\n        \"command\": {\n            \"type\": \"string\",\n            \"description\": \"要执行的命令\"\n        },\n        \"working_dir\": {\n            \"type\": \"string\",\n            \"description\": \"工作目录\",\n            \"nullable\": True\n        },\n        \"timeout\": {\n            \"type\": \"number\",\n            \"description\": \"超时时间（秒）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n        # 危险命令黑名单\n        self.dangerous_commands = [\n            \"rm -rf\", \"del /f\", \"format\", \"fdisk\", \"mkfs\",\n            \"shutdown\", \"reboot\", \"halt\", \"poweroff\",\n            \"chmod 777\", \"chown\", \"sudo rm\", \"dd if=\"\n        ]\n\n    def _is_safe_command(self, command: str) -> bool:\n        \"\"\"检查命令是否安全\"\"\"\n        command_lower = command.lower()\n\n        for dangerous in self.dangerous_commands:\n            if dangerous in command_lower:\n                return False\n\n        return True\n\n    def forward(self, command: str, working_dir: str = \"\", timeout: float = 30.0) -> str:\n        \"\"\"执行命令\"\"\"\n        # 安全检查\n        if not self._is_safe_command(command):\n            error_msg = f\"危险命令被阻止：{command}\"\n            self.memory_manager.store_knowledge(\n                \"security_blocks\",\n                f\"cmd_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"reason\": \"dangerous_command\",\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n            return error_msg\n\n        try:\n            # 设置工作目录\n            cwd = Path(working_dir) if working_dir else Path.cwd()\n\n            # 执行命令\n            result = subprocess.run(\n                command,\n                shell=True,\n                cwd=cwd,\n                capture_output=True,\n                text=True,\n                timeout=timeout\n            )\n\n            # 构建结果\n            output = f\"命令执行完成\\n\"\n            output += f\"返回码: {result.returncode}\\n\"\n\n            if result.stdout:\n                output += f\"标准输出:\\n{result.stdout}\\n\"\n\n            if result.stderr:\n                output += f\"标准错误:\\n{result.stderr}\\n\"\n\n            # 记录命令执行\n            self.memory_manager.store_knowledge(\n                \"command_history\",\n                f\"cmd_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"working_dir\": str(cwd),\n                    \"return_code\": result.returncode,\n                    \"stdout\": result.stdout,\n                    \"stderr\": result.stderr,\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return output\n\n        except subprocess.TimeoutExpired:\n            return f\"命令执行超时（{timeout}秒）：{command}\"\n\n        except Exception as e:\n            error_msg = f\"命令执行失败：{str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"operation_errors\",\n                f\"cmd_error_{int(time.time())}\",\n                {\n                    \"command\": command,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\nclass TaskDecompositionTool(Tool):\n    \"\"\"任务分解工具 - 将复杂任务分解为子任务\"\"\"\n\n    name = \"task_decomposition\"\n    description = \"将复杂任务分解为可执行的子任务序列\"\n    inputs = {\n        \"task_description\": {\n            \"type\": \"string\",\n            \"description\": \"复杂任务的描述\"\n        },\n        \"context\": {\n            \"type\": \"string\",\n            \"description\": \"任务上下文信息\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, memory_manager: MemoryManager):\n        super().__init__()\n        self.memory_manager = memory_manager\n\n    def forward(self, task_description: str, context: str = \"\") -> str:\n        \"\"\"分解任务\"\"\"\n        # 搜索相似任务的历史分解\n        similar_tasks = self.memory_manager.search_conversations(task_description, limit=3)\n\n        # 获取已知的项目结构模式\n        project_patterns = self.memory_manager.retrieve_knowledge(\"project_structures\") or {}\n\n        # 基于规则的任务分解（简化版本）\n        subtasks = self._decompose_task(task_description, context)\n\n        # 格式化输出\n        result = f\"任务分解结果：{task_description}\\n\\n\"\n        result += \"子任务序列：\\n\"\n\n        for i, subtask in enumerate(subtasks, 1):\n            result += f\"{i}. {subtask['name']}\\n\"\n            result += f\"   描述: {subtask['description']}\\n\"\n            result += f\"   工具: {subtask['tools']}\\n\"\n            result += f\"   预估时间: {subtask['estimated_time']}\\n\\n\"\n\n        # 存储任务分解结果\n        self.memory_manager.store_knowledge(\n            \"task_decompositions\",\n            hashlib.md5(task_description.encode()).hexdigest()[:8],\n            {\n                \"original_task\": task_description,\n                \"context\": context,\n                \"subtasks\": subtasks,\n                \"timestamp\": datetime.now().isoformat()\n            }\n        )\n\n        return result\n\n    def _decompose_task(self, task_description: str, context: str) -> List[Dict]:\n        \"\"\"基于规则的任务分解\"\"\"\n        subtasks = []\n\n        # 分析任务类型\n        task_lower = task_description.lower()\n\n        if \"创建\" in task_lower or \"开发\" in task_lower:\n            if \"网站\" in task_lower or \"web\" in task_lower:\n                subtasks.extend(self._web_development_tasks())\n            elif \"api\" in task_lower:\n                subtasks.extend(self._api_development_tasks())\n            elif \"数据分析\" in task_lower:\n                subtasks.extend(self._data_analysis_tasks())\n            else:\n                subtasks.extend(self._general_development_tasks())\n\n        elif \"分析\" in task_lower:\n            subtasks.extend(self._analysis_tasks())\n\n        elif \"测试\" in task_lower:\n            subtasks.extend(self._testing_tasks())\n\n        else:\n            # 通用任务分解\n            subtasks.extend(self._general_tasks(task_description))\n\n        return subtasks\n\n    def _web_development_tasks(self) -> List[Dict]:\n        \"\"\"Web 开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"项目初始化\",\n                \"description\": \"创建项目目录结构和配置文件\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"前端开发\",\n                \"description\": \"创建HTML、CSS、JavaScript文件\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"后端开发\",\n                \"description\": \"实现服务器端逻辑\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"45分钟\"\n            },\n            {\n                \"name\": \"测试和部署\",\n                \"description\": \"运行测试并部署应用\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _api_development_tasks(self) -> List[Dict]:\n        \"\"\"API 开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"API设计\",\n                \"description\": \"设计API接口和数据模型\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"20分钟\"\n            },\n            {\n                \"name\": \"实现API端点\",\n                \"description\": \"编写API路由和处理函数\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"40分钟\"\n            },\n            {\n                \"name\": \"添加文档\",\n                \"description\": \"生成API文档\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _data_analysis_tasks(self) -> List[Dict]:\n        \"\"\"数据分析任务分解\"\"\"\n        return [\n            {\n                \"name\": \"数据收集\",\n                \"description\": \"获取和加载数据\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"数据清洗\",\n                \"description\": \"处理缺失值和异常数据\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"25分钟\"\n            },\n            {\n                \"name\": \"数据分析\",\n                \"description\": \"执行统计分析和可视化\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"生成报告\",\n                \"description\": \"创建分析报告\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"20分钟\"\n            }\n        ]\n\n    def _general_development_tasks(self) -> List[Dict]:\n        \"\"\"通用开发任务分解\"\"\"\n        return [\n            {\n                \"name\": \"需求分析\",\n                \"description\": \"分析和理解需求\",\n                \"tools\": [],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"代码实现\",\n                \"description\": \"编写核心功能代码\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"40分钟\"\n            },\n            {\n                \"name\": \"测试验证\",\n                \"description\": \"测试功能正确性\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            }\n        ]\n\n    def _analysis_tasks(self) -> List[Dict]:\n        \"\"\"分析任务分解\"\"\"\n        return [\n            {\n                \"name\": \"数据收集\",\n                \"description\": \"收集相关数据和信息\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"数据处理\",\n                \"description\": \"清洗和预处理数据\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"20分钟\"\n            },\n            {\n                \"name\": \"分析计算\",\n                \"description\": \"执行分析算法\",\n                \"tools\": [\"code_generation\"],\n                \"estimated_time\": \"25分钟\"\n            },\n            {\n                \"name\": \"结果展示\",\n                \"description\": \"可视化和报告生成\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"20分钟\"\n            }\n        ]\n\n    def _testing_tasks(self) -> List[Dict]:\n        \"\"\"测试任务分解\"\"\"\n        return [\n            {\n                \"name\": \"测试计划\",\n                \"description\": \"制定测试策略和计划\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            },\n            {\n                \"name\": \"编写测试\",\n                \"description\": \"创建测试用例和测试代码\",\n                \"tools\": [\"code_generation\", \"file_operation\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"执行测试\",\n                \"description\": \"运行测试并收集结果\",\n                \"tools\": [\"command_execution\"],\n                \"estimated_time\": \"15分钟\"\n            },\n            {\n                \"name\": \"测试报告\",\n                \"description\": \"生成测试报告\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            }\n        ]\n\n    def _general_tasks(self, task_description: str) -> List[Dict]:\n        \"\"\"通用任务分解\"\"\"\n        return [\n            {\n                \"name\": \"任务准备\",\n                \"description\": f\"准备执行任务：{task_description}\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"5分钟\"\n            },\n            {\n                \"name\": \"主要执行\",\n                \"description\": \"执行主要任务逻辑\",\n                \"tools\": [\"code_generation\", \"file_operation\", \"command_execution\"],\n                \"estimated_time\": \"30分钟\"\n            },\n            {\n                \"name\": \"结果整理\",\n                \"description\": \"整理和验证结果\",\n                \"tools\": [\"file_operation\"],\n                \"estimated_time\": \"10分钟\"\n            }\n        ]\n\nclass CheckpointTool(Tool):\n    \"\"\"检查点管理工具 - 管理工作区状态检查点\"\"\"\n\n    name = \"checkpoint_management\"\n    description = \"创建、列出、恢复工作区检查点\"\n    inputs = {\n        \"operation\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：create, list, restore\"\n        },\n        \"checkpoint_name\": {\n            \"type\": \"string\",\n            \"description\": \"检查点名称\",\n            \"nullable\": True\n        },\n        \"description\": {\n            \"type\": \"string\",\n            \"description\": \"检查点描述\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n\n    def __init__(self, checkpoint_manager: CheckpointManager):\n        super().__init__()\n        self.checkpoint_manager = checkpoint_manager\n\n    def forward(self, operation: str, checkpoint_name: str = \"\", description: str = \"\") -> str:\n        \"\"\"执行检查点操作\"\"\"\n        try:\n            if operation == \"create\":\n                if not checkpoint_name:\n                    checkpoint_name = f\"auto_{int(time.time())}\"\n\n                checkpoint_id = self.checkpoint_manager.create_checkpoint(\n                    checkpoint_name, description\n                )\n                return f\"检查点创建成功：{checkpoint_id}\"\n\n            elif operation == \"list\":\n                checkpoints = self.checkpoint_manager.list_checkpoints()\n\n                if not checkpoints:\n                    return \"没有找到检查点\"\n\n                result = \"可用检查点：\\n\"\n                for cp in checkpoints:\n                    result += f\"- {cp['id']}: {cp['name']}\\n\"\n                    result += f\"  描述: {cp['description']}\\n\"\n                    result += f\"  时间: {cp['timestamp']}\\n\\n\"\n\n                return result\n\n            elif operation == \"restore\":\n                if not checkpoint_name:\n                    return \"错误：需要指定检查点名称\"\n\n                success = self.checkpoint_manager.restore_checkpoint(checkpoint_name)\n\n                if success:\n                    return f\"检查点恢复成功：{checkpoint_name}\"\n                else:\n                    return f\"检查点恢复失败：{checkpoint_name}\"\n\n            else:\n                return f\"不支持的操作：{operation}\"\n\n        except Exception as e:\n            return f\"检查点操作失败：{str(e)}\"\n\nclass IntelligentCodeAgent:\n    \"\"\"智能代码编写助手 - 主要的 Agent 类\"\"\"\n\n    def __init__(self, api_key: str = None, model_name: str = \"deepseek-chat\"):\n        \"\"\"初始化 CodeAgent\"\"\"\n\n        # 初始化记忆和检查点管理\n        self.memory_manager = MemoryManager()\n        self.checkpoint_manager = CheckpointManager(self.memory_manager)\n\n        # 初始化工具\n        self.tools = [\n            PythonInterpreterTool(),\n            CodeGenerationTool(self.memory_manager),\n            FileOperationTool(self.memory_manager),\n            CommandExecutionTool(self.memory_manager),\n            TaskDecompositionTool(self.memory_manager),\n            CheckpointTool(self.checkpoint_manager)\n        ]\n\n        # 初始化模型\n        self.model = self._setup_model(api_key, model_name)\n\n        # 创建 smolagents CodeAgent\n        self.agent = CodeAgent(\n            tools=self.tools,\n            model=self.model,\n            stream_outputs=True\n        )\n\n        # 系统提示词\n        self.system_prompt = self._build_system_prompt()\n\n        print(\"🤖 智能代码助手已初始化完成！\")\n        print(\"💡 支持功能：代码生成、文件操作、命令执行、任务分解、记忆管理\")\n\n    def _setup_model(self, api_key: str, model_name: str):\n        \"\"\"设置模型\"\"\"\n        try:\n            # 使用 LiteLLM 支持 DeepSeek\n            model = LiteLLMModel(\n                model_id=model_name,\n                api_key=api_key or os.environ.get(\"DEEPSEEK_API_KEY\"),\n                api_base=\"https://api.deepseek.com/v1\",\n                temperature=0.1,\n                max_tokens=4000\n            )\n            return model\n        except Exception as e:\n            print(f\"⚠️ 模型初始化失败，使用默认配置: {e}\")\n            # 回退到默认模型\n            from smolagents import InferenceClientModel\n            return InferenceClientModel(\n                model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n                provider=\"auto\"\n            )\n\n    def _build_system_prompt(self) -> str:\n        \"\"\"构建系统提示词\"\"\"\n        return \"\"\"\n你是一个专业的代码编写助手，具有以下能力：\n\n🎯 核心功能：\n1. 代码生成：根据自然语言描述生成高质量代码\n2. 文件操作：创建、读取、修改、删除文件和目录\n3. 命令执行：安全执行系统命令和脚本\n4. 任务分解：将复杂任务分解为可执行的子任务\n5. 记忆管理：记住用户偏好和历史交互\n6. 检查点管理：创建和恢复工作区状态\n\n🛠️ 可用工具：\n- python_interpreter: Python 代码执行\n- code_generation: 代码生成\n- file_operation: 文件操作\n- command_execution: 命令执行\n- task_decomposition: 任务分解\n- checkpoint_management: 检查点管理\n\n💡 工作原则：\n1. 安全第一：避免执行危险命令\n2. 代码质量：生成清晰、可读、符合最佳实践的代码\n3. 用户体验：提供详细的解释和建议\n4. 持续学习：从交互中学习用户偏好\n5. 状态管理：适时创建检查点保护工作成果\n\n🔄 工作流程：\n1. 理解用户需求\n2. 分解复杂任务（如需要）\n3. 选择合适的工具\n4. 执行操作\n5. 验证结果\n6. 记录经验\n\n请始终以专业、友好的方式协助用户完成编程任务。\n\"\"\"\n\n    def run(self, user_input: str, auto_checkpoint: bool = True) -> str:\n        \"\"\"运行用户请求\"\"\"\n        try:\n            # 记录开始时间\n            start_time = time.time()\n\n            # 搜索相关历史记录\n            relevant_history = self.memory_manager.search_conversations(user_input, limit=3)\n\n            # 构建增强的输入\n            enhanced_input = self._enhance_input(user_input, relevant_history)\n\n            # 自动创建检查点（如果启用）\n            if auto_checkpoint:\n                checkpoint_id = self.checkpoint_manager.create_checkpoint(\n                    f\"before_{int(time.time())}\",\n                    f\"执行前检查点: {user_input[:50]}...\"\n                )\n                print(f\"📍 已创建检查点: {checkpoint_id}\")\n\n            # 执行任务\n            result = self.agent.run(enhanced_input)\n\n            # 计算执行时间\n            execution_time = time.time() - start_time\n\n            # 存储对话记录\n            self.memory_manager.store_conversation(\n                user_input,\n                result,\n                {\n                    \"execution_time\": execution_time,\n                    \"tools_used\": [tool.name for tool in self.tools],\n                    \"checkpoint_created\": auto_checkpoint\n                }\n            )\n\n            # 学习用户偏好\n            self._learn_from_interaction(user_input, result)\n\n            print(f\"⏱️ 执行时间: {execution_time:.2f}秒\")\n\n            return result\n\n        except Exception as e:\n            error_msg = f\"执行失败: {str(e)}\"\n\n            # 记录错误\n            self.memory_manager.store_knowledge(\n                \"execution_errors\",\n                f\"error_{int(time.time())}\",\n                {\n                    \"user_input\": user_input,\n                    \"error\": str(e),\n                    \"timestamp\": datetime.now().isoformat()\n                }\n            )\n\n            return error_msg\n\n    def _enhance_input(self, user_input: str, relevant_history: List[Dict]) -> str:\n        \"\"\"增强用户输入\"\"\"\n        enhanced = f\"{self.system_prompt}\\n\\n\"\n\n        if relevant_history:\n            enhanced += \"📚 相关历史记录:\\n\"\n            for i, conv in enumerate(relevant_history, 1):\n                enhanced += f\"{i}. 用户: {conv['user_input'][:100]}...\\n\"\n                enhanced += f\"   助手: {conv['agent_response'][:100]}...\\n\\n\"\n\n        # 获取用户偏好\n        user_prefs = self.memory_manager.retrieve_knowledge(\"user_preferences\") or {}\n        if user_prefs:\n            enhanced += f\"👤 用户偏好: {json.dumps(user_prefs, ensure_ascii=False)}\\n\\n\"\n\n        enhanced += f\"🎯 当前任务: {user_input}\\n\\n\"\n        enhanced += \"请根据上述信息和你的能力，高效完成用户的请求。\"\n\n        return enhanced\n\n    def _learn_from_interaction(self, user_input: str, result: str):\n        \"\"\"从交互中学习\"\"\"\n        # 分析用户偏好\n        input_lower = user_input.lower()\n\n        # 编程语言偏好\n        languages = [\"python\", \"javascript\", \"java\", \"cpp\", \"go\", \"rust\"]\n        for lang in languages:\n            if lang in input_lower:\n                self.memory_manager.store_knowledge(\n                    \"user_preferences\",\n                    \"preferred_language\",\n                    lang\n                )\n                break\n\n        # 代码风格偏好\n        if \"注释\" in user_input or \"comment\" in input_lower:\n            self.memory_manager.store_knowledge(\n                \"user_preferences\",\n                \"wants_comments\",\n                True\n            )\n\n        if \"测试\" in user_input or \"test\" in input_lower:\n            self.memory_manager.store_knowledge(\n                \"user_preferences\",\n                \"wants_tests\",\n                True\n            )\n\n    def get_status(self) -> Dict:\n        \"\"\"获取助手状态\"\"\"\n        conversations = self.memory_manager._load_json(self.memory_manager.conversation_file)\n        checkpoints = self.checkpoint_manager.list_checkpoints()\n\n        return {\n            \"conversations_count\": len(conversations.get(\"conversations\", [])),\n            \"checkpoints_count\": len(checkpoints),\n            \"tools_count\": len(self.tools),\n            \"memory_dir\": str(self.memory_manager.memory_dir),\n            \"model_info\": {\n                \"type\": type(self.model).__name__,\n                \"model_id\": getattr(self.model, 'model_id', 'unknown')\n            }\n        }\n\n    def clear_memory(self, confirm: bool = False):\n        \"\"\"清除记忆（谨慎使用）\"\"\"\n        if not confirm:\n            print(\"⚠️ 此操作将清除所有记忆数据，请使用 clear_memory(confirm=True) 确认\")\n            return\n\n        import shutil\n        if self.memory_manager.memory_dir.exists():\n            shutil.rmtree(self.memory_manager.memory_dir)\n            self.memory_manager._init_memory_files()\n            print(\"🗑️ 记忆数据已清除\")\n\n    def export_memory(self, export_path: str = \"memory_export.json\"):\n        \"\"\"导出记忆数据\"\"\"\n        conversations = self.memory_manager._load_json(self.memory_manager.conversation_file)\n        knowledge = self.memory_manager._load_json(self.memory_manager.knowledge_file)\n        checkpoints = self.checkpoint_manager.list_checkpoints()\n\n        export_data = {\n            \"conversations\": conversations,\n            \"knowledge\": knowledge,\n            \"checkpoints\": checkpoints,\n            \"export_time\": datetime.now().isoformat()\n        }\n\n        with open(export_path, 'w', encoding='utf-8') as f:\n            json.dump(export_data, f, ensure_ascii=False, indent=2)\n\n        print(f\"📤 记忆数据已导出到: {export_path}\")\n\n# 示例使用和测试函数\ndef demo_code_agent():\n    \"\"\"演示 CodeAgent 的使用\"\"\"\n    print(\"🚀 CodeAgent 演示开始\\n\")\n\n    # 创建 CodeAgent 实例\n    agent = IntelligentCodeAgent()\n\n    # 显示状态\n    status = agent.get_status()\n    print(f\"📊 助手状态: {json.dumps(status, ensure_ascii=False, indent=2)}\\n\")\n\n    # 示例任务\n    demo_tasks = [\n        \"创建一个简单的 Python 函数来计算斐波那契数列\",\n        \"分析当前目录的文件结构\",\n        \"创建一个检查点\",\n        \"生成一个简单的 Web API 项目结构\"\n    ]\n\n    for i, task in enumerate(demo_tasks, 1):\n        print(f\"🎯 任务 {i}: {task}\")\n        try:\n            result = agent.run(task)\n            print(f\"✅ 结果: {result[:200]}...\\n\")\n        except Exception as e:\n            print(f\"❌ 错误: {e}\\n\")\n\n    print(\"🎉 演示完成！\")\n\nif __name__ == \"__main__\":\n    # 运行演示\n    demo_code_agent()\n", "hash": "af5a777e16bcd31d2932ad202a5984ce", "size": 41605}, ".history/learn_20250720132155.py": {"content": "\"\"\"\nsmolagents 学习项目\n基于 Hugging Face smolagents 库的 Agent 开发示例\n\n这是一个完整的 smolagents 学习项目，包含：\n- 基础 Agent 使用\n- 自定义工具开发\n- 多 Agent 协作\n- 安全执行环境配置\n\"\"\"\n\n# 主学习文件 - 项目概览和快速开始指南\n\ndef main():\n    \"\"\"主函数 - 展示项目概览\"\"\"\n    print(\"🤖 欢迎来到 smolagents 学习项目！\\n\")\n\n    print(\"📚 项目内容:\")\n    print(\"1. 基础 Agent 示例 - examples/basic_agent.py\")\n    print(\"2. 自定义工具开发 - examples/custom_tools.py\")\n    print(\"3. 多 Agent 协作 - examples/multi_agent.py\")\n    print(\"4. 安全执行环境 - examples/secure_execution.py\")\n    print(\"5. 简单演示 - simple_demo.py\")\n\n    print(\"\\n🛠️ 自定义工具:\")\n    print(\"- WeatherTool: 天气查询工具\")\n    print(\"- DataAnalysisTool: 数据分析工具\")\n    print(\"- TimerTool: 计时器工具\")\n    print(\"- FileManagerTool: 文件管理工具\")\n\n    print(\"\\n🚀 快速开始:\")\n    print(\"1. 运行简单演示: python simple_demo.py\")\n    print(\"2. 测试自定义工具: python examples/custom_tools.py\")\n    print(\"3. 体验多 Agent 协作: python examples/multi_agent.py\")\n\n    print(\"\\n💡 提示:\")\n    print(\"- 设置 HUGGINGFACE_TOKEN 环境变量以使用外部模型\")\n    print(\"- 查看 README.md 了解详细说明\")\n    print(\"- 参考 config/agent_config.py 进行配置\")\n\ndef show_smolagents_overview():\n    \"\"\"展示 smolagents 概览\"\"\"\n    print(\"\\n🏗️ smolagents 架构:\")\n    print(\"Agent (智能代理) + Model (语言模型) + Tools (工具) + Executor (执行器)\")\n\n    print(\"\\n🔥 核心特性:\")\n    print(\"✨ 代码优先的 Agent 设计\")\n    print(\"🧑‍💻 支持多种语言模型\")\n    print(\"🛠️ 丰富的工具生态\")\n    print(\"🔒 安全的执行环境\")\n    print(\"🤗 Hub 集成\")\n\nif __name__ == \"__main__\":\n    main()\n    show_smolagents_overview()", "hash": "a80fe5d86ef75658d4381014f1f2785c", "size": 1939}, "examples/secure_execution.py": {"content": "\"\"\"\n安全执行环境示例\n展示如何配置和使用安全的代码执行环境（沙箱）\n\"\"\"\n\nimport os\nimport sys\nsys.path.append(os.path.join(os.path.dirname(__file__), '..'))\n\nfrom smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool\n\ndef demo_local_execution():\n    \"\"\"演示本地执行环境\"\"\"\n    print(\"🖥️ === 本地执行环境 ===\\n\")\n    \n    print(\"本地执行特点:\")\n    print(\"✅ 速度快，无网络延迟\")\n    print(\"✅ 完全控制执行环境\")\n    print(\"⚠️ 安全风险：代码直接在本地运行\")\n    print(\"⚠️ 需要谨慎处理不可信代码\\n\")\n    \n    # 创建使用本地执行器的 Agent\n    try:\n        model = InferenceClientModel(\n            model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n            token=os.environ.get(\"HUGGINGFACE_TOKEN\"),\n            provider=\"auto\"\n        )\n        \n        agent = CodeAgent(\n            tools=[PythonInterpreterTool()],\n            model=model,\n            executor_type=\"local\",  # 明确指定本地执行\n            stream_outputs=False\n        )\n        \n        print(\"🧪 测试本地执行:\")\n        result = agent.run(\"计算 1+1 并显示 Python 版本信息\")\n        print(f\"结果: {result}\\n\")\n        \n    except Exception as e:\n        print(f\"❌ 本地执行测试失败: {e}\\n\")\n\ndef demo_docker_execution():\n    \"\"\"演示 Docker 执行环境\"\"\"\n    print(\"🐳 === Docker 执行环境 ===\\n\")\n    \n    print(\"Docker 执行特点:\")\n    print(\"✅ 容器隔离，安全性高\")\n    print(\"✅ 环境一致性好\")\n    print(\"✅ 可以限制资源使用\")\n    print(\"⚠️ 需要安装 Docker\")\n    print(\"⚠️ 启动时间稍长\\n\")\n    \n    # Docker 配置示例\n    docker_config = {\n        \"image\": \"python:3.11-slim\",\n        \"timeout\": 30,\n        \"memory_limit\": \"512m\",\n        \"cpu_limit\": \"1.0\"\n    }\n    \n    print(\"Docker 配置示例:\")\n    for key, value in docker_config.items():\n        print(f\"  {key}: {value}\")\n    \n    print(\"\\n创建 Docker Agent 的代码:\")\n    code_example = '''\n    from smolagents import CodeAgent, DockerExecutor\n    \n    # 配置 Docker 执行器\n    docker_executor = DockerExecutor(\n        image=\"python:3.11-slim\",\n        timeout=30,\n        memory_limit=\"512m\"\n    )\n    \n    agent = CodeAgent(\n        tools=[PythonInterpreterTool()],\n        model=model,\n        executor_type=\"docker\",\n        executor_kwargs={\n            \"image\": \"python:3.11-slim\",\n            \"timeout\": 30\n        }\n    )\n    '''\n    print(code_example)\n\ndef demo_e2b_execution():\n    \"\"\"演示 E2B 执行环境\"\"\"\n    print(\"☁️ === E2B 云端执行环境 ===\\n\")\n    \n    print(\"E2B 执行特点:\")\n    print(\"✅ 云端沙箱，完全隔离\")\n    print(\"✅ 无需本地配置\")\n    print(\"✅ 支持多种编程语言\")\n    print(\"✅ 实时协作功能\")\n    print(\"⚠️ 需要网络连接\")\n    print(\"⚠️ 需要 E2B API 密钥\\n\")\n    \n    print(\"E2B 配置步骤:\")\n    steps = [\n        \"1. 注册 E2B 账户: https://e2b.dev/\",\n        \"2. 获取 API 密钥\",\n        \"3. 设置环境变量: export E2B_API_KEY='your_key'\",\n        \"4. 安装 E2B SDK: pip install e2b\",\n        \"5. 配置 Agent 使用 E2B 执行器\"\n    ]\n    \n    for step in steps:\n        print(f\"  {step}\")\n    \n    print(\"\\n创建 E2B Agent 的代码:\")\n    code_example = '''\n    from smolagents import CodeAgent, E2BExecutor\n    \n    agent = CodeAgent(\n        tools=[PythonInterpreterTool()],\n        model=model,\n        executor_type=\"e2b\",\n        executor_kwargs={\n            \"api_key\": os.environ.get(\"E2B_API_KEY\"),\n            \"template\": \"python3\"\n        }\n    )\n    '''\n    print(code_example)\n\ndef demo_security_considerations():\n    \"\"\"演示安全考虑因素\"\"\"\n    print(\"🔒 === 安全考虑因素 ===\\n\")\n    \n    security_guide = \"\"\"\n    代码执行安全最佳实践:\n    \n    🚨 高风险操作:\n    ├── 文件系统访问 (读写敏感文件)\n    ├── 网络请求 (访问内部服务)\n    ├── 系统命令执行 (os.system, subprocess)\n    ├── 导入危险模块 (eval, exec, __import__)\n    └── 资源消耗 (无限循环, 大内存分配)\n    \n    🛡️ 防护措施:\n    ├── 沙箱执行 (Docker, E2B)\n    ├── 资源限制 (CPU, 内存, 时间)\n    ├── 网络隔离 (禁止外网访问)\n    ├── 文件系统限制 (只读挂载)\n    └── 模块白名单 (限制可导入模块)\n    \n    📋 安全检查清单:\n    ├── ✅ 使用沙箱环境\n    ├── ✅ 设置执行超时\n    ├── ✅ 限制内存使用\n    ├── ✅ 监控系统调用\n    ├── ✅ 记录执行日志\n    └── ✅ 定期安全审计\n    \"\"\"\n    \n    print(security_guide)\n\ndef demo_execution_comparison():\n    \"\"\"比较不同执行环境\"\"\"\n    print(\"⚖️ === 执行环境比较 ===\\n\")\n    \n    comparison = \"\"\"\n    执行环境对比:\n    \n    特性          | 本地执行 | Docker  | E2B云端\n    -------------|---------|---------|--------\n    安全性        | ⭐⭐     | ⭐⭐⭐⭐  | ⭐⭐⭐⭐⭐\n    性能          | ⭐⭐⭐⭐⭐ | ⭐⭐⭐    | ⭐⭐⭐\n    配置复杂度     | ⭐      | ⭐⭐⭐    | ⭐⭐\n    网络依赖      | 无      | 无      | 必需\n    资源隔离      | 无      | 完全    | 完全\n    多语言支持     | 有限    | 完全    | 完全\n    \n    推荐使用场景:\n    \n    🏠 本地执行:\n    ├── 开发测试阶段\n    ├── 可信代码执行\n    └── 性能要求极高的场景\n    \n    🐳 Docker 执行:\n    ├── 生产环境部署\n    ├── 需要环境一致性\n    └── 有本地 Docker 环境\n    \n    ☁️ E2B 执行:\n    ├── 云端应用\n    ├── 多用户共享\n    └── 无需维护基础设施\n    \"\"\"\n    \n    print(comparison)\n\ndef demo_practical_examples():\n    \"\"\"实际应用示例\"\"\"\n    print(\"💼 === 实际应用示例 ===\\n\")\n    \n    examples = \"\"\"\n    实际应用场景:\n    \n    🎓 教育平台:\n    ├── 在线编程练习\n    ├── 代码自动评测\n    └── 安全的学生代码执行\n    → 推荐: E2B (多用户隔离)\n    \n    🏢 企业应用:\n    ├── 数据分析脚本执行\n    ├── 自动化任务处理\n    └── 内部工具开发\n    → 推荐: Docker (安全可控)\n    \n    🔬 研究开发:\n    ├── 算法原型验证\n    ├── 数据处理实验\n    └── 模型训练脚本\n    → 推荐: 本地执行 (快速迭代)\n    \n    🌐 SaaS 服务:\n    ├── 用户自定义脚本\n    ├── API 数据处理\n    └── 多租户代码执行\n    → 推荐: E2B (云端扩展)\n    \"\"\"\n    \n    print(examples)\n\nif __name__ == \"__main__\":\n    print(\"🔒 欢迎使用 smolagents 安全执行环境示例！\\n\")\n    \n    # 演示不同执行环境\n    demo_local_execution()\n    demo_docker_execution()\n    demo_e2b_execution()\n    \n    # 安全指南\n    demo_security_considerations()\n    demo_execution_comparison()\n    demo_practical_examples()\n    \n    print(\"\\n💡 下一步:\")\n    print(\"1. 根据需求选择合适的执行环境\")\n    print(\"2. 配置相应的安全参数\")\n    print(\"3. 测试代码执行的安全性\")\n    print(\"4. 监控和审计执行日志\")\n    print(\"\\n🔗 相关资源:\")\n    print(\"- E2B 官网: https://e2b.dev/\")\n    print(\"- Docker 文档: https://docs.docker.com/\")\n    print(\"- smolagents 安全指南: https://huggingface.co/docs/smolagents\")\n", "hash": "1b2ca55686b7331308370c1faf7a349b", "size": 7477}, "examples/basic_agent.py": {"content": "\"\"\"\n基础 smolagents Agent 示例\n展示如何创建和使用 CodeAgent\n\"\"\"\n\nimport os\nfrom smolagents import CodeAgent, InferenceClientModel, WebSearchTool, PythonInterpreterTool\n\ndef create_basic_agent():\n    \"\"\"创建一个基础的 CodeAgent\"\"\"\n    \n    # 1. 选择模型 - 使用 Hugging Face Inference API\n    model = InferenceClientModel(\n        model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n        token=os.environ.get(\"HUGGINGFACE_TOKEN\")  # 可选，用于访问私有模型\n    )\n    \n    # 2. 选择工具\n    tools = [\n        WebSearchTool(),           # 网络搜索工具\n        PythonInterpreterTool(),   # Python 解释器工具\n    ]\n    \n    # 3. 创建 Agent\n    agent = CodeAgent(\n        tools=tools,\n        model=model,\n        max_iterations=10,\n        stream_outputs=True,\n        verbose=True\n    )\n    \n    return agent\n\ndef demo_basic_tasks():\n    \"\"\"演示基础任务\"\"\"\n    agent = create_basic_agent()\n    \n    print(\"=== smolagents 基础示例 ===\\n\")\n    \n    # 示例 1: 简单计算\n    print(\"📊 示例 1: 数学计算\")\n    result1 = agent.run(\"计算 123 * 456 + 789，并解释计算过程\")\n    print(f\"结果: {result1}\\n\")\n    \n    # 示例 2: 数据分析\n    print(\"📈 示例 2: 数据分析\")\n    result2 = agent.run(\"\"\"\n    创建一个包含以下数据的列表：[10, 25, 30, 45, 50, 60, 75, 80, 90, 100]\n    计算平均值、中位数和标准差，并用 matplotlib 创建一个简单的柱状图\n    \"\"\")\n    print(f\"结果: {result2}\\n\")\n    \n    # 示例 3: 网络搜索 + 数据处理\n    print(\"🔍 示例 3: 网络搜索与数据处理\")\n    result3 = agent.run(\"\"\"\n    搜索\"Python 编程语言的最新版本\"，\n    提取版本号信息，并创建一个简单的总结\n    \"\"\")\n    print(f\"结果: {result3}\\n\")\n\ndef demo_code_agent_features():\n    \"\"\"演示 CodeAgent 的特色功能\"\"\"\n    agent = create_basic_agent()\n    \n    print(\"=== CodeAgent 特色功能演示 ===\\n\")\n    \n    # 展示代码生成能力\n    print(\"💻 代码生成示例:\")\n    result = agent.run(\"\"\"\n    写一个 Python 函数来计算斐波那契数列的前 n 项，\n    然后计算前 10 项并显示结果。\n    要求函数要有适当的文档字符串和类型提示。\n    \"\"\")\n    print(f\"结果: {result}\\n\")\n\ndef demo_multi_step_reasoning():\n    \"\"\"演示多步推理能力\"\"\"\n    agent = create_basic_agent()\n    \n    print(\"=== 多步推理演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    我需要规划一个简单的数据科学项目：\n    1. 生成一些模拟数据（比如销售数据）\n    2. 进行基础的统计分析\n    3. 创建可视化图表\n    4. 得出简单的结论\n    \n    请帮我完成这个完整的流程。\n    \"\"\")\n    print(f\"结果: {result}\\n\")\n\nif __name__ == \"__main__\":\n    try:\n        print(\"🤖 欢迎使用 smolagents 基础示例！\\n\")\n        \n        # 检查是否有 Hugging Face token（可选）\n        if not os.environ.get(\"HUGGINGFACE_TOKEN\"):\n            print(\"💡 提示: 设置 HUGGINGFACE_TOKEN 环境变量可以访问更多模型\")\n            print(\"   export HUGGINGFACE_TOKEN='your_token_here'\\n\")\n        \n        # 运行示例\n        demo_basic_tasks()\n        demo_code_agent_features()\n        demo_multi_step_reasoning()\n        \n        print(\"✅ 所有示例运行完成！\")\n        \n    except Exception as e:\n        print(f\"❌ 运行出错: {e}\")\n        print(\"\\n💡 可能的解决方案:\")\n        print(\"1. 确保已安装 smolagents[toolkit]\")\n        print(\"2. 检查网络连接\")\n        print(\"3. 设置适当的环境变量\")\n", "hash": "d526d1b904d87d2b2676f737685456d0", "size": 3592}, "examples/multi_agent.py": {"content": "\"\"\"\n多 Agent 协作示例\n展示如何使用多个 Agent 协同完成复杂任务\n\"\"\"\n\nimport os\nimport sys\nsys.path.append(os.path.join(os.path.dirname(__file__), '..'))\n\nfrom smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool\nfrom tools.custom_tools import WeatherTool, DataAnalysisTool, TimerTool\n\nclass MultiAgentSystem:\n    \"\"\"多 Agent 系统\"\"\"\n    \n    def __init__(self):\n        self.agents = {}\n        self.shared_memory = {}\n        self.setup_agents()\n    \n    def setup_agents(self):\n        \"\"\"设置不同专业的 Agent\"\"\"\n        \n        # 基础模型配置\n        model_config = {\n            \"model_id\": \"Qwen/Qwen2.5-Coder-32B-Instruct\",\n            \"token\": os.environ.get(\"HUGGINGFACE_TOKEN\"),\n            \"provider\": \"auto\"\n        }\n        \n        # 1. 数据分析师 Agent\n        self.agents['data_analyst'] = CodeAgent(\n            tools=[PythonInterpreterTool(), DataAnalysisTool()],\n            model=InferenceClientModel(**model_config),\n            stream_outputs=False\n        )\n        \n        # 2. 天气专家 Agent  \n        self.agents['weather_expert'] = CodeAgent(\n            tools=[WeatherTool(), PythonInterpreterTool()],\n            model=InferenceClientModel(**model_config),\n            stream_outputs=False\n        )\n        \n        # 3. 项目管理 Agent\n        self.agents['project_manager'] = CodeAgent(\n            tools=[TimerTool(), PythonInterpreterTool()],\n            model=InferenceClientModel(**model_config),\n            stream_outputs=False\n        )\n    \n    def coordinate_task(self, task_description):\n        \"\"\"协调多个 Agent 完成任务\"\"\"\n        print(f\"🎯 任务: {task_description}\\n\")\n        \n        # 项目管理 Agent 开始计时\n        print(\"📋 项目管理 Agent: 开始任务计时\")\n        pm_result = self.agents['project_manager'].run(\"启动计时器并记录当前时间\")\n        print(f\"结果: {pm_result}\\n\")\n        \n        # 天气专家收集数据\n        print(\"🌤️ 天气专家 Agent: 收集天气数据\")\n        weather_result = self.agents['weather_expert'].run(\n            \"收集北京、上海、广州三个城市的天气数据，并提取温度信息\"\n        )\n        print(f\"结果: {weather_result}\\n\")\n        \n        # 数据分析师分析数据\n        print(\"📊 数据分析师 Agent: 分析天气数据\")\n        analysis_result = self.agents['data_analyst'].run(\n            f\"基于以下天气数据进行统计分析: {weather_result}。\"\n            \"提取温度数值，计算平均温度、最高温度、最低温度，并生成简单的可视化图表。\"\n        )\n        print(f\"结果: {analysis_result}\\n\")\n        \n        # 项目管理 Agent 结束计时\n        print(\"📋 项目管理 Agent: 结束任务计时\")\n        final_result = self.agents['project_manager'].run(\"停止计时器并报告总耗时\")\n        print(f\"结果: {final_result}\\n\")\n        \n        return {\n            \"weather_data\": weather_result,\n            \"analysis\": analysis_result,\n            \"timing\": final_result\n        }\n\ndef demo_simple_multi_agent():\n    \"\"\"简单的多 Agent 演示（不依赖外部模型）\"\"\"\n    print(\"🤖 === 简单多 Agent 协作演示 ===\\n\")\n    \n    # 直接使用工具模拟 Agent 协作\n    weather_tool = WeatherTool()\n    data_tool = DataAnalysisTool()\n    timer_tool = TimerTool()\n    \n    print(\"1️⃣ 天气专家收集数据:\")\n    cities = [\"北京\", \"上海\", \"广州\"]\n    weather_data = []\n    temperatures = []\n    \n    for city in cities:\n        weather = weather_tool.forward(city)\n        print(f\"   {weather}\")\n        # 提取温度（简单解析）\n        temp = int(weather.split(\"温度 \")[1].split(\"°C\")[0])\n        temperatures.append(temp)\n        weather_data.append(weather)\n    \n    print(f\"\\n2️⃣ 数据分析师分析温度数据: {temperatures}\")\n    analysis = data_tool.forward(str(temperatures), \"basic_stats\")\n    print(f\"   {analysis}\")\n    \n    print(\"\\n3️⃣ 项目管理计时:\")\n    timer_tool.forward(\"start\")\n    timer_tool.forward(\"sleep\", 0.5)\n    timing = timer_tool.forward(\"stop\")\n    print(f\"   {timing}\")\n    \n    print(\"\\n✅ 多 Agent 协作完成！\")\n\ndef demo_agent_specialization():\n    \"\"\"展示 Agent 专业化的概念\"\"\"\n    print(\"🎓 === Agent 专业化概念 ===\\n\")\n    \n    specializations = \"\"\"\n    Agent 专业化设计原则:\n    \n    🔬 数据分析师 Agent:\n    ├── 专业工具: DataAnalysisTool, PythonInterpreterTool\n    ├── 专长: 统计分析、数据可视化、模式识别\n    └── 输出: 分析报告、图表、洞察\n    \n    🌤️ 天气专家 Agent:\n    ├── 专业工具: WeatherTool, 地理信息工具\n    ├── 专长: 天气数据收集、气象分析、预测\n    └── 输出: 天气报告、趋势分析\n    \n    📋 项目管理 Agent:\n    ├── 专业工具: TimerTool, 任务管理工具\n    ├── 专长: 时间管理、任务协调、进度跟踪\n    └── 输出: 项目报告、时间统计\n    \n    🔄 协作模式:\n    1. 任务分解: 将复杂任务分解为专业子任务\n    2. 并行处理: 不同 Agent 同时处理不同方面\n    3. 信息传递: Agent 间共享中间结果\n    4. 结果整合: 汇总各 Agent 的输出\n    \"\"\"\n    \n    print(specializations)\n\ndef demo_communication_patterns():\n    \"\"\"展示 Agent 间通信模式\"\"\"\n    print(\"💬 === Agent 通信模式 ===\\n\")\n    \n    patterns = \"\"\"\n    常见的 Agent 通信模式:\n    \n    1. 🔄 顺序协作 (Sequential):\n       Agent A → Agent B → Agent C\n       适用于: 流水线式任务处理\n    \n    2. 🌟 中心协调 (Hub-and-Spoke):\n       Agent B ← Manager → Agent C\n                ↑\n              Agent A\n       适用于: 需要统一协调的复杂任务\n    \n    3. 🕸️ 网状协作 (Mesh):\n       Agent A ↔ Agent B\n           ↕       ↕\n       Agent C ↔ Agent D\n       适用于: 高度协作的复杂问题\n    \n    4. 📊 分层协作 (Hierarchical):\n       Manager Agent\n       ├── Specialist A\n       ├── Specialist B\n       └── Specialist C\n       适用于: 大型项目管理\n    \"\"\"\n    \n    print(patterns)\n\ndef demo_real_world_scenarios():\n    \"\"\"展示真实世界的多 Agent 应用场景\"\"\"\n    print(\"🌍 === 真实世界应用场景 ===\\n\")\n    \n    scenarios = \"\"\"\n    多 Agent 系统的实际应用:\n    \n    🏢 企业应用:\n    ├── 客服系统: 路由Agent + 专业客服Agent\n    ├── 数据分析: 收集Agent + 清洗Agent + 分析Agent\n    └── 项目管理: 规划Agent + 执行Agent + 监控Agent\n    \n    🔬 科研应用:\n    ├── 文献调研: 搜索Agent + 筛选Agent + 总结Agent\n    ├── 实验设计: 假设Agent + 设计Agent + 验证Agent\n    └── 数据处理: 预处理Agent + 分析Agent + 可视化Agent\n    \n    🎮 游戏/娱乐:\n    ├── NPC系统: 对话Agent + 行为Agent + 决策Agent\n    ├── 内容生成: 创意Agent + 编写Agent + 审核Agent\n    └── 个性化推荐: 分析Agent + 匹配Agent + 推荐Agent\n    \n    🏭 自动化:\n    ├── 智能制造: 监控Agent + 控制Agent + 优化Agent\n    ├── 供应链: 预测Agent + 采购Agent + 物流Agent\n    └── 质量控制: 检测Agent + 分析Agent + 报告Agent\n    \"\"\"\n    \n    print(scenarios)\n\nif __name__ == \"__main__\":\n    print(\"🤖 欢迎使用 smolagents 多 Agent 协作示例！\\n\")\n    \n    # 简单演示（不需要外部模型）\n    demo_simple_multi_agent()\n    \n    # 概念展示\n    demo_agent_specialization()\n    demo_communication_patterns()\n    demo_real_world_scenarios()\n    \n    print(\"\\n💡 高级用法:\")\n    print(\"1. 设置 HUGGINGFACE_TOKEN 环境变量\")\n    print(\"2. 运行完整的多 Agent 系统:\")\n    print(\"   system = MultiAgentSystem()\")\n    print(\"   system.coordinate_task('分析多城市天气数据')\")\n    print(\"3. 自定义 Agent 专业化配置\")\n    print(\"4. 实现 Agent 间的消息传递机制\")\n", "hash": "7c27bbcd9000ad0c4066e043170dc421", "size": 8038}, "examples/custom_tools.py": {"content": "\"\"\"\n自定义工具使用示例\n展示如何创建和使用自定义工具来扩展 Agent 能力\n\"\"\"\n\nimport os\nimport sys\nsys.path.append(os.path.join(os.path.dirname(__file__), '..'))\n\nfrom smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool\nfrom tools.custom_tools import WeatherTool, DataAnalysisTool, TimerTool, FileManagerTool\n\ndef create_agent_with_custom_tools():\n    \"\"\"创建带有自定义工具的 Agent\"\"\"\n    \n    # 1. 选择模型\n    model = InferenceClientModel(\n        model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n        token=os.environ.get(\"HUGGINGFACE_TOKEN\")\n    )\n    \n    # 2. 组合内置工具和自定义工具\n    tools = [\n        PythonInterpreterTool(),    # 内置工具\n        WeatherTool(),              # 自定义天气工具\n        DataAnalysisTool(),         # 自定义数据分析工具\n        TimerTool(),                # 自定义计时器工具\n        FileManagerTool(),          # 自定义文件管理工具\n    ]\n    \n    # 3. 创建 Agent\n    agent = CodeAgent(\n        tools=tools,\n        model=model,\n        max_iterations=15,\n        stream_outputs=True,\n        verbose=True\n    )\n    \n    return agent\n\ndef demo_weather_tool():\n    \"\"\"演示天气工具\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"🌤️ === 天气工具演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    请帮我查询以下城市的天气情况：\n    1. 北京\n    2. 上海\n    3. 成都（这个城市不在预设列表中）\n    \n    然后对这些城市的温度进行比较，告诉我哪个城市最热，哪个最冷。\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_data_analysis_tool():\n    \"\"\"演示数据分析工具\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"📊 === 数据分析工具演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    我有一组销售数据：[120, 150, 180, 200, 175, 190, 210, 165, 185, 195]\n    \n    请使用数据分析工具：\n    1. 计算基础统计信息\n    2. 分析数据分布\n    3. 用 Python 创建一个简单的可视化图表\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_timer_tool():\n    \"\"\"演示计时器工具\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"⏱️ === 计时器工具演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    请演示计时器工具的使用：\n    1. 获取当前时间\n    2. 启动计时器\n    3. 等待 2 秒\n    4. 停止计时器并显示耗时\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_file_manager_tool():\n    \"\"\"演示文件管理工具\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"📁 === 文件管理工具演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    请使用文件管理工具：\n    1. 创建一个名为 'test_data.txt' 的文件，内容为一些示例数据\n    2. 读取这个文件的内容\n    3. 向文件中写入新的内容\n    4. 再次读取文件确认内容已更新\n    5. 列出当前目录的所有文件\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_combined_workflow():\n    \"\"\"演示组合工作流程\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"🔄 === 组合工作流程演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    请完成一个完整的数据处理工作流程：\n    \n    1. 查询北京和上海的天气\n    2. 创建一个包含这两个城市温度数据的文件\n    3. 读取文件并对温度数据进行统计分析\n    4. 生成一个简单的温度对比图表\n    5. 将分析结果保存到新文件中\n    \n    整个过程请使用计时器记录耗时。\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_tool_creation_guide():\n    \"\"\"展示如何创建自定义工具的指南\"\"\"\n    print(\"🛠️ === 自定义工具创建指南 ===\\n\")\n    \n    guide = \"\"\"\n    创建自定义工具的步骤：\n    \n    1. 继承 smolagents.Tool 类\n    2. 定义工具属性：\n       - name: 工具名称\n       - description: 工具描述\n       - inputs: 输入参数定义\n       - output_type: 输出类型\n    \n    3. 实现 forward() 方法\n    \n    示例代码：\n    ```python\n    from smolagents import Tool\n    \n    class MyCustomTool(Tool):\n        name = \"my_tool\"\n        description = \"我的自定义工具\"\n        inputs = {\n            \"param1\": {\n                \"type\": \"string\",\n                \"description\": \"参数1的描述\"\n            }\n        }\n        output_type = \"string\"\n        \n        def forward(self, param1: str) -> str:\n            # 实现工具逻辑\n            return f\"处理结果: {param1}\"\n    ```\n    \n    4. 在 Agent 中使用：\n    ```python\n    tools = [MyCustomTool()]\n    agent = CodeAgent(tools=tools, model=model)\n    ```\n    \"\"\"\n    \n    print(guide)\n\nif __name__ == \"__main__\":\n    try:\n        print(\"🤖 欢迎使用 smolagents 自定义工具示例！\\n\")\n        \n        # 检查环境\n        if not os.environ.get(\"HUGGINGFACE_TOKEN\"):\n            print(\"💡 提示: 设置 HUGGINGFACE_TOKEN 环境变量可以访问更多模型\")\n            print(\"   export HUGGINGFACE_TOKEN='your_token_here'\\n\")\n        \n        # 展示工具创建指南\n        demo_tool_creation_guide()\n        \n        # 运行各种演示\n        demo_weather_tool()\n        demo_data_analysis_tool()\n        demo_timer_tool()\n        demo_file_manager_tool()\n        demo_combined_workflow()\n        \n        print(\"✅ 所有自定义工具演示完成！\")\n        \n    except Exception as e:\n        print(f\"❌ 运行出错: {e}\")\n        print(\"\\n💡 可能的解决方案:\")\n        print(\"1. 确保已安装 smolagents[toolkit]\")\n        print(\"2. 检查自定义工具的导入路径\")\n        print(\"3. 确认网络连接正常\")\n", "hash": "491a9132393d5096a77304938ad62584", "size": 5739}, ".history/tools/custom_tools_20250720131830.py": {"content": "\"\"\"\n自定义工具实现\n展示如何创建自己的工具来扩展 Agent 能力\n\"\"\"\n\nimport json\nimport time\nimport random\nfrom typing import Dict, List, Any\nfrom smolagents import Tool\n\nclass WeatherTool(Tool):\n    \"\"\"模拟天气查询工具\"\"\"\n    \n    name = \"weather_tool\"\n    description = \"获取指定城市的天气信息（模拟数据）\"\n    inputs = {\n        \"city\": {\n            \"type\": \"string\", \n            \"description\": \"要查询天气的城市名称\"\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, city: str) -> str:\n        \"\"\"模拟天气查询\"\"\"\n        # 模拟一些城市的天气数据\n        weather_data = {\n            \"北京\": {\"temperature\": 15, \"condition\": \"晴天\", \"humidity\": 45},\n            \"上海\": {\"temperature\": 18, \"condition\": \"多云\", \"humidity\": 60},\n            \"广州\": {\"temperature\": 25, \"condition\": \"小雨\", \"humidity\": 75},\n            \"深圳\": {\"temperature\": 26, \"condition\": \"晴天\", \"humidity\": 55},\n            \"杭州\": {\"temperature\": 20, \"condition\": \"阴天\", \"humidity\": 65},\n        }\n        \n        # 如果城市不在预设列表中，生成随机数据\n        if city not in weather_data:\n            weather_data[city] = {\n                \"temperature\": random.randint(10, 30),\n                \"condition\": random.choice([\"晴天\", \"多云\", \"阴天\", \"小雨\", \"大雨\"]),\n                \"humidity\": random.randint(30, 90)\n            }\n        \n        data = weather_data[city]\n        return f\"{city}的天气：温度 {data['temperature']}°C，{data['condition']}，湿度 {data['humidity']}%\"\n\nclass FileManagerTool(Tool):\n    \"\"\"文件管理工具\"\"\"\n    \n    name = \"file_manager\"\n    description = \"管理文件：创建、读取、写入文本文件\"\n    inputs = {\n        \"action\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：'create', 'read', 'write', 'list'\"\n        },\n        \"filename\": {\n            \"type\": \"string\", \n            \"description\": \"文件名（对于 list 操作可选）\"\n        },\n        \"content\": {\n            \"type\": \"string\",\n            \"description\": \"文件内容（仅用于 write 操作）\"\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, action: str, filename: str = \"\", content: str = \"\") -> str:\n        \"\"\"执行文件操作\"\"\"\n        try:\n            if action == \"create\":\n                with open(filename, 'w', encoding='utf-8') as f:\n                    f.write(content or \"\")\n                return f\"文件 {filename} 创建成功\"\n            \n            elif action == \"read\":\n                with open(filename, 'r', encoding='utf-8') as f:\n                    content = f.read()\n                return f\"文件 {filename} 内容：\\n{content}\"\n            \n            elif action == \"write\":\n                with open(filename, 'w', encoding='utf-8') as f:\n                    f.write(content)\n                return f\"内容已写入文件 {filename}\"\n            \n            elif action == \"list\":\n                import os\n                files = os.listdir('.')\n                return f\"当前目录文件：{', '.join(files)}\"\n            \n            else:\n                return f\"不支持的操作：{action}\"\n                \n        except Exception as e:\n            return f\"文件操作失败：{str(e)}\"\n\nclass DataAnalysisTool(Tool):\n    \"\"\"数据分析工具\"\"\"\n    \n    name = \"data_analysis\"\n    description = \"对数据进行基础统计分析\"\n    inputs = {\n        \"data\": {\n            \"type\": \"string\",\n            \"description\": \"要分析的数据，JSON 格式的数字列表\"\n        },\n        \"analysis_type\": {\n            \"type\": \"string\",\n            \"description\": \"分析类型：'basic_stats', 'distribution', 'correlation'\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, data: str, analysis_type: str = \"basic_stats\") -> str:\n        \"\"\"执行数据分析\"\"\"\n        try:\n            # 解析数据\n            numbers = json.loads(data)\n            if not isinstance(numbers, list) or not all(isinstance(x, (int, float)) for x in numbers):\n                return \"错误：数据必须是数字列表\"\n            \n            if analysis_type == \"basic_stats\":\n                mean = sum(numbers) / len(numbers)\n                sorted_nums = sorted(numbers)\n                n = len(sorted_nums)\n                median = sorted_nums[n//2] if n % 2 == 1 else (sorted_nums[n//2-1] + sorted_nums[n//2]) / 2\n                \n                variance = sum((x - mean) ** 2 for x in numbers) / len(numbers)\n                std_dev = variance ** 0.5\n                \n                return f\"\"\"基础统计分析结果：\n平均值: {mean:.2f}\n中位数: {median:.2f}\n标准差: {std_dev:.2f}\n最小值: {min(numbers)}\n最大值: {max(numbers)}\n数据点数: {len(numbers)}\"\"\"\n            \n            elif analysis_type == \"distribution\":\n                # 简单的分布分析\n                ranges = {}\n                min_val, max_val = min(numbers), max(numbers)\n                range_size = (max_val - min_val) / 5  # 分成5个区间\n                \n                for i in range(5):\n                    start = min_val + i * range_size\n                    end = min_val + (i + 1) * range_size\n                    count = sum(1 for x in numbers if start <= x < end)\n                    ranges[f\"{start:.1f}-{end:.1f}\"] = count\n                \n                result = \"数据分布分析：\\n\"\n                for range_name, count in ranges.items():\n                    result += f\"{range_name}: {count} 个数据点\\n\"\n                return result\n            \n            else:\n                return f\"不支持的分析类型：{analysis_type}\"\n                \n        except Exception as e:\n            return f\"数据分析失败：{str(e)}\"\n\nclass TimerTool(Tool):\n    \"\"\"计时器工具\"\"\"\n    \n    name = \"timer\"\n    description = \"计时器功能：开始计时、停止计时、获取当前时间\"\n    inputs = {\n        \"action\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：'start', 'stop', 'current', 'sleep'\"\n        },\n        \"seconds\": {\n            \"type\": \"number\",\n            \"description\": \"睡眠时间（仅用于 sleep 操作）\"\n        }\n    }\n    output_type = \"string\"\n    \n    def __init__(self):\n        super().__init__()\n        self.start_time = None\n    \n    def forward(self, action: str, seconds: float = 0) -> str:\n        \"\"\"执行计时操作\"\"\"\n        if action == \"start\":\n            self.start_time = time.time()\n            return f\"计时器已启动：{time.strftime('%Y-%m-%d %H:%M:%S')}\"\n        \n        elif action == \"stop\":\n            if self.start_time is None:\n                return \"计时器未启动\"\n            elapsed = time.time() - self.start_time\n            self.start_time = None\n            return f\"计时结束，耗时：{elapsed:.2f} 秒\"\n        \n        elif action == \"current\":\n            return f\"当前时间：{time.strftime('%Y-%m-%d %H:%M:%S')}\"\n        \n        elif action == \"sleep\":\n            time.sleep(seconds)\n            return f\"已等待 {seconds} 秒\"\n        \n        else:\n            return f\"不支持的操作：{action}\"\n", "hash": "90cb767926265d1fd5724b2da4b6ab5f", "size": 7287}, ".history/tools/custom_tools_20250720131854.py": {"content": "\"\"\"\n自定义工具实现\n展示如何创建自己的工具来扩展 Agent 能力\n\"\"\"\n\nimport json\nimport time\nimport random\nfrom typing import Dict, List, Any\nfrom smolagents import Tool\n\nclass WeatherTool(Tool):\n    \"\"\"模拟天气查询工具\"\"\"\n    \n    name = \"weather_tool\"\n    description = \"获取指定城市的天气信息（模拟数据）\"\n    inputs = {\n        \"city\": {\n            \"type\": \"string\", \n            \"description\": \"要查询天气的城市名称\"\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, city: str) -> str:\n        \"\"\"模拟天气查询\"\"\"\n        # 模拟一些城市的天气数据\n        weather_data = {\n            \"北京\": {\"temperature\": 15, \"condition\": \"晴天\", \"humidity\": 45},\n            \"上海\": {\"temperature\": 18, \"condition\": \"多云\", \"humidity\": 60},\n            \"广州\": {\"temperature\": 25, \"condition\": \"小雨\", \"humidity\": 75},\n            \"深圳\": {\"temperature\": 26, \"condition\": \"晴天\", \"humidity\": 55},\n            \"杭州\": {\"temperature\": 20, \"condition\": \"阴天\", \"humidity\": 65},\n        }\n        \n        # 如果城市不在预设列表中，生成随机数据\n        if city not in weather_data:\n            weather_data[city] = {\n                \"temperature\": random.randint(10, 30),\n                \"condition\": random.choice([\"晴天\", \"多云\", \"阴天\", \"小雨\", \"大雨\"]),\n                \"humidity\": random.randint(30, 90)\n            }\n        \n        data = weather_data[city]\n        return f\"{city}的天气：温度 {data['temperature']}°C，{data['condition']}，湿度 {data['humidity']}%\"\n\nclass FileManagerTool(Tool):\n    \"\"\"文件管理工具\"\"\"\n    \n    name = \"file_manager\"\n    description = \"管理文件：创建、读取、写入文本文件\"\n    inputs = {\n        \"action\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：'create', 'read', 'write', 'list'\"\n        },\n        \"filename\": {\n            \"type\": \"string\",\n            \"description\": \"文件名（对于 list 操作可选）\",\n            \"nullable\": True\n        },\n        \"content\": {\n            \"type\": \"string\",\n            \"description\": \"文件内容（仅用于 write 操作）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, action: str, filename: str = \"\", content: str = \"\") -> str:\n        \"\"\"执行文件操作\"\"\"\n        try:\n            if action == \"create\":\n                with open(filename, 'w', encoding='utf-8') as f:\n                    f.write(content or \"\")\n                return f\"文件 {filename} 创建成功\"\n            \n            elif action == \"read\":\n                with open(filename, 'r', encoding='utf-8') as f:\n                    content = f.read()\n                return f\"文件 {filename} 内容：\\n{content}\"\n            \n            elif action == \"write\":\n                with open(filename, 'w', encoding='utf-8') as f:\n                    f.write(content)\n                return f\"内容已写入文件 {filename}\"\n            \n            elif action == \"list\":\n                import os\n                files = os.listdir('.')\n                return f\"当前目录文件：{', '.join(files)}\"\n            \n            else:\n                return f\"不支持的操作：{action}\"\n                \n        except Exception as e:\n            return f\"文件操作失败：{str(e)}\"\n\nclass DataAnalysisTool(Tool):\n    \"\"\"数据分析工具\"\"\"\n    \n    name = \"data_analysis\"\n    description = \"对数据进行基础统计分析\"\n    inputs = {\n        \"data\": {\n            \"type\": \"string\",\n            \"description\": \"要分析的数据，JSON 格式的数字列表\"\n        },\n        \"analysis_type\": {\n            \"type\": \"string\",\n            \"description\": \"分析类型：'basic_stats', 'distribution', 'correlation'\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, data: str, analysis_type: str = \"basic_stats\") -> str:\n        \"\"\"执行数据分析\"\"\"\n        try:\n            # 解析数据\n            numbers = json.loads(data)\n            if not isinstance(numbers, list) or not all(isinstance(x, (int, float)) for x in numbers):\n                return \"错误：数据必须是数字列表\"\n            \n            if analysis_type == \"basic_stats\":\n                mean = sum(numbers) / len(numbers)\n                sorted_nums = sorted(numbers)\n                n = len(sorted_nums)\n                median = sorted_nums[n//2] if n % 2 == 1 else (sorted_nums[n//2-1] + sorted_nums[n//2]) / 2\n                \n                variance = sum((x - mean) ** 2 for x in numbers) / len(numbers)\n                std_dev = variance ** 0.5\n                \n                return f\"\"\"基础统计分析结果：\n平均值: {mean:.2f}\n中位数: {median:.2f}\n标准差: {std_dev:.2f}\n最小值: {min(numbers)}\n最大值: {max(numbers)}\n数据点数: {len(numbers)}\"\"\"\n            \n            elif analysis_type == \"distribution\":\n                # 简单的分布分析\n                ranges = {}\n                min_val, max_val = min(numbers), max(numbers)\n                range_size = (max_val - min_val) / 5  # 分成5个区间\n                \n                for i in range(5):\n                    start = min_val + i * range_size\n                    end = min_val + (i + 1) * range_size\n                    count = sum(1 for x in numbers if start <= x < end)\n                    ranges[f\"{start:.1f}-{end:.1f}\"] = count\n                \n                result = \"数据分布分析：\\n\"\n                for range_name, count in ranges.items():\n                    result += f\"{range_name}: {count} 个数据点\\n\"\n                return result\n            \n            else:\n                return f\"不支持的分析类型：{analysis_type}\"\n                \n        except Exception as e:\n            return f\"数据分析失败：{str(e)}\"\n\nclass TimerTool(Tool):\n    \"\"\"计时器工具\"\"\"\n    \n    name = \"timer\"\n    description = \"计时器功能：开始计时、停止计时、获取当前时间\"\n    inputs = {\n        \"action\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：'start', 'stop', 'current', 'sleep'\"\n        },\n        \"seconds\": {\n            \"type\": \"number\",\n            \"description\": \"睡眠时间（仅用于 sleep 操作）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n    \n    def __init__(self):\n        super().__init__()\n        self.start_time = None\n    \n    def forward(self, action: str, seconds: float = 0) -> str:\n        \"\"\"执行计时操作\"\"\"\n        if action == \"start\":\n            self.start_time = time.time()\n            return f\"计时器已启动：{time.strftime('%Y-%m-%d %H:%M:%S')}\"\n        \n        elif action == \"stop\":\n            if self.start_time is None:\n                return \"计时器未启动\"\n            elapsed = time.time() - self.start_time\n            self.start_time = None\n            return f\"计时结束，耗时：{elapsed:.2f} 秒\"\n        \n        elif action == \"current\":\n            return f\"当前时间：{time.strftime('%Y-%m-%d %H:%M:%S')}\"\n        \n        elif action == \"sleep\":\n            time.sleep(seconds)\n            return f\"已等待 {seconds} 秒\"\n        \n        else:\n            return f\"不支持的操作：{action}\"\n", "hash": "817ef0fc10ffa81860a27bf9883effdb", "size": 7376}, ".history/tools/custom_tools_20250720131842.py": {"content": "\"\"\"\n自定义工具实现\n展示如何创建自己的工具来扩展 Agent 能力\n\"\"\"\n\nimport json\nimport time\nimport random\nfrom typing import Dict, List, Any\nfrom smolagents import Tool\n\nclass WeatherTool(Tool):\n    \"\"\"模拟天气查询工具\"\"\"\n    \n    name = \"weather_tool\"\n    description = \"获取指定城市的天气信息（模拟数据）\"\n    inputs = {\n        \"city\": {\n            \"type\": \"string\", \n            \"description\": \"要查询天气的城市名称\"\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, city: str) -> str:\n        \"\"\"模拟天气查询\"\"\"\n        # 模拟一些城市的天气数据\n        weather_data = {\n            \"北京\": {\"temperature\": 15, \"condition\": \"晴天\", \"humidity\": 45},\n            \"上海\": {\"temperature\": 18, \"condition\": \"多云\", \"humidity\": 60},\n            \"广州\": {\"temperature\": 25, \"condition\": \"小雨\", \"humidity\": 75},\n            \"深圳\": {\"temperature\": 26, \"condition\": \"晴天\", \"humidity\": 55},\n            \"杭州\": {\"temperature\": 20, \"condition\": \"阴天\", \"humidity\": 65},\n        }\n        \n        # 如果城市不在预设列表中，生成随机数据\n        if city not in weather_data:\n            weather_data[city] = {\n                \"temperature\": random.randint(10, 30),\n                \"condition\": random.choice([\"晴天\", \"多云\", \"阴天\", \"小雨\", \"大雨\"]),\n                \"humidity\": random.randint(30, 90)\n            }\n        \n        data = weather_data[city]\n        return f\"{city}的天气：温度 {data['temperature']}°C，{data['condition']}，湿度 {data['humidity']}%\"\n\nclass FileManagerTool(Tool):\n    \"\"\"文件管理工具\"\"\"\n    \n    name = \"file_manager\"\n    description = \"管理文件：创建、读取、写入文本文件\"\n    inputs = {\n        \"action\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：'create', 'read', 'write', 'list'\"\n        },\n        \"filename\": {\n            \"type\": \"string\",\n            \"description\": \"文件名（对于 list 操作可选）\",\n            \"nullable\": True\n        },\n        \"content\": {\n            \"type\": \"string\",\n            \"description\": \"文件内容（仅用于 write 操作）\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, action: str, filename: str = \"\", content: str = \"\") -> str:\n        \"\"\"执行文件操作\"\"\"\n        try:\n            if action == \"create\":\n                with open(filename, 'w', encoding='utf-8') as f:\n                    f.write(content or \"\")\n                return f\"文件 {filename} 创建成功\"\n            \n            elif action == \"read\":\n                with open(filename, 'r', encoding='utf-8') as f:\n                    content = f.read()\n                return f\"文件 {filename} 内容：\\n{content}\"\n            \n            elif action == \"write\":\n                with open(filename, 'w', encoding='utf-8') as f:\n                    f.write(content)\n                return f\"内容已写入文件 {filename}\"\n            \n            elif action == \"list\":\n                import os\n                files = os.listdir('.')\n                return f\"当前目录文件：{', '.join(files)}\"\n            \n            else:\n                return f\"不支持的操作：{action}\"\n                \n        except Exception as e:\n            return f\"文件操作失败：{str(e)}\"\n\nclass DataAnalysisTool(Tool):\n    \"\"\"数据分析工具\"\"\"\n    \n    name = \"data_analysis\"\n    description = \"对数据进行基础统计分析\"\n    inputs = {\n        \"data\": {\n            \"type\": \"string\",\n            \"description\": \"要分析的数据，JSON 格式的数字列表\"\n        },\n        \"analysis_type\": {\n            \"type\": \"string\",\n            \"description\": \"分析类型：'basic_stats', 'distribution', 'correlation'\",\n            \"nullable\": True\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, data: str, analysis_type: str = \"basic_stats\") -> str:\n        \"\"\"执行数据分析\"\"\"\n        try:\n            # 解析数据\n            numbers = json.loads(data)\n            if not isinstance(numbers, list) or not all(isinstance(x, (int, float)) for x in numbers):\n                return \"错误：数据必须是数字列表\"\n            \n            if analysis_type == \"basic_stats\":\n                mean = sum(numbers) / len(numbers)\n                sorted_nums = sorted(numbers)\n                n = len(sorted_nums)\n                median = sorted_nums[n//2] if n % 2 == 1 else (sorted_nums[n//2-1] + sorted_nums[n//2]) / 2\n                \n                variance = sum((x - mean) ** 2 for x in numbers) / len(numbers)\n                std_dev = variance ** 0.5\n                \n                return f\"\"\"基础统计分析结果：\n平均值: {mean:.2f}\n中位数: {median:.2f}\n标准差: {std_dev:.2f}\n最小值: {min(numbers)}\n最大值: {max(numbers)}\n数据点数: {len(numbers)}\"\"\"\n            \n            elif analysis_type == \"distribution\":\n                # 简单的分布分析\n                ranges = {}\n                min_val, max_val = min(numbers), max(numbers)\n                range_size = (max_val - min_val) / 5  # 分成5个区间\n                \n                for i in range(5):\n                    start = min_val + i * range_size\n                    end = min_val + (i + 1) * range_size\n                    count = sum(1 for x in numbers if start <= x < end)\n                    ranges[f\"{start:.1f}-{end:.1f}\"] = count\n                \n                result = \"数据分布分析：\\n\"\n                for range_name, count in ranges.items():\n                    result += f\"{range_name}: {count} 个数据点\\n\"\n                return result\n            \n            else:\n                return f\"不支持的分析类型：{analysis_type}\"\n                \n        except Exception as e:\n            return f\"数据分析失败：{str(e)}\"\n\nclass TimerTool(Tool):\n    \"\"\"计时器工具\"\"\"\n    \n    name = \"timer\"\n    description = \"计时器功能：开始计时、停止计时、获取当前时间\"\n    inputs = {\n        \"action\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：'start', 'stop', 'current', 'sleep'\"\n        },\n        \"seconds\": {\n            \"type\": \"number\",\n            \"description\": \"睡眠时间（仅用于 sleep 操作）\"\n        }\n    }\n    output_type = \"string\"\n    \n    def __init__(self):\n        super().__init__()\n        self.start_time = None\n    \n    def forward(self, action: str, seconds: float = 0) -> str:\n        \"\"\"执行计时操作\"\"\"\n        if action == \"start\":\n            self.start_time = time.time()\n            return f\"计时器已启动：{time.strftime('%Y-%m-%d %H:%M:%S')}\"\n        \n        elif action == \"stop\":\n            if self.start_time is None:\n                return \"计时器未启动\"\n            elapsed = time.time() - self.start_time\n            self.start_time = None\n            return f\"计时结束，耗时：{elapsed:.2f} 秒\"\n        \n        elif action == \"current\":\n            return f\"当前时间：{time.strftime('%Y-%m-%d %H:%M:%S')}\"\n        \n        elif action == \"sleep\":\n            time.sleep(seconds)\n            return f\"已等待 {seconds} 秒\"\n        \n        else:\n            return f\"不支持的操作：{action}\"\n", "hash": "1e4add37871ff9f21f7a405cdd26a810", "size": 7346}, ".history/tools/custom_tools_20250720131302.py": {"content": "\"\"\"\n自定义工具实现\n展示如何创建自己的工具来扩展 Agent 能力\n\"\"\"\n\nimport json\nimport time\nimport random\nfrom typing import Dict, List, Any\nfrom smolagents import Tool\n\nclass WeatherTool(Tool):\n    \"\"\"模拟天气查询工具\"\"\"\n    \n    name = \"weather_tool\"\n    description = \"获取指定城市的天气信息（模拟数据）\"\n    inputs = {\n        \"city\": {\n            \"type\": \"string\", \n            \"description\": \"要查询天气的城市名称\"\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, city: str) -> str:\n        \"\"\"模拟天气查询\"\"\"\n        # 模拟一些城市的天气数据\n        weather_data = {\n            \"北京\": {\"temperature\": 15, \"condition\": \"晴天\", \"humidity\": 45},\n            \"上海\": {\"temperature\": 18, \"condition\": \"多云\", \"humidity\": 60},\n            \"广州\": {\"temperature\": 25, \"condition\": \"小雨\", \"humidity\": 75},\n            \"深圳\": {\"temperature\": 26, \"condition\": \"晴天\", \"humidity\": 55},\n            \"杭州\": {\"temperature\": 20, \"condition\": \"阴天\", \"humidity\": 65},\n        }\n        \n        # 如果城市不在预设列表中，生成随机数据\n        if city not in weather_data:\n            weather_data[city] = {\n                \"temperature\": random.randint(10, 30),\n                \"condition\": random.choice([\"晴天\", \"多云\", \"阴天\", \"小雨\", \"大雨\"]),\n                \"humidity\": random.randint(30, 90)\n            }\n        \n        data = weather_data[city]\n        return f\"{city}的天气：温度 {data['temperature']}°C，{data['condition']}，湿度 {data['humidity']}%\"\n\nclass FileManagerTool(Tool):\n    \"\"\"文件管理工具\"\"\"\n    \n    name = \"file_manager\"\n    description = \"管理文件：创建、读取、写入文本文件\"\n    inputs = {\n        \"action\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：'create', 'read', 'write', 'list'\"\n        },\n        \"filename\": {\n            \"type\": \"string\", \n            \"description\": \"文件名（对于 list 操作可选）\"\n        },\n        \"content\": {\n            \"type\": \"string\",\n            \"description\": \"文件内容（仅用于 write 操作）\"\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, action: str, filename: str = \"\", content: str = \"\") -> str:\n        \"\"\"执行文件操作\"\"\"\n        try:\n            if action == \"create\":\n                with open(filename, 'w', encoding='utf-8') as f:\n                    f.write(content or \"\")\n                return f\"文件 {filename} 创建成功\"\n            \n            elif action == \"read\":\n                with open(filename, 'r', encoding='utf-8') as f:\n                    content = f.read()\n                return f\"文件 {filename} 内容：\\n{content}\"\n            \n            elif action == \"write\":\n                with open(filename, 'w', encoding='utf-8') as f:\n                    f.write(content)\n                return f\"内容已写入文件 {filename}\"\n            \n            elif action == \"list\":\n                import os\n                files = os.listdir('.')\n                return f\"当前目录文件：{', '.join(files)}\"\n            \n            else:\n                return f\"不支持的操作：{action}\"\n                \n        except Exception as e:\n            return f\"文件操作失败：{str(e)}\"\n\nclass DataAnalysisTool(Tool):\n    \"\"\"数据分析工具\"\"\"\n    \n    name = \"data_analysis\"\n    description = \"对数据进行基础统计分析\"\n    inputs = {\n        \"data\": {\n            \"type\": \"string\",\n            \"description\": \"要分析的数据，JSON 格式的数字列表\"\n        },\n        \"analysis_type\": {\n            \"type\": \"string\",\n            \"description\": \"分析类型：'basic_stats', 'distribution', 'correlation'\"\n        }\n    }\n    output_type = \"string\"\n    \n    def forward(self, data: str, analysis_type: str = \"basic_stats\") -> str:\n        \"\"\"执行数据分析\"\"\"\n        try:\n            # 解析数据\n            numbers = json.loads(data)\n            if not isinstance(numbers, list) or not all(isinstance(x, (int, float)) for x in numbers):\n                return \"错误：数据必须是数字列表\"\n            \n            if analysis_type == \"basic_stats\":\n                mean = sum(numbers) / len(numbers)\n                sorted_nums = sorted(numbers)\n                n = len(sorted_nums)\n                median = sorted_nums[n//2] if n % 2 == 1 else (sorted_nums[n//2-1] + sorted_nums[n//2]) / 2\n                \n                variance = sum((x - mean) ** 2 for x in numbers) / len(numbers)\n                std_dev = variance ** 0.5\n                \n                return f\"\"\"基础统计分析结果：\n平均值: {mean:.2f}\n中位数: {median:.2f}\n标准差: {std_dev:.2f}\n最小值: {min(numbers)}\n最大值: {max(numbers)}\n数据点数: {len(numbers)}\"\"\"\n            \n            elif analysis_type == \"distribution\":\n                # 简单的分布分析\n                ranges = {}\n                min_val, max_val = min(numbers), max(numbers)\n                range_size = (max_val - min_val) / 5  # 分成5个区间\n                \n                for i in range(5):\n                    start = min_val + i * range_size\n                    end = min_val + (i + 1) * range_size\n                    count = sum(1 for x in numbers if start <= x < end)\n                    ranges[f\"{start:.1f}-{end:.1f}\"] = count\n                \n                result = \"数据分布分析：\\n\"\n                for range_name, count in ranges.items():\n                    result += f\"{range_name}: {count} 个数据点\\n\"\n                return result\n            \n            else:\n                return f\"不支持的分析类型：{analysis_type}\"\n                \n        except Exception as e:\n            return f\"数据分析失败：{str(e)}\"\n\nclass TimerTool(Tool):\n    \"\"\"计时器工具\"\"\"\n    \n    name = \"timer\"\n    description = \"计时器功能：开始计时、停止计时、获取当前时间\"\n    inputs = {\n        \"action\": {\n            \"type\": \"string\",\n            \"description\": \"操作类型：'start', 'stop', 'current', 'sleep'\"\n        },\n        \"seconds\": {\n            \"type\": \"number\",\n            \"description\": \"睡眠时间（仅用于 sleep 操作）\"\n        }\n    }\n    output_type = \"string\"\n    \n    def __init__(self):\n        super().__init__()\n        self.start_time = None\n    \n    def forward(self, action: str, seconds: float = 0) -> str:\n        \"\"\"执行计时操作\"\"\"\n        if action == \"start\":\n            self.start_time = time.time()\n            return f\"计时器已启动：{time.strftime('%Y-%m-%d %H:%M:%S')}\"\n        \n        elif action == \"stop\":\n            if self.start_time is None:\n                return \"计时器未启动\"\n            elapsed = time.time() - self.start_time\n            self.start_time = None\n            return f\"计时结束，耗时：{elapsed:.2f} 秒\"\n        \n        elif action == \"current\":\n            return f\"当前时间：{time.strftime('%Y-%m-%d %H:%M:%S')}\"\n        \n        elif action == \"sleep\":\n            time.sleep(seconds)\n            return f\"已等待 {seconds} 秒\"\n        \n        else:\n            return f\"不支持的操作：{action}\"\n", "hash": "048cddff77f72c1f541cef27dc28f932", "size": 7257}, ".history/examples/basic_agent_20250720131607.py": {"content": "\"\"\"\n基础 smolagents Agent 示例\n展示如何创建和使用 CodeAgent\n\"\"\"\n\nimport os\nfrom smolagents import CodeAgent, InferenceClientModel, WebSearchTool, PythonInterpreterTool\n\ndef create_basic_agent():\n    \"\"\"创建一个基础的 CodeAgent\"\"\"\n    \n    # 1. 选择模型 - 使用 Hugging Face Inference API\n    model = InferenceClientModel(\n        model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n        token=os.environ.get(\"HUGGINGFACE_TOKEN\")  # 可选，用于访问私有模型\n    )\n    \n    # 2. 选择工具\n    tools = [\n        WebSearchTool(),           # 网络搜索工具\n        PythonInterpreterTool(),   # Python 解释器工具\n    ]\n    \n    # 3. 创建 Agent\n    agent = CodeAgent(\n        tools=tools,\n        model=model,\n        max_iterations=10,\n        stream_outputs=True,\n        verbose=True\n    )\n    \n    return agent\n\ndef demo_basic_tasks():\n    \"\"\"演示基础任务\"\"\"\n    agent = create_basic_agent()\n    \n    print(\"=== smolagents 基础示例 ===\\n\")\n    \n    # 示例 1: 简单计算\n    print(\"📊 示例 1: 数学计算\")\n    result1 = agent.run(\"计算 123 * 456 + 789，并解释计算过程\")\n    print(f\"结果: {result1}\\n\")\n    \n    # 示例 2: 数据分析\n    print(\"📈 示例 2: 数据分析\")\n    result2 = agent.run(\"\"\"\n    创建一个包含以下数据的列表：[10, 25, 30, 45, 50, 60, 75, 80, 90, 100]\n    计算平均值、中位数和标准差，并用 matplotlib 创建一个简单的柱状图\n    \"\"\")\n    print(f\"结果: {result2}\\n\")\n    \n    # 示例 3: 网络搜索 + 数据处理\n    print(\"🔍 示例 3: 网络搜索与数据处理\")\n    result3 = agent.run(\"\"\"\n    搜索\"Python 编程语言的最新版本\"，\n    提取版本号信息，并创建一个简单的总结\n    \"\"\")\n    print(f\"结果: {result3}\\n\")\n\ndef demo_code_agent_features():\n    \"\"\"演示 CodeAgent 的特色功能\"\"\"\n    agent = create_basic_agent()\n    \n    print(\"=== CodeAgent 特色功能演示 ===\\n\")\n    \n    # 展示代码生成能力\n    print(\"💻 代码生成示例:\")\n    result = agent.run(\"\"\"\n    写一个 Python 函数来计算斐波那契数列的前 n 项，\n    然后计算前 10 项并显示结果。\n    要求函数要有适当的文档字符串和类型提示。\n    \"\"\")\n    print(f\"结果: {result}\\n\")\n\ndef demo_multi_step_reasoning():\n    \"\"\"演示多步推理能力\"\"\"\n    agent = create_basic_agent()\n    \n    print(\"=== 多步推理演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    我需要规划一个简单的数据科学项目：\n    1. 生成一些模拟数据（比如销售数据）\n    2. 进行基础的统计分析\n    3. 创建可视化图表\n    4. 得出简单的结论\n    \n    请帮我完成这个完整的流程。\n    \"\"\")\n    print(f\"结果: {result}\\n\")\n\nif __name__ == \"__main__\":\n    try:\n        print(\"🤖 欢迎使用 smolagents 基础示例！\\n\")\n        \n        # 检查是否有 Hugging Face token（可选）\n        if not os.environ.get(\"HUGGINGFACE_TOKEN\"):\n            print(\"💡 提示: 设置 HUGGINGFACE_TOKEN 环境变量可以访问更多模型\")\n            print(\"   export HUGGINGFACE_TOKEN='your_token_here'\\n\")\n        \n        # 运行示例\n        demo_basic_tasks()\n        demo_code_agent_features()\n        demo_multi_step_reasoning()\n        \n        print(\"✅ 所有示例运行完成！\")\n        \n    except Exception as e:\n        print(f\"❌ 运行出错: {e}\")\n        print(\"\\n💡 可能的解决方案:\")\n        print(\"1. 确保已安装 smolagents[toolkit]\")\n        print(\"2. 检查网络连接\")\n        print(\"3. 设置适当的环境变量\")\n", "hash": "d526d1b904d87d2b2676f737685456d0", "size": 3592}, ".history/examples/basic_agent_20250720131222.py": {"content": "\"\"\"\n基础 smolagents Agent 示例\n展示如何创建和使用 CodeAgent\n\"\"\"\n\nimport os\nfrom smolagents import CodeAgent, InferenceClientModel\nfrom smolagents.tools import WebSearchTool, PythonInterpreterTool\n\ndef create_basic_agent():\n    \"\"\"创建一个基础的 CodeAgent\"\"\"\n    \n    # 1. 选择模型 - 使用 Hugging Face Inference API\n    model = InferenceClientModel(\n        model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n        token=os.environ.get(\"HUGGINGFACE_TOKEN\")  # 可选，用于访问私有模型\n    )\n    \n    # 2. 选择工具\n    tools = [\n        WebSearchTool(),           # 网络搜索工具\n        PythonInterpreterTool(),   # Python 解释器工具\n    ]\n    \n    # 3. 创建 Agent\n    agent = CodeAgent(\n        tools=tools,\n        model=model,\n        max_iterations=10,\n        stream_outputs=True,\n        verbose=True\n    )\n    \n    return agent\n\ndef demo_basic_tasks():\n    \"\"\"演示基础任务\"\"\"\n    agent = create_basic_agent()\n    \n    print(\"=== smolagents 基础示例 ===\\n\")\n    \n    # 示例 1: 简单计算\n    print(\"📊 示例 1: 数学计算\")\n    result1 = agent.run(\"计算 123 * 456 + 789，并解释计算过程\")\n    print(f\"结果: {result1}\\n\")\n    \n    # 示例 2: 数据分析\n    print(\"📈 示例 2: 数据分析\")\n    result2 = agent.run(\"\"\"\n    创建一个包含以下数据的列表：[10, 25, 30, 45, 50, 60, 75, 80, 90, 100]\n    计算平均值、中位数和标准差，并用 matplotlib 创建一个简单的柱状图\n    \"\"\")\n    print(f\"结果: {result2}\\n\")\n    \n    # 示例 3: 网络搜索 + 数据处理\n    print(\"🔍 示例 3: 网络搜索与数据处理\")\n    result3 = agent.run(\"\"\"\n    搜索\"Python 编程语言的最新版本\"，\n    提取版本号信息，并创建一个简单的总结\n    \"\"\")\n    print(f\"结果: {result3}\\n\")\n\ndef demo_code_agent_features():\n    \"\"\"演示 CodeAgent 的特色功能\"\"\"\n    agent = create_basic_agent()\n    \n    print(\"=== CodeAgent 特色功能演示 ===\\n\")\n    \n    # 展示代码生成能力\n    print(\"💻 代码生成示例:\")\n    result = agent.run(\"\"\"\n    写一个 Python 函数来计算斐波那契数列的前 n 项，\n    然后计算前 10 项并显示结果。\n    要求函数要有适当的文档字符串和类型提示。\n    \"\"\")\n    print(f\"结果: {result}\\n\")\n\ndef demo_multi_step_reasoning():\n    \"\"\"演示多步推理能力\"\"\"\n    agent = create_basic_agent()\n    \n    print(\"=== 多步推理演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    我需要规划一个简单的数据科学项目：\n    1. 生成一些模拟数据（比如销售数据）\n    2. 进行基础的统计分析\n    3. 创建可视化图表\n    4. 得出简单的结论\n    \n    请帮我完成这个完整的流程。\n    \"\"\")\n    print(f\"结果: {result}\\n\")\n\nif __name__ == \"__main__\":\n    try:\n        print(\"🤖 欢迎使用 smolagents 基础示例！\\n\")\n        \n        # 检查是否有 Hugging Face token（可选）\n        if not os.environ.get(\"HUGGINGFACE_TOKEN\"):\n            print(\"💡 提示: 设置 HUGGINGFACE_TOKEN 环境变量可以访问更多模型\")\n            print(\"   export HUGGINGFACE_TOKEN='your_token_here'\\n\")\n        \n        # 运行示例\n        demo_basic_tasks()\n        demo_code_agent_features()\n        demo_multi_step_reasoning()\n        \n        print(\"✅ 所有示例运行完成！\")\n        \n    except Exception as e:\n        print(f\"❌ 运行出错: {e}\")\n        print(\"\\n💡 可能的解决方案:\")\n        print(\"1. 确保已安装 smolagents[toolkit]\")\n        print(\"2. 检查网络连接\")\n        print(\"3. 设置适当的环境变量\")\n", "hash": "8511afd5b4cbb47c1f63d5cd628c5323", "size": 3620}, ".history/examples/custom_tools_20250720131543.py": {"content": "\"\"\"\n自定义工具使用示例\n展示如何创建和使用自定义工具来扩展 Agent 能力\n\"\"\"\n\nimport os\nimport sys\nsys.path.append(os.path.join(os.path.dirname(__file__), '..'))\n\nfrom smolagents import CodeAgent, InferenceClientModel\nfrom smolagents.tools import PythonInterpreterTool\nfrom tools.custom_tools import WeatherTool, DataAnalysisTool, TimerTool, FileManagerTool\n\ndef create_agent_with_custom_tools():\n    \"\"\"创建带有自定义工具的 Agent\"\"\"\n    \n    # 1. 选择模型\n    model = InferenceClientModel(\n        model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n        token=os.environ.get(\"HUGGINGFACE_TOKEN\")\n    )\n    \n    # 2. 组合内置工具和自定义工具\n    tools = [\n        PythonInterpreterTool(),    # 内置工具\n        WeatherTool(),              # 自定义天气工具\n        DataAnalysisTool(),         # 自定义数据分析工具\n        TimerTool(),                # 自定义计时器工具\n        FileManagerTool(),          # 自定义文件管理工具\n    ]\n    \n    # 3. 创建 Agent\n    agent = CodeAgent(\n        tools=tools,\n        model=model,\n        max_iterations=15,\n        stream_outputs=True,\n        verbose=True\n    )\n    \n    return agent\n\ndef demo_weather_tool():\n    \"\"\"演示天气工具\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"🌤️ === 天气工具演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    请帮我查询以下城市的天气情况：\n    1. 北京\n    2. 上海\n    3. 成都（这个城市不在预设列表中）\n    \n    然后对这些城市的温度进行比较，告诉我哪个城市最热，哪个最冷。\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_data_analysis_tool():\n    \"\"\"演示数据分析工具\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"📊 === 数据分析工具演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    我有一组销售数据：[120, 150, 180, 200, 175, 190, 210, 165, 185, 195]\n    \n    请使用数据分析工具：\n    1. 计算基础统计信息\n    2. 分析数据分布\n    3. 用 Python 创建一个简单的可视化图表\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_timer_tool():\n    \"\"\"演示计时器工具\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"⏱️ === 计时器工具演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    请演示计时器工具的使用：\n    1. 获取当前时间\n    2. 启动计时器\n    3. 等待 2 秒\n    4. 停止计时器并显示耗时\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_file_manager_tool():\n    \"\"\"演示文件管理工具\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"📁 === 文件管理工具演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    请使用文件管理工具：\n    1. 创建一个名为 'test_data.txt' 的文件，内容为一些示例数据\n    2. 读取这个文件的内容\n    3. 向文件中写入新的内容\n    4. 再次读取文件确认内容已更新\n    5. 列出当前目录的所有文件\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_combined_workflow():\n    \"\"\"演示组合工作流程\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"🔄 === 组合工作流程演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    请完成一个完整的数据处理工作流程：\n    \n    1. 查询北京和上海的天气\n    2. 创建一个包含这两个城市温度数据的文件\n    3. 读取文件并对温度数据进行统计分析\n    4. 生成一个简单的温度对比图表\n    5. 将分析结果保存到新文件中\n    \n    整个过程请使用计时器记录耗时。\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_tool_creation_guide():\n    \"\"\"展示如何创建自定义工具的指南\"\"\"\n    print(\"🛠️ === 自定义工具创建指南 ===\\n\")\n    \n    guide = \"\"\"\n    创建自定义工具的步骤：\n    \n    1. 继承 smolagents.Tool 类\n    2. 定义工具属性：\n       - name: 工具名称\n       - description: 工具描述\n       - inputs: 输入参数定义\n       - output_type: 输出类型\n    \n    3. 实现 forward() 方法\n    \n    示例代码：\n    ```python\n    from smolagents import Tool\n    \n    class MyCustomTool(Tool):\n        name = \"my_tool\"\n        description = \"我的自定义工具\"\n        inputs = {\n            \"param1\": {\n                \"type\": \"string\",\n                \"description\": \"参数1的描述\"\n            }\n        }\n        output_type = \"string\"\n        \n        def forward(self, param1: str) -> str:\n            # 实现工具逻辑\n            return f\"处理结果: {param1}\"\n    ```\n    \n    4. 在 Agent 中使用：\n    ```python\n    tools = [MyCustomTool()]\n    agent = CodeAgent(tools=tools, model=model)\n    ```\n    \"\"\"\n    \n    print(guide)\n\nif __name__ == \"__main__\":\n    try:\n        print(\"🤖 欢迎使用 smolagents 自定义工具示例！\\n\")\n        \n        # 检查环境\n        if not os.environ.get(\"HUGGINGFACE_TOKEN\"):\n            print(\"💡 提示: 设置 HUGGINGFACE_TOKEN 环境变量可以访问更多模型\")\n            print(\"   export HUGGINGFACE_TOKEN='your_token_here'\\n\")\n        \n        # 展示工具创建指南\n        demo_tool_creation_guide()\n        \n        # 运行各种演示\n        demo_weather_tool()\n        demo_data_analysis_tool()\n        demo_timer_tool()\n        demo_file_manager_tool()\n        demo_combined_workflow()\n        \n        print(\"✅ 所有自定义工具演示完成！\")\n        \n    except Exception as e:\n        print(f\"❌ 运行出错: {e}\")\n        print(\"\\n💡 可能的解决方案:\")\n        print(\"1. 确保已安装 smolagents[toolkit]\")\n        print(\"2. 检查自定义工具的导入路径\")\n        print(\"3. 确认网络连接正常\")\n", "hash": "fa9a4faf4277a10e8dc349d5575bcdad", "size": 5767}, ".history/examples/custom_tools_20250720131615.py": {"content": "\"\"\"\n自定义工具使用示例\n展示如何创建和使用自定义工具来扩展 Agent 能力\n\"\"\"\n\nimport os\nimport sys\nsys.path.append(os.path.join(os.path.dirname(__file__), '..'))\n\nfrom smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool\nfrom tools.custom_tools import WeatherTool, DataAnalysisTool, TimerTool, FileManagerTool\n\ndef create_agent_with_custom_tools():\n    \"\"\"创建带有自定义工具的 Agent\"\"\"\n    \n    # 1. 选择模型\n    model = InferenceClientModel(\n        model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\",\n        token=os.environ.get(\"HUGGINGFACE_TOKEN\")\n    )\n    \n    # 2. 组合内置工具和自定义工具\n    tools = [\n        PythonInterpreterTool(),    # 内置工具\n        WeatherTool(),              # 自定义天气工具\n        DataAnalysisTool(),         # 自定义数据分析工具\n        TimerTool(),                # 自定义计时器工具\n        FileManagerTool(),          # 自定义文件管理工具\n    ]\n    \n    # 3. 创建 Agent\n    agent = CodeAgent(\n        tools=tools,\n        model=model,\n        max_iterations=15,\n        stream_outputs=True,\n        verbose=True\n    )\n    \n    return agent\n\ndef demo_weather_tool():\n    \"\"\"演示天气工具\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"🌤️ === 天气工具演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    请帮我查询以下城市的天气情况：\n    1. 北京\n    2. 上海\n    3. 成都（这个城市不在预设列表中）\n    \n    然后对这些城市的温度进行比较，告诉我哪个城市最热，哪个最冷。\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_data_analysis_tool():\n    \"\"\"演示数据分析工具\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"📊 === 数据分析工具演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    我有一组销售数据：[120, 150, 180, 200, 175, 190, 210, 165, 185, 195]\n    \n    请使用数据分析工具：\n    1. 计算基础统计信息\n    2. 分析数据分布\n    3. 用 Python 创建一个简单的可视化图表\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_timer_tool():\n    \"\"\"演示计时器工具\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"⏱️ === 计时器工具演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    请演示计时器工具的使用：\n    1. 获取当前时间\n    2. 启动计时器\n    3. 等待 2 秒\n    4. 停止计时器并显示耗时\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_file_manager_tool():\n    \"\"\"演示文件管理工具\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"📁 === 文件管理工具演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    请使用文件管理工具：\n    1. 创建一个名为 'test_data.txt' 的文件，内容为一些示例数据\n    2. 读取这个文件的内容\n    3. 向文件中写入新的内容\n    4. 再次读取文件确认内容已更新\n    5. 列出当前目录的所有文件\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_combined_workflow():\n    \"\"\"演示组合工作流程\"\"\"\n    agent = create_agent_with_custom_tools()\n    \n    print(\"🔄 === 组合工作流程演示 ===\\n\")\n    \n    result = agent.run(\"\"\"\n    请完成一个完整的数据处理工作流程：\n    \n    1. 查询北京和上海的天气\n    2. 创建一个包含这两个城市温度数据的文件\n    3. 读取文件并对温度数据进行统计分析\n    4. 生成一个简单的温度对比图表\n    5. 将分析结果保存到新文件中\n    \n    整个过程请使用计时器记录耗时。\n    \"\"\")\n    \n    print(f\"结果: {result}\\n\")\n\ndef demo_tool_creation_guide():\n    \"\"\"展示如何创建自定义工具的指南\"\"\"\n    print(\"🛠️ === 自定义工具创建指南 ===\\n\")\n    \n    guide = \"\"\"\n    创建自定义工具的步骤：\n    \n    1. 继承 smolagents.Tool 类\n    2. 定义工具属性：\n       - name: 工具名称\n       - description: 工具描述\n       - inputs: 输入参数定义\n       - output_type: 输出类型\n    \n    3. 实现 forward() 方法\n    \n    示例代码：\n    ```python\n    from smolagents import Tool\n    \n    class MyCustomTool(Tool):\n        name = \"my_tool\"\n        description = \"我的自定义工具\"\n        inputs = {\n            \"param1\": {\n                \"type\": \"string\",\n                \"description\": \"参数1的描述\"\n            }\n        }\n        output_type = \"string\"\n        \n        def forward(self, param1: str) -> str:\n            # 实现工具逻辑\n            return f\"处理结果: {param1}\"\n    ```\n    \n    4. 在 Agent 中使用：\n    ```python\n    tools = [MyCustomTool()]\n    agent = CodeAgent(tools=tools, model=model)\n    ```\n    \"\"\"\n    \n    print(guide)\n\nif __name__ == \"__main__\":\n    try:\n        print(\"🤖 欢迎使用 smolagents 自定义工具示例！\\n\")\n        \n        # 检查环境\n        if not os.environ.get(\"HUGGINGFACE_TOKEN\"):\n            print(\"💡 提示: 设置 HUGGINGFACE_TOKEN 环境变量可以访问更多模型\")\n            print(\"   export HUGGINGFACE_TOKEN='your_token_here'\\n\")\n        \n        # 展示工具创建指南\n        demo_tool_creation_guide()\n        \n        # 运行各种演示\n        demo_weather_tool()\n        demo_data_analysis_tool()\n        demo_timer_tool()\n        demo_file_manager_tool()\n        demo_combined_workflow()\n        \n        print(\"✅ 所有自定义工具演示完成！\")\n        \n    except Exception as e:\n        print(f\"❌ 运行出错: {e}\")\n        print(\"\\n💡 可能的解决方案:\")\n        print(\"1. 确保已安装 smolagents[toolkit]\")\n        print(\"2. 检查自定义工具的导入路径\")\n        print(\"3. 确认网络连接正常\")\n", "hash": "491a9132393d5096a77304938ad62584", "size": 5739}}, "working_directory": "/Users/<USER>/Desktop/agent"}