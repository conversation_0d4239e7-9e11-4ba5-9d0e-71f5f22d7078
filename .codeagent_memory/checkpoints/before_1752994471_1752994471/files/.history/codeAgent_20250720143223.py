"""
CodeAgent - 基于 smolagents 的智能代码编写助手
集成 DeepSeek 模型，实现从自然语言到代码生成的全流程自动化

主要功能：
- 自然语言理解与代码生成
- 文件操作与项目管理
- 命令执行与环境管理
- 任务分解与多工具协同
- 记忆管理与检查点机制
"""

import os
import json
import time
import hashlib
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from smolagents import CodeAgent, Tool, LiteLLMModel
from smolagents import PythonInterpreterTool

class MemoryManager:
    """记忆管理系统 - 实现持久化记忆与检索"""
    
    def __init__(self, memory_dir: str = ".codeagent_memory"):
        self.memory_dir = Path(memory_dir)
        self.memory_dir.mkdir(exist_ok=True)
        
        # 记忆文件路径
        self.conversation_file = self.memory_dir / "conversations.json"
        self.knowledge_file = self.memory_dir / "knowledge_base.json"
        self.checkpoints_dir = self.memory_dir / "checkpoints"
        self.checkpoints_dir.mkdir(exist_ok=True)
        
        # 初始化记忆存储
        self._init_memory_files()
    
    def _init_memory_files(self):
        """初始化记忆文件"""
        if not self.conversation_file.exists():
            self._save_json(self.conversation_file, {"conversations": []})
        
        if not self.knowledge_file.exists():
            self._save_json(self.knowledge_file, {
                "code_patterns": {},
                "project_structures": {},
                "user_preferences": {},
                "learned_solutions": {}
            })
    
    def _save_json(self, file_path: Path, data: Dict):
        """保存 JSON 数据"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def _load_json(self, file_path: Path) -> Dict:
        """加载 JSON 数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}
    
    def store_conversation(self, user_input: str, agent_response: str, context: Dict = None):
        """存储对话记录"""
        conversations = self._load_json(self.conversation_file)
        
        conversation_entry = {
            "timestamp": datetime.now().isoformat(),
            "user_input": user_input,
            "agent_response": agent_response,
            "context": context or {}
        }
        
        conversations["conversations"].append(conversation_entry)
        
        # 保持最近 100 条对话
        if len(conversations["conversations"]) > 100:
            conversations["conversations"] = conversations["conversations"][-100:]
        
        self._save_json(self.conversation_file, conversations)
    
    def store_knowledge(self, category: str, key: str, value: Any):
        """存储知识"""
        knowledge = self._load_json(self.knowledge_file)
        
        if category not in knowledge:
            knowledge[category] = {}
        
        knowledge[category][key] = {
            "value": value,
            "timestamp": datetime.now().isoformat()
        }
        
        self._save_json(self.knowledge_file, knowledge)
    
    def retrieve_knowledge(self, category: str, key: str = None) -> Any:
        """检索知识"""
        knowledge = self._load_json(self.knowledge_file)
        
        if category not in knowledge:
            return None
        
        if key is None:
            return knowledge[category]
        
        return knowledge[category].get(key, {}).get("value")
    
    def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:
        """搜索相关对话"""
        conversations = self._load_json(self.conversation_file)
        
        relevant_conversations = []
        query_lower = query.lower()
        
        for conv in conversations.get("conversations", []):
            if (query_lower in conv["user_input"].lower() or 
                query_lower in conv["agent_response"].lower()):
                relevant_conversations.append(conv)
        
        return relevant_conversations[-limit:]

class CheckpointManager:
    """检查点管理系统 - 实现工作区状态管理"""
    
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self.checkpoints_dir = memory_manager.checkpoints_dir
    
    def create_checkpoint(self, name: str, description: str = "") -> str:
        """创建检查点"""
        checkpoint_id = f"{name}_{int(time.time())}"
        checkpoint_dir = self.checkpoints_dir / checkpoint_id
        checkpoint_dir.mkdir(exist_ok=True)
        
        # 保存当前工作目录状态
        current_files = self._get_current_files()
        
        checkpoint_data = {
            "id": checkpoint_id,
            "name": name,
            "description": description,
            "timestamp": datetime.now().isoformat(),
            "files": current_files,
            "working_directory": str(Path.cwd())
        }
        
        checkpoint_file = checkpoint_dir / "checkpoint.json"
        self.memory_manager._save_json(checkpoint_file, checkpoint_data)
        
        # 复制重要文件
        self._backup_files(checkpoint_dir, current_files)
        
        return checkpoint_id
    
    def _get_current_files(self) -> Dict[str, str]:
        """获取当前目录的文件信息"""
        files_info = {}
        current_dir = Path.cwd()
        
        for file_path in current_dir.rglob("*"):
            if file_path.is_file() and not self._should_ignore_file(file_path):
                relative_path = file_path.relative_to(current_dir)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    files_info[str(relative_path)] = {
                        "content": content,
                        "hash": hashlib.md5(content.encode()).hexdigest(),
                        "size": file_path.stat().st_size
                    }
                except (UnicodeDecodeError, PermissionError):
                    # 跳过二进制文件或无权限文件
                    files_info[str(relative_path)] = {
                        "content": "[BINARY_FILE]",
                        "hash": "binary",
                        "size": file_path.stat().st_size
                    }
        
        return files_info
    
    def _should_ignore_file(self, file_path: Path) -> bool:
        """判断是否应该忽略文件"""
        ignore_patterns = [
            ".git", "__pycache__", ".pytest_cache", "node_modules",
            ".codeagent_memory", "*.pyc", "*.pyo", "*.pyd", ".DS_Store"
        ]
        
        path_str = str(file_path)
        for pattern in ignore_patterns:
            if pattern in path_str:
                return True
        
        return False
    
    def _backup_files(self, checkpoint_dir: Path, files_info: Dict):
        """备份文件到检查点目录"""
        files_dir = checkpoint_dir / "files"
        files_dir.mkdir(exist_ok=True)
        
        for file_path, info in files_info.items():
            if info["content"] != "[BINARY_FILE]":
                backup_file = files_dir / file_path
                backup_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(info["content"])
    
    def list_checkpoints(self) -> List[Dict]:
        """列出所有检查点"""
        checkpoints = []
        
        for checkpoint_dir in self.checkpoints_dir.iterdir():
            if checkpoint_dir.is_dir():
                checkpoint_file = checkpoint_dir / "checkpoint.json"
                if checkpoint_file.exists():
                    checkpoint_data = self.memory_manager._load_json(checkpoint_file)
                    checkpoints.append(checkpoint_data)
        
        return sorted(checkpoints, key=lambda x: x["timestamp"], reverse=True)
    
    def restore_checkpoint(self, checkpoint_id: str) -> bool:
        """恢复检查点"""
        checkpoint_dir = self.checkpoints_dir / checkpoint_id
        checkpoint_file = checkpoint_dir / "checkpoint.json"
        
        if not checkpoint_file.exists():
            return False
        
        checkpoint_data = self.memory_manager._load_json(checkpoint_file)
        files_dir = checkpoint_dir / "files"
        
        if not files_dir.exists():
            return False
        
        # 恢复文件
        for file_path in files_dir.rglob("*"):
            if file_path.is_file():
                relative_path = file_path.relative_to(files_dir)
                target_path = Path.cwd() / relative_path
                target_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(file_path, 'r', encoding='utf-8') as src:
                    content = src.read()
                
                with open(target_path, 'w', encoding='utf-8') as dst:
                    dst.write(content)
        
        return True
