"""
基础 smolagents Agent 示例
展示如何创建和使用 CodeAgent
"""

import os
from smolagents import CodeAgent, InferenceClientModel
from smolagents.tools import WebSearchTool, PythonInterpreterTool

def create_basic_agent():
    """创建一个基础的 CodeAgent"""
    
    # 1. 选择模型 - 使用 Hugging Face Inference API
    model = InferenceClientModel(
        model_id="Qwen/Qwen2.5-Coder-32B-Instruct",
        token=os.environ.get("HUGGINGFACE_TOKEN")  # 可选，用于访问私有模型
    )
    
    # 2. 选择工具
    tools = [
        WebSearchTool(),           # 网络搜索工具
        PythonInterpreterTool(),   # Python 解释器工具
    ]
    
    # 3. 创建 Agent
    agent = CodeAgent(
        tools=tools,
        model=model,
        max_iterations=10,
        stream_outputs=True,
        verbose=True
    )
    
    return agent

def demo_basic_tasks():
    """演示基础任务"""
    agent = create_basic_agent()
    
    print("=== smolagents 基础示例 ===\n")
    
    # 示例 1: 简单计算
    print("📊 示例 1: 数学计算")
    result1 = agent.run("计算 123 * 456 + 789，并解释计算过程")
    print(f"结果: {result1}\n")
    
    # 示例 2: 数据分析
    print("📈 示例 2: 数据分析")
    result2 = agent.run("""
    创建一个包含以下数据的列表：[10, 25, 30, 45, 50, 60, 75, 80, 90, 100]
    计算平均值、中位数和标准差，并用 matplotlib 创建一个简单的柱状图
    """)
    print(f"结果: {result2}\n")
    
    # 示例 3: 网络搜索 + 数据处理
    print("🔍 示例 3: 网络搜索与数据处理")
    result3 = agent.run("""
    搜索"Python 编程语言的最新版本"，
    提取版本号信息，并创建一个简单的总结
    """)
    print(f"结果: {result3}\n")

def demo_code_agent_features():
    """演示 CodeAgent 的特色功能"""
    agent = create_basic_agent()
    
    print("=== CodeAgent 特色功能演示 ===\n")
    
    # 展示代码生成能力
    print("💻 代码生成示例:")
    result = agent.run("""
    写一个 Python 函数来计算斐波那契数列的前 n 项，
    然后计算前 10 项并显示结果。
    要求函数要有适当的文档字符串和类型提示。
    """)
    print(f"结果: {result}\n")

def demo_multi_step_reasoning():
    """演示多步推理能力"""
    agent = create_basic_agent()
    
    print("=== 多步推理演示 ===\n")
    
    result = agent.run("""
    我需要规划一个简单的数据科学项目：
    1. 生成一些模拟数据（比如销售数据）
    2. 进行基础的统计分析
    3. 创建可视化图表
    4. 得出简单的结论
    
    请帮我完成这个完整的流程。
    """)
    print(f"结果: {result}\n")

if __name__ == "__main__":
    try:
        print("🤖 欢迎使用 smolagents 基础示例！\n")
        
        # 检查是否有 Hugging Face token（可选）
        if not os.environ.get("HUGGINGFACE_TOKEN"):
            print("💡 提示: 设置 HUGGINGFACE_TOKEN 环境变量可以访问更多模型")
            print("   export HUGGINGFACE_TOKEN='your_token_here'\n")
        
        # 运行示例
        demo_basic_tasks()
        demo_code_agent_features()
        demo_multi_step_reasoning()
        
        print("✅ 所有示例运行完成！")
        
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        print("\n💡 可能的解决方案:")
        print("1. 确保已安装 smolagents[toolkit]")
        print("2. 检查网络连接")
        print("3. 设置适当的环境变量")
