"""
自定义工具实现
展示如何创建自己的工具来扩展 Agent 能力
"""

import json
import time
import random
from typing import Dict, List, Any
from smolagents import Tool

class WeatherTool(Tool):
    """模拟天气查询工具"""
    
    name = "weather_tool"
    description = "获取指定城市的天气信息（模拟数据）"
    inputs = {
        "city": {
            "type": "string", 
            "description": "要查询天气的城市名称"
        }
    }
    output_type = "string"
    
    def forward(self, city: str) -> str:
        """模拟天气查询"""
        # 模拟一些城市的天气数据
        weather_data = {
            "北京": {"temperature": 15, "condition": "晴天", "humidity": 45},
            "上海": {"temperature": 18, "condition": "多云", "humidity": 60},
            "广州": {"temperature": 25, "condition": "小雨", "humidity": 75},
            "深圳": {"temperature": 26, "condition": "晴天", "humidity": 55},
            "杭州": {"temperature": 20, "condition": "阴天", "humidity": 65},
        }
        
        # 如果城市不在预设列表中，生成随机数据
        if city not in weather_data:
            weather_data[city] = {
                "temperature": random.randint(10, 30),
                "condition": random.choice(["晴天", "多云", "阴天", "小雨", "大雨"]),
                "humidity": random.randint(30, 90)
            }
        
        data = weather_data[city]
        return f"{city}的天气：温度 {data['temperature']}°C，{data['condition']}，湿度 {data['humidity']}%"

class FileManagerTool(Tool):
    """文件管理工具"""
    
    name = "file_manager"
    description = "管理文件：创建、读取、写入文本文件"
    inputs = {
        "action": {
            "type": "string",
            "description": "操作类型：'create', 'read', 'write', 'list'"
        },
        "filename": {
            "type": "string",
            "description": "文件名（对于 list 操作可选）",
            "nullable": True
        },
        "content": {
            "type": "string",
            "description": "文件内容（仅用于 write 操作）",
            "nullable": True
        }
    }
    output_type = "string"
    
    def forward(self, action: str, filename: str = "", content: str = "") -> str:
        """执行文件操作"""
        try:
            if action == "create":
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content or "")
                return f"文件 {filename} 创建成功"
            
            elif action == "read":
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                return f"文件 {filename} 内容：\n{content}"
            
            elif action == "write":
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                return f"内容已写入文件 {filename}"
            
            elif action == "list":
                import os
                files = os.listdir('.')
                return f"当前目录文件：{', '.join(files)}"
            
            else:
                return f"不支持的操作：{action}"
                
        except Exception as e:
            return f"文件操作失败：{str(e)}"

class DataAnalysisTool(Tool):
    """数据分析工具"""
    
    name = "data_analysis"
    description = "对数据进行基础统计分析"
    inputs = {
        "data": {
            "type": "string",
            "description": "要分析的数据，JSON 格式的数字列表"
        },
        "analysis_type": {
            "type": "string",
            "description": "分析类型：'basic_stats', 'distribution', 'correlation'",
            "nullable": True
        }
    }
    output_type = "string"
    
    def forward(self, data: str, analysis_type: str = "basic_stats") -> str:
        """执行数据分析"""
        try:
            # 解析数据
            numbers = json.loads(data)
            if not isinstance(numbers, list) or not all(isinstance(x, (int, float)) for x in numbers):
                return "错误：数据必须是数字列表"
            
            if analysis_type == "basic_stats":
                mean = sum(numbers) / len(numbers)
                sorted_nums = sorted(numbers)
                n = len(sorted_nums)
                median = sorted_nums[n//2] if n % 2 == 1 else (sorted_nums[n//2-1] + sorted_nums[n//2]) / 2
                
                variance = sum((x - mean) ** 2 for x in numbers) / len(numbers)
                std_dev = variance ** 0.5
                
                return f"""基础统计分析结果：
平均值: {mean:.2f}
中位数: {median:.2f}
标准差: {std_dev:.2f}
最小值: {min(numbers)}
最大值: {max(numbers)}
数据点数: {len(numbers)}"""
            
            elif analysis_type == "distribution":
                # 简单的分布分析
                ranges = {}
                min_val, max_val = min(numbers), max(numbers)
                range_size = (max_val - min_val) / 5  # 分成5个区间
                
                for i in range(5):
                    start = min_val + i * range_size
                    end = min_val + (i + 1) * range_size
                    count = sum(1 for x in numbers if start <= x < end)
                    ranges[f"{start:.1f}-{end:.1f}"] = count
                
                result = "数据分布分析：\n"
                for range_name, count in ranges.items():
                    result += f"{range_name}: {count} 个数据点\n"
                return result
            
            else:
                return f"不支持的分析类型：{analysis_type}"
                
        except Exception as e:
            return f"数据分析失败：{str(e)}"

class TimerTool(Tool):
    """计时器工具"""
    
    name = "timer"
    description = "计时器功能：开始计时、停止计时、获取当前时间"
    inputs = {
        "action": {
            "type": "string",
            "description": "操作类型：'start', 'stop', 'current', 'sleep'"
        },
        "seconds": {
            "type": "number",
            "description": "睡眠时间（仅用于 sleep 操作）"
        }
    }
    output_type = "string"
    
    def __init__(self):
        super().__init__()
        self.start_time = None
    
    def forward(self, action: str, seconds: float = 0) -> str:
        """执行计时操作"""
        if action == "start":
            self.start_time = time.time()
            return f"计时器已启动：{time.strftime('%Y-%m-%d %H:%M:%S')}"
        
        elif action == "stop":
            if self.start_time is None:
                return "计时器未启动"
            elapsed = time.time() - self.start_time
            self.start_time = None
            return f"计时结束，耗时：{elapsed:.2f} 秒"
        
        elif action == "current":
            return f"当前时间：{time.strftime('%Y-%m-%d %H:%M:%S')}"
        
        elif action == "sleep":
            time.sleep(seconds)
            return f"已等待 {seconds} 秒"
        
        else:
            return f"不支持的操作：{action}"
