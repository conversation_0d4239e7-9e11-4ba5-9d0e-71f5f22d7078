"""
CodeAgent - 基于 smolagents 的智能代码编写助手
集成 DeepSeek 模型，实现从自然语言到代码生成的全流程自动化

主要功能：
- 自然语言理解与代码生成
- 文件操作与项目管理
- 命令执行与环境管理
- 任务分解与多工具协同
- 记忆管理与检查点机制
"""

import os
import json
import time
import hashlib
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from smolagents import CodeAgent, Tool, LiteLLMModel
from smolagents import PythonInterpreterTool

class MemoryManager:
    """记忆管理系统 - 实现持久化记忆与检索"""
    
    def __init__(self, memory_dir: str = ".codeagent_memory"):
        self.memory_dir = Path(memory_dir)
        self.memory_dir.mkdir(exist_ok=True)
        
        # 记忆文件路径
        self.conversation_file = self.memory_dir / "conversations.json"
        self.knowledge_file = self.memory_dir / "knowledge_base.json"
        self.checkpoints_dir = self.memory_dir / "checkpoints"
        self.checkpoints_dir.mkdir(exist_ok=True)
        
        # 初始化记忆存储
        self._init_memory_files()
    
    def _init_memory_files(self):
        """初始化记忆文件"""
        if not self.conversation_file.exists():
            self._save_json(self.conversation_file, {"conversations": []})
        
        if not self.knowledge_file.exists():
            self._save_json(self.knowledge_file, {
                "code_patterns": {},
                "project_structures": {},
                "user_preferences": {},
                "learned_solutions": {}
            })
    
    def _save_json(self, file_path: Path, data: Dict):
        """保存 JSON 数据"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def _load_json(self, file_path: Path) -> Dict:
        """加载 JSON 数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}
    
    def store_conversation(self, user_input: str, agent_response: str, context: Dict = None):
        """存储对话记录"""
        conversations = self._load_json(self.conversation_file)
        
        conversation_entry = {
            "timestamp": datetime.now().isoformat(),
            "user_input": user_input,
            "agent_response": agent_response,
            "context": context or {}
        }
        
        conversations["conversations"].append(conversation_entry)
        
        # 保持最近 100 条对话
        if len(conversations["conversations"]) > 100:
            conversations["conversations"] = conversations["conversations"][-100:]
        
        self._save_json(self.conversation_file, conversations)
    
    def store_knowledge(self, category: str, key: str, value: Any):
        """存储知识"""
        knowledge = self._load_json(self.knowledge_file)
        
        if category not in knowledge:
            knowledge[category] = {}
        
        knowledge[category][key] = {
            "value": value,
            "timestamp": datetime.now().isoformat()
        }
        
        self._save_json(self.knowledge_file, knowledge)
    
    def retrieve_knowledge(self, category: str, key: str = None) -> Any:
        """检索知识"""
        knowledge = self._load_json(self.knowledge_file)
        
        if category not in knowledge:
            return None
        
        if key is None:
            return knowledge[category]
        
        return knowledge[category].get(key, {}).get("value")
    
    def search_conversations(self, query: str, limit: int = 5) -> List[Dict]:
        """搜索相关对话"""
        conversations = self._load_json(self.conversation_file)
        
        relevant_conversations = []
        query_lower = query.lower()
        
        for conv in conversations.get("conversations", []):
            if (query_lower in conv["user_input"].lower() or 
                query_lower in conv["agent_response"].lower()):
                relevant_conversations.append(conv)
        
        return relevant_conversations[-limit:]

class CheckpointManager:
    """检查点管理系统 - 实现工作区状态管理"""
    
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self.checkpoints_dir = memory_manager.checkpoints_dir
    
    def create_checkpoint(self, name: str, description: str = "") -> str:
        """创建检查点"""
        checkpoint_id = f"{name}_{int(time.time())}"
        checkpoint_dir = self.checkpoints_dir / checkpoint_id
        checkpoint_dir.mkdir(exist_ok=True)
        
        # 保存当前工作目录状态
        current_files = self._get_current_files()
        
        checkpoint_data = {
            "id": checkpoint_id,
            "name": name,
            "description": description,
            "timestamp": datetime.now().isoformat(),
            "files": current_files,
            "working_directory": str(Path.cwd())
        }
        
        checkpoint_file = checkpoint_dir / "checkpoint.json"
        self.memory_manager._save_json(checkpoint_file, checkpoint_data)
        
        # 复制重要文件
        self._backup_files(checkpoint_dir, current_files)
        
        return checkpoint_id
    
    def _get_current_files(self) -> Dict[str, str]:
        """获取当前目录的文件信息"""
        files_info = {}
        current_dir = Path.cwd()
        
        for file_path in current_dir.rglob("*"):
            if file_path.is_file() and not self._should_ignore_file(file_path):
                relative_path = file_path.relative_to(current_dir)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    files_info[str(relative_path)] = {
                        "content": content,
                        "hash": hashlib.md5(content.encode()).hexdigest(),
                        "size": file_path.stat().st_size
                    }
                except (UnicodeDecodeError, PermissionError):
                    # 跳过二进制文件或无权限文件
                    files_info[str(relative_path)] = {
                        "content": "[BINARY_FILE]",
                        "hash": "binary",
                        "size": file_path.stat().st_size
                    }
        
        return files_info
    
    def _should_ignore_file(self, file_path: Path) -> bool:
        """判断是否应该忽略文件"""
        ignore_patterns = [
            ".git", "__pycache__", ".pytest_cache", "node_modules",
            ".codeagent_memory", "*.pyc", "*.pyo", "*.pyd", ".DS_Store"
        ]
        
        path_str = str(file_path)
        for pattern in ignore_patterns:
            if pattern in path_str:
                return True
        
        return False
    
    def _backup_files(self, checkpoint_dir: Path, files_info: Dict):
        """备份文件到检查点目录"""
        files_dir = checkpoint_dir / "files"
        files_dir.mkdir(exist_ok=True)
        
        for file_path, info in files_info.items():
            if info["content"] != "[BINARY_FILE]":
                backup_file = files_dir / file_path
                backup_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(info["content"])
    
    def list_checkpoints(self) -> List[Dict]:
        """列出所有检查点"""
        checkpoints = []
        
        for checkpoint_dir in self.checkpoints_dir.iterdir():
            if checkpoint_dir.is_dir():
                checkpoint_file = checkpoint_dir / "checkpoint.json"
                if checkpoint_file.exists():
                    checkpoint_data = self.memory_manager._load_json(checkpoint_file)
                    checkpoints.append(checkpoint_data)
        
        return sorted(checkpoints, key=lambda x: x["timestamp"], reverse=True)
    
    def restore_checkpoint(self, checkpoint_id: str) -> bool:
        """恢复检查点"""
        checkpoint_dir = self.checkpoints_dir / checkpoint_id
        checkpoint_file = checkpoint_dir / "checkpoint.json"
        
        if not checkpoint_file.exists():
            return False
        
        checkpoint_data = self.memory_manager._load_json(checkpoint_file)
        files_dir = checkpoint_dir / "files"
        
        if not files_dir.exists():
            return False
        
        # 恢复文件
        for file_path in files_dir.rglob("*"):
            if file_path.is_file():
                relative_path = file_path.relative_to(files_dir)
                target_path = Path.cwd() / relative_path
                target_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(file_path, 'r', encoding='utf-8') as src:
                    content = src.read()
                
                with open(target_path, 'w', encoding='utf-8') as dst:
                    dst.write(content)
        
        return True

class CodeGenerationTool(Tool):
    """代码生成工具 - 基于自然语言生成代码"""

    name = "code_generation"
    description = "根据自然语言描述生成代码，支持多种编程语言"
    inputs = {
        "description": {
            "type": "string",
            "description": "代码功能的自然语言描述"
        },
        "language": {
            "type": "string",
            "description": "编程语言（python, javascript, java, cpp 等）",
            "nullable": True
        },
        "context": {
            "type": "string",
            "description": "相关上下文信息",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, memory_manager: MemoryManager):
        super().__init__()
        self.memory_manager = memory_manager

    def forward(self, description: str, language: str = "python", context: str = "") -> str:
        """生成代码"""
        # 搜索相关历史经验
        similar_conversations = self.memory_manager.search_conversations(description, limit=3)

        # 获取用户偏好
        user_preferences = self.memory_manager.retrieve_knowledge("user_preferences") or {}

        # 构建增强的提示
        enhanced_prompt = f"""
基于以下描述生成 {language} 代码：

需求描述：{description}

上下文信息：{context}

用户偏好：{json.dumps(user_preferences, ensure_ascii=False, indent=2)}

相关历史经验：
{json.dumps(similar_conversations, ensure_ascii=False, indent=2)}

请生成清晰、可读、符合最佳实践的代码，并包含必要的注释。
"""

        # 这里应该调用 LLM 生成代码，暂时返回模板
        generated_code = f"""
# 根据描述生成的 {language} 代码
# 需求：{description}

# TODO: 实际的代码生成逻辑
def generated_function():
    '''
    {description}
    '''
    pass
"""

        # 存储生成的代码模式
        self.memory_manager.store_knowledge(
            "code_patterns",
            f"{language}_{hashlib.md5(description.encode()).hexdigest()[:8]}",
            {
                "description": description,
                "language": language,
                "code": generated_code,
                "context": context
            }
        )

        return generated_code

class FileOperationTool(Tool):
    """文件操作工具 - 处理文件读写、创建、删除等操作"""

    name = "file_operation"
    description = "执行文件操作：创建、读取、写入、删除、移动文件"
    inputs = {
        "operation": {
            "type": "string",
            "description": "操作类型：create, read, write, delete, move, list"
        },
        "file_path": {
            "type": "string",
            "description": "文件路径"
        },
        "content": {
            "type": "string",
            "description": "文件内容（用于 write 操作）",
            "nullable": True
        },
        "target_path": {
            "type": "string",
            "description": "目标路径（用于 move 操作）",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, memory_manager: MemoryManager):
        super().__init__()
        self.memory_manager = memory_manager

    def forward(self, operation: str, file_path: str, content: str = "", target_path: str = "") -> str:
        """执行文件操作"""
        try:
            path = Path(file_path)

            if operation == "create":
                path.parent.mkdir(parents=True, exist_ok=True)
                path.touch()
                return f"文件 {file_path} 创建成功"

            elif operation == "read":
                if not path.exists():
                    return f"错误：文件 {file_path} 不存在"

                with open(path, 'r', encoding='utf-8') as f:
                    file_content = f.read()

                # 存储文件访问记录
                self.memory_manager.store_knowledge(
                    "file_access",
                    str(path),
                    {"last_read": datetime.now().isoformat(), "size": len(file_content)}
                )

                return file_content

            elif operation == "write":
                path.parent.mkdir(parents=True, exist_ok=True)

                with open(path, 'w', encoding='utf-8') as f:
                    f.write(content)

                # 存储文件修改记录
                self.memory_manager.store_knowledge(
                    "file_modifications",
                    str(path),
                    {
                        "last_modified": datetime.now().isoformat(),
                        "content_hash": hashlib.md5(content.encode()).hexdigest()
                    }
                )

                return f"内容已写入文件 {file_path}"

            elif operation == "delete":
                if path.exists():
                    if path.is_file():
                        path.unlink()
                        return f"文件 {file_path} 删除成功"
                    elif path.is_dir():
                        import shutil
                        shutil.rmtree(path)
                        return f"目录 {file_path} 删除成功"
                else:
                    return f"错误：路径 {file_path} 不存在"

            elif operation == "move":
                if not path.exists():
                    return f"错误：源文件 {file_path} 不存在"

                target = Path(target_path)
                target.parent.mkdir(parents=True, exist_ok=True)
                path.rename(target)
                return f"文件从 {file_path} 移动到 {target_path}"

            elif operation == "list":
                if path.is_dir():
                    files = [str(p) for p in path.iterdir()]
                    return f"目录 {file_path} 内容：\n" + "\n".join(files)
                else:
                    return f"错误：{file_path} 不是目录"

            else:
                return f"不支持的操作：{operation}"

        except Exception as e:
            error_msg = f"文件操作失败：{str(e)}"

            # 记录错误
            self.memory_manager.store_knowledge(
                "operation_errors",
                f"file_op_{int(time.time())}",
                {
                    "operation": operation,
                    "file_path": file_path,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            )

            return error_msg

class CommandExecutionTool(Tool):
    """命令执行工具 - 安全执行系统命令"""

    name = "command_execution"
    description = "执行系统命令，支持安全检查和结果记录"
    inputs = {
        "command": {
            "type": "string",
            "description": "要执行的命令"
        },
        "working_dir": {
            "type": "string",
            "description": "工作目录",
            "nullable": True
        },
        "timeout": {
            "type": "number",
            "description": "超时时间（秒）",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, memory_manager: MemoryManager):
        super().__init__()
        self.memory_manager = memory_manager

        # 危险命令黑名单
        self.dangerous_commands = [
            "rm -rf", "del /f", "format", "fdisk", "mkfs",
            "shutdown", "reboot", "halt", "poweroff",
            "chmod 777", "chown", "sudo rm", "dd if="
        ]

    def _is_safe_command(self, command: str) -> bool:
        """检查命令是否安全"""
        command_lower = command.lower()

        for dangerous in self.dangerous_commands:
            if dangerous in command_lower:
                return False

        return True

    def forward(self, command: str, working_dir: str = "", timeout: float = 30.0) -> str:
        """执行命令"""
        # 安全检查
        if not self._is_safe_command(command):
            error_msg = f"危险命令被阻止：{command}"
            self.memory_manager.store_knowledge(
                "security_blocks",
                f"cmd_{int(time.time())}",
                {
                    "command": command,
                    "reason": "dangerous_command",
                    "timestamp": datetime.now().isoformat()
                }
            )
            return error_msg

        try:
            # 设置工作目录
            cwd = Path(working_dir) if working_dir else Path.cwd()

            # 执行命令
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )

            # 构建结果
            output = f"命令执行完成\n"
            output += f"返回码: {result.returncode}\n"

            if result.stdout:
                output += f"标准输出:\n{result.stdout}\n"

            if result.stderr:
                output += f"标准错误:\n{result.stderr}\n"

            # 记录命令执行
            self.memory_manager.store_knowledge(
                "command_history",
                f"cmd_{int(time.time())}",
                {
                    "command": command,
                    "working_dir": str(cwd),
                    "return_code": result.returncode,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "timestamp": datetime.now().isoformat()
                }
            )

            return output

        except subprocess.TimeoutExpired:
            return f"命令执行超时（{timeout}秒）：{command}"

        except Exception as e:
            error_msg = f"命令执行失败：{str(e)}"

            # 记录错误
            self.memory_manager.store_knowledge(
                "operation_errors",
                f"cmd_error_{int(time.time())}",
                {
                    "command": command,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            )

            return error_msg

class TaskDecompositionTool(Tool):
    """任务分解工具 - 将复杂任务分解为子任务"""

    name = "task_decomposition"
    description = "将复杂任务分解为可执行的子任务序列"
    inputs = {
        "task_description": {
            "type": "string",
            "description": "复杂任务的描述"
        },
        "context": {
            "type": "string",
            "description": "任务上下文信息",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, memory_manager: MemoryManager):
        super().__init__()
        self.memory_manager = memory_manager

    def forward(self, task_description: str, context: str = "") -> str:
        """分解任务"""
        # 搜索相似任务的历史分解
        similar_tasks = self.memory_manager.search_conversations(task_description, limit=3)

        # 获取已知的项目结构模式
        project_patterns = self.memory_manager.retrieve_knowledge("project_structures") or {}

        # 基于规则的任务分解（简化版本）
        subtasks = self._decompose_task(task_description, context)

        # 格式化输出
        result = f"任务分解结果：{task_description}\n\n"
        result += "子任务序列：\n"

        for i, subtask in enumerate(subtasks, 1):
            result += f"{i}. {subtask['name']}\n"
            result += f"   描述: {subtask['description']}\n"
            result += f"   工具: {subtask['tools']}\n"
            result += f"   预估时间: {subtask['estimated_time']}\n\n"

        # 存储任务分解结果
        self.memory_manager.store_knowledge(
            "task_decompositions",
            hashlib.md5(task_description.encode()).hexdigest()[:8],
            {
                "original_task": task_description,
                "context": context,
                "subtasks": subtasks,
                "timestamp": datetime.now().isoformat()
            }
        )

        return result

    def _decompose_task(self, task_description: str, context: str) -> List[Dict]:
        """基于规则的任务分解"""
        subtasks = []

        # 分析任务类型
        task_lower = task_description.lower()

        if "创建" in task_lower or "开发" in task_lower:
            if "网站" in task_lower or "web" in task_lower:
                subtasks.extend(self._web_development_tasks())
            elif "api" in task_lower:
                subtasks.extend(self._api_development_tasks())
            elif "数据分析" in task_lower:
                subtasks.extend(self._data_analysis_tasks())
            else:
                subtasks.extend(self._general_development_tasks())

        elif "分析" in task_lower:
            subtasks.extend(self._analysis_tasks())

        elif "测试" in task_lower:
            subtasks.extend(self._testing_tasks())

        else:
            # 通用任务分解
            subtasks.extend(self._general_tasks(task_description))

        return subtasks

    def _web_development_tasks(self) -> List[Dict]:
        """Web 开发任务分解"""
        return [
            {
                "name": "项目初始化",
                "description": "创建项目目录结构和配置文件",
                "tools": ["file_operation"],
                "estimated_time": "10分钟"
            },
            {
                "name": "前端开发",
                "description": "创建HTML、CSS、JavaScript文件",
                "tools": ["code_generation", "file_operation"],
                "estimated_time": "30分钟"
            },
            {
                "name": "后端开发",
                "description": "实现服务器端逻辑",
                "tools": ["code_generation", "file_operation"],
                "estimated_time": "45分钟"
            },
            {
                "name": "测试和部署",
                "description": "运行测试并部署应用",
                "tools": ["command_execution"],
                "estimated_time": "15分钟"
            }
        ]

    def _api_development_tasks(self) -> List[Dict]:
        """API 开发任务分解"""
        return [
            {
                "name": "API设计",
                "description": "设计API接口和数据模型",
                "tools": ["code_generation"],
                "estimated_time": "20分钟"
            },
            {
                "name": "实现API端点",
                "description": "编写API路由和处理函数",
                "tools": ["code_generation", "file_operation"],
                "estimated_time": "40分钟"
            },
            {
                "name": "添加文档",
                "description": "生成API文档",
                "tools": ["code_generation", "file_operation"],
                "estimated_time": "15分钟"
            }
        ]

    def _data_analysis_tasks(self) -> List[Dict]:
        """数据分析任务分解"""
        return [
            {
                "name": "数据收集",
                "description": "获取和加载数据",
                "tools": ["code_generation", "file_operation"],
                "estimated_time": "15分钟"
            },
            {
                "name": "数据清洗",
                "description": "处理缺失值和异常数据",
                "tools": ["code_generation"],
                "estimated_time": "25分钟"
            },
            {
                "name": "数据分析",
                "description": "执行统计分析和可视化",
                "tools": ["code_generation"],
                "estimated_time": "30分钟"
            },
            {
                "name": "生成报告",
                "description": "创建分析报告",
                "tools": ["file_operation"],
                "estimated_time": "20分钟"
            }
        ]

    def _general_development_tasks(self) -> List[Dict]:
        """通用开发任务分解"""
        return [
            {
                "name": "需求分析",
                "description": "分析和理解需求",
                "tools": [],
                "estimated_time": "10分钟"
            },
            {
                "name": "代码实现",
                "description": "编写核心功能代码",
                "tools": ["code_generation", "file_operation"],
                "estimated_time": "40分钟"
            },
            {
                "name": "测试验证",
                "description": "测试功能正确性",
                "tools": ["command_execution"],
                "estimated_time": "15分钟"
            }
        ]

    def _analysis_tasks(self) -> List[Dict]:
        """分析任务分解"""
        return [
            {
                "name": "数据收集",
                "description": "收集相关数据和信息",
                "tools": ["file_operation"],
                "estimated_time": "15分钟"
            },
            {
                "name": "数据处理",
                "description": "清洗和预处理数据",
                "tools": ["code_generation"],
                "estimated_time": "20分钟"
            },
            {
                "name": "分析计算",
                "description": "执行分析算法",
                "tools": ["code_generation"],
                "estimated_time": "25分钟"
            },
            {
                "name": "结果展示",
                "description": "可视化和报告生成",
                "tools": ["code_generation", "file_operation"],
                "estimated_time": "20分钟"
            }
        ]

    def _testing_tasks(self) -> List[Dict]:
        """测试任务分解"""
        return [
            {
                "name": "测试计划",
                "description": "制定测试策略和计划",
                "tools": ["file_operation"],
                "estimated_time": "10分钟"
            },
            {
                "name": "编写测试",
                "description": "创建测试用例和测试代码",
                "tools": ["code_generation", "file_operation"],
                "estimated_time": "30分钟"
            },
            {
                "name": "执行测试",
                "description": "运行测试并收集结果",
                "tools": ["command_execution"],
                "estimated_time": "15分钟"
            },
            {
                "name": "测试报告",
                "description": "生成测试报告",
                "tools": ["file_operation"],
                "estimated_time": "10分钟"
            }
        ]

    def _general_tasks(self, task_description: str) -> List[Dict]:
        """通用任务分解"""
        return [
            {
                "name": "任务准备",
                "description": f"准备执行任务：{task_description}",
                "tools": ["file_operation"],
                "estimated_time": "5分钟"
            },
            {
                "name": "主要执行",
                "description": "执行主要任务逻辑",
                "tools": ["code_generation", "file_operation", "command_execution"],
                "estimated_time": "30分钟"
            },
            {
                "name": "结果整理",
                "description": "整理和验证结果",
                "tools": ["file_operation"],
                "estimated_time": "10分钟"
            }
        ]

class CheckpointTool(Tool):
    """检查点管理工具 - 管理工作区状态检查点"""

    name = "checkpoint_management"
    description = "创建、列出、恢复工作区检查点"
    inputs = {
        "operation": {
            "type": "string",
            "description": "操作类型：create, list, restore"
        },
        "checkpoint_name": {
            "type": "string",
            "description": "检查点名称",
            "nullable": True
        },
        "description": {
            "type": "string",
            "description": "检查点描述",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, checkpoint_manager: CheckpointManager):
        super().__init__()
        self.checkpoint_manager = checkpoint_manager

    def forward(self, operation: str, checkpoint_name: str = "", description: str = "") -> str:
        """执行检查点操作"""
        try:
            if operation == "create":
                if not checkpoint_name:
                    checkpoint_name = f"auto_{int(time.time())}"

                checkpoint_id = self.checkpoint_manager.create_checkpoint(
                    checkpoint_name, description
                )
                return f"检查点创建成功：{checkpoint_id}"

            elif operation == "list":
                checkpoints = self.checkpoint_manager.list_checkpoints()

                if not checkpoints:
                    return "没有找到检查点"

                result = "可用检查点：\n"
                for cp in checkpoints:
                    result += f"- {cp['id']}: {cp['name']}\n"
                    result += f"  描述: {cp['description']}\n"
                    result += f"  时间: {cp['timestamp']}\n\n"

                return result

            elif operation == "restore":
                if not checkpoint_name:
                    return "错误：需要指定检查点名称"

                success = self.checkpoint_manager.restore_checkpoint(checkpoint_name)

                if success:
                    return f"检查点恢复成功：{checkpoint_name}"
                else:
                    return f"检查点恢复失败：{checkpoint_name}"

            else:
                return f"不支持的操作：{operation}"

        except Exception as e:
            return f"检查点操作失败：{str(e)}"

class IntelligentCodeAgent:
    """智能代码编写助手 - 主要的 Agent 类"""

    def __init__(self, api_key: str = None, model_name: str = "deepseek-chat"):
        """初始化 CodeAgent"""

        # 初始化记忆和检查点管理
        self.memory_manager = MemoryManager()
        self.checkpoint_manager = CheckpointManager(self.memory_manager)

        # 初始化工具
        self.tools = [
            PythonInterpreterTool(),
            CodeGenerationTool(self.memory_manager),
            FileOperationTool(self.memory_manager),
            CommandExecutionTool(self.memory_manager),
            TaskDecompositionTool(self.memory_manager),
            CheckpointTool(self.checkpoint_manager)
        ]

        # 初始化模型
        self.model = self._setup_model(api_key, model_name)

        # 创建 smolagents CodeAgent
        self.agent = CodeAgent(
            tools=self.tools,
            model=self.model,
            stream_outputs=True
        )

        # 系统提示词
        self.system_prompt = self._build_system_prompt()

        print("🤖 智能代码助手已初始化完成！")
        print("💡 支持功能：代码生成、文件操作、命令执行、任务分解、记忆管理")

    def _setup_model(self, api_key: str, model_name: str):
        """设置模型"""
        try:
            # 使用 LiteLLM 支持 DeepSeek
            model = LiteLLMModel(
                model_id=model_name,
                api_key=api_key or os.environ.get("DEEPSEEK_API_KEY"),
                api_base="https://api.deepseek.com/v1",
                temperature=0.1,
                max_tokens=4000
            )
            return model
        except Exception as e:
            print(f"⚠️ 模型初始化失败，使用默认配置: {e}")
            # 回退到默认模型
            from smolagents import InferenceClientModel
            return InferenceClientModel(
                model_id="Qwen/Qwen2.5-Coder-32B-Instruct",
                provider="auto"
            )

    def _build_system_prompt(self) -> str:
        """构建系统提示词"""
        return """
你是一个专业的代码编写助手，具有以下能力：

🎯 核心功能：
1. 代码生成：根据自然语言描述生成高质量代码
2. 文件操作：创建、读取、修改、删除文件和目录
3. 命令执行：安全执行系统命令和脚本
4. 任务分解：将复杂任务分解为可执行的子任务
5. 记忆管理：记住用户偏好和历史交互
6. 检查点管理：创建和恢复工作区状态

🛠️ 可用工具：
- python_interpreter: Python 代码执行
- code_generation: 代码生成
- file_operation: 文件操作
- command_execution: 命令执行
- task_decomposition: 任务分解
- checkpoint_management: 检查点管理

💡 工作原则：
1. 安全第一：避免执行危险命令
2. 代码质量：生成清晰、可读、符合最佳实践的代码
3. 用户体验：提供详细的解释和建议
4. 持续学习：从交互中学习用户偏好
5. 状态管理：适时创建检查点保护工作成果

🔄 工作流程：
1. 理解用户需求
2. 分解复杂任务（如需要）
3. 选择合适的工具
4. 执行操作
5. 验证结果
6. 记录经验

请始终以专业、友好的方式协助用户完成编程任务。
"""

    def run(self, user_input: str, auto_checkpoint: bool = True) -> str:
        """运行用户请求"""
        try:
            # 记录开始时间
            start_time = time.time()

            # 搜索相关历史记录
            relevant_history = self.memory_manager.search_conversations(user_input, limit=3)

            # 构建增强的输入
            enhanced_input = self._enhance_input(user_input, relevant_history)

            # 自动创建检查点（如果启用）
            if auto_checkpoint:
                checkpoint_id = self.checkpoint_manager.create_checkpoint(
                    f"before_{int(time.time())}",
                    f"执行前检查点: {user_input[:50]}..."
                )
                print(f"📍 已创建检查点: {checkpoint_id}")

            # 执行任务
            result = self.agent.run(enhanced_input)

            # 计算执行时间
            execution_time = time.time() - start_time

            # 存储对话记录
            self.memory_manager.store_conversation(
                user_input,
                result,
                {
                    "execution_time": execution_time,
                    "tools_used": [tool.name for tool in self.tools],
                    "checkpoint_created": auto_checkpoint
                }
            )

            # 学习用户偏好
            self._learn_from_interaction(user_input, result)

            print(f"⏱️ 执行时间: {execution_time:.2f}秒")

            return result

        except Exception as e:
            error_msg = f"执行失败: {str(e)}"

            # 记录错误
            self.memory_manager.store_knowledge(
                "execution_errors",
                f"error_{int(time.time())}",
                {
                    "user_input": user_input,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            )

            return error_msg

    def _enhance_input(self, user_input: str, relevant_history: List[Dict]) -> str:
        """增强用户输入"""
        enhanced = f"{self.system_prompt}\n\n"

        if relevant_history:
            enhanced += "📚 相关历史记录:\n"
            for i, conv in enumerate(relevant_history, 1):
                enhanced += f"{i}. 用户: {conv['user_input'][:100]}...\n"
                enhanced += f"   助手: {conv['agent_response'][:100]}...\n\n"

        # 获取用户偏好
        user_prefs = self.memory_manager.retrieve_knowledge("user_preferences") or {}
        if user_prefs:
            enhanced += f"👤 用户偏好: {json.dumps(user_prefs, ensure_ascii=False)}\n\n"

        enhanced += f"🎯 当前任务: {user_input}\n\n"
        enhanced += "请根据上述信息和你的能力，高效完成用户的请求。"

        return enhanced

    def _learn_from_interaction(self, user_input: str, result: str):
        """从交互中学习"""
        # 分析用户偏好
        input_lower = user_input.lower()

        # 编程语言偏好
        languages = ["python", "javascript", "java", "cpp", "go", "rust"]
        for lang in languages:
            if lang in input_lower:
                self.memory_manager.store_knowledge(
                    "user_preferences",
                    "preferred_language",
                    lang
                )
                break

        # 代码风格偏好
        if "注释" in user_input or "comment" in input_lower:
            self.memory_manager.store_knowledge(
                "user_preferences",
                "wants_comments",
                True
            )

        if "测试" in user_input or "test" in input_lower:
            self.memory_manager.store_knowledge(
                "user_preferences",
                "wants_tests",
                True
            )

    def get_status(self) -> Dict:
        """获取助手状态"""
        conversations = self.memory_manager._load_json(self.memory_manager.conversation_file)
        checkpoints = self.checkpoint_manager.list_checkpoints()

        return {
            "conversations_count": len(conversations.get("conversations", [])),
            "checkpoints_count": len(checkpoints),
            "tools_count": len(self.tools),
            "memory_dir": str(self.memory_manager.memory_dir),
            "model_info": {
                "type": type(self.model).__name__,
                "model_id": getattr(self.model, 'model_id', 'unknown')
            }
        }

    def clear_memory(self, confirm: bool = False):
        """清除记忆（谨慎使用）"""
        if not confirm:
            print("⚠️ 此操作将清除所有记忆数据，请使用 clear_memory(confirm=True) 确认")
            return

        import shutil
        if self.memory_manager.memory_dir.exists():
            shutil.rmtree(self.memory_manager.memory_dir)
            self.memory_manager._init_memory_files()
            print("🗑️ 记忆数据已清除")

    def export_memory(self, export_path: str = "memory_export.json"):
        """导出记忆数据"""
        conversations = self.memory_manager._load_json(self.memory_manager.conversation_file)
        knowledge = self.memory_manager._load_json(self.memory_manager.knowledge_file)
        checkpoints = self.checkpoint_manager.list_checkpoints()

        export_data = {
            "conversations": conversations,
            "knowledge": knowledge,
            "checkpoints": checkpoints,
            "export_time": datetime.now().isoformat()
        }

        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)

        print(f"📤 记忆数据已导出到: {export_path}")

# 示例使用和测试函数
def demo_code_agent():
    """演示 CodeAgent 的使用"""
    print("🚀 CodeAgent 演示开始\n")

    # 创建 CodeAgent 实例
    agent = IntelligentCodeAgent()

    # 显示状态
    status = agent.get_status()
    print(f"📊 助手状态: {json.dumps(status, ensure_ascii=False, indent=2)}\n")

    # 示例任务
    demo_tasks = [
        "创建一个简单的 Python 函数来计算斐波那契数列",
        "分析当前目录的文件结构",
        "创建一个检查点",
        "生成一个简单的 Web API 项目结构"
    ]

    for i, task in enumerate(demo_tasks, 1):
        print(f"🎯 任务 {i}: {task}")
        try:
            result = agent.run(task)
            print(f"✅ 结果: {result[:200]}...\n")
        except Exception as e:
            print(f"❌ 错误: {e}\n")

    print("🎉 演示完成！")

if __name__ == "__main__":
    # 运行演示
    demo_code_agent()
