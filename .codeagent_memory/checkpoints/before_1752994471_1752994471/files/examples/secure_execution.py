"""
安全执行环境示例
展示如何配置和使用安全的代码执行环境（沙箱）
"""

import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool

def demo_local_execution():
    """演示本地执行环境"""
    print("🖥️ === 本地执行环境 ===\n")
    
    print("本地执行特点:")
    print("✅ 速度快，无网络延迟")
    print("✅ 完全控制执行环境")
    print("⚠️ 安全风险：代码直接在本地运行")
    print("⚠️ 需要谨慎处理不可信代码\n")
    
    # 创建使用本地执行器的 Agent
    try:
        model = InferenceClientModel(
            model_id="Qwen/Qwen2.5-Coder-32B-Instruct",
            token=os.environ.get("HUGGINGFACE_TOKEN"),
            provider="auto"
        )
        
        agent = CodeAgent(
            tools=[PythonInterpreterTool()],
            model=model,
            executor_type="local",  # 明确指定本地执行
            stream_outputs=False
        )
        
        print("🧪 测试本地执行:")
        result = agent.run("计算 1+1 并显示 Python 版本信息")
        print(f"结果: {result}\n")
        
    except Exception as e:
        print(f"❌ 本地执行测试失败: {e}\n")

def demo_docker_execution():
    """演示 Docker 执行环境"""
    print("🐳 === Docker 执行环境 ===\n")
    
    print("Docker 执行特点:")
    print("✅ 容器隔离，安全性高")
    print("✅ 环境一致性好")
    print("✅ 可以限制资源使用")
    print("⚠️ 需要安装 Docker")
    print("⚠️ 启动时间稍长\n")
    
    # Docker 配置示例
    docker_config = {
        "image": "python:3.11-slim",
        "timeout": 30,
        "memory_limit": "512m",
        "cpu_limit": "1.0"
    }
    
    print("Docker 配置示例:")
    for key, value in docker_config.items():
        print(f"  {key}: {value}")
    
    print("\n创建 Docker Agent 的代码:")
    code_example = '''
    from smolagents import CodeAgent, DockerExecutor
    
    # 配置 Docker 执行器
    docker_executor = DockerExecutor(
        image="python:3.11-slim",
        timeout=30,
        memory_limit="512m"
    )
    
    agent = CodeAgent(
        tools=[PythonInterpreterTool()],
        model=model,
        executor_type="docker",
        executor_kwargs={
            "image": "python:3.11-slim",
            "timeout": 30
        }
    )
    '''
    print(code_example)

def demo_e2b_execution():
    """演示 E2B 执行环境"""
    print("☁️ === E2B 云端执行环境 ===\n")
    
    print("E2B 执行特点:")
    print("✅ 云端沙箱，完全隔离")
    print("✅ 无需本地配置")
    print("✅ 支持多种编程语言")
    print("✅ 实时协作功能")
    print("⚠️ 需要网络连接")
    print("⚠️ 需要 E2B API 密钥\n")
    
    print("E2B 配置步骤:")
    steps = [
        "1. 注册 E2B 账户: https://e2b.dev/",
        "2. 获取 API 密钥",
        "3. 设置环境变量: export E2B_API_KEY='your_key'",
        "4. 安装 E2B SDK: pip install e2b",
        "5. 配置 Agent 使用 E2B 执行器"
    ]
    
    for step in steps:
        print(f"  {step}")
    
    print("\n创建 E2B Agent 的代码:")
    code_example = '''
    from smolagents import CodeAgent, E2BExecutor
    
    agent = CodeAgent(
        tools=[PythonInterpreterTool()],
        model=model,
        executor_type="e2b",
        executor_kwargs={
            "api_key": os.environ.get("E2B_API_KEY"),
            "template": "python3"
        }
    )
    '''
    print(code_example)

def demo_security_considerations():
    """演示安全考虑因素"""
    print("🔒 === 安全考虑因素 ===\n")
    
    security_guide = """
    代码执行安全最佳实践:
    
    🚨 高风险操作:
    ├── 文件系统访问 (读写敏感文件)
    ├── 网络请求 (访问内部服务)
    ├── 系统命令执行 (os.system, subprocess)
    ├── 导入危险模块 (eval, exec, __import__)
    └── 资源消耗 (无限循环, 大内存分配)
    
    🛡️ 防护措施:
    ├── 沙箱执行 (Docker, E2B)
    ├── 资源限制 (CPU, 内存, 时间)
    ├── 网络隔离 (禁止外网访问)
    ├── 文件系统限制 (只读挂载)
    └── 模块白名单 (限制可导入模块)
    
    📋 安全检查清单:
    ├── ✅ 使用沙箱环境
    ├── ✅ 设置执行超时
    ├── ✅ 限制内存使用
    ├── ✅ 监控系统调用
    ├── ✅ 记录执行日志
    └── ✅ 定期安全审计
    """
    
    print(security_guide)

def demo_execution_comparison():
    """比较不同执行环境"""
    print("⚖️ === 执行环境比较 ===\n")
    
    comparison = """
    执行环境对比:
    
    特性          | 本地执行 | Docker  | E2B云端
    -------------|---------|---------|--------
    安全性        | ⭐⭐     | ⭐⭐⭐⭐  | ⭐⭐⭐⭐⭐
    性能          | ⭐⭐⭐⭐⭐ | ⭐⭐⭐    | ⭐⭐⭐
    配置复杂度     | ⭐      | ⭐⭐⭐    | ⭐⭐
    网络依赖      | 无      | 无      | 必需
    资源隔离      | 无      | 完全    | 完全
    多语言支持     | 有限    | 完全    | 完全
    
    推荐使用场景:
    
    🏠 本地执行:
    ├── 开发测试阶段
    ├── 可信代码执行
    └── 性能要求极高的场景
    
    🐳 Docker 执行:
    ├── 生产环境部署
    ├── 需要环境一致性
    └── 有本地 Docker 环境
    
    ☁️ E2B 执行:
    ├── 云端应用
    ├── 多用户共享
    └── 无需维护基础设施
    """
    
    print(comparison)

def demo_practical_examples():
    """实际应用示例"""
    print("💼 === 实际应用示例 ===\n")
    
    examples = """
    实际应用场景:
    
    🎓 教育平台:
    ├── 在线编程练习
    ├── 代码自动评测
    └── 安全的学生代码执行
    → 推荐: E2B (多用户隔离)
    
    🏢 企业应用:
    ├── 数据分析脚本执行
    ├── 自动化任务处理
    └── 内部工具开发
    → 推荐: Docker (安全可控)
    
    🔬 研究开发:
    ├── 算法原型验证
    ├── 数据处理实验
    └── 模型训练脚本
    → 推荐: 本地执行 (快速迭代)
    
    🌐 SaaS 服务:
    ├── 用户自定义脚本
    ├── API 数据处理
    └── 多租户代码执行
    → 推荐: E2B (云端扩展)
    """
    
    print(examples)

if __name__ == "__main__":
    print("🔒 欢迎使用 smolagents 安全执行环境示例！\n")
    
    # 演示不同执行环境
    demo_local_execution()
    demo_docker_execution()
    demo_e2b_execution()
    
    # 安全指南
    demo_security_considerations()
    demo_execution_comparison()
    demo_practical_examples()
    
    print("\n💡 下一步:")
    print("1. 根据需求选择合适的执行环境")
    print("2. 配置相应的安全参数")
    print("3. 测试代码执行的安全性")
    print("4. 监控和审计执行日志")
    print("\n🔗 相关资源:")
    print("- E2B 官网: https://e2b.dev/")
    print("- Docker 文档: https://docs.docker.com/")
    print("- smolagents 安全指南: https://huggingface.co/docs/smolagents")
