"""
smolagents 学习项目
基于 Hugging Face smolagents 库的 Agent 开发示例

这是一个完整的 smolagents 学习项目，包含：
- 基础 Agent 使用
- 自定义工具开发
- 多 Agent 协作
- 安全执行环境配置
"""

# 主学习文件 - 项目概览和快速开始指南

def main():
    """主函数 - 展示项目概览"""
    print("🤖 欢迎来到 smolagents 学习项目！\n")

    print("📚 项目内容:")
    print("1. 基础 Agent 示例 - examples/basic_agent.py")
    print("2. 自定义工具开发 - examples/custom_tools.py")
    print("3. 多 Agent 协作 - examples/multi_agent.py")
    print("4. 安全执行环境 - examples/secure_execution.py")
    print("5. 简单演示 - simple_demo.py")

    print("\n🛠️ 自定义工具:")
    print("- WeatherTool: 天气查询工具")
    print("- DataAnalysisTool: 数据分析工具")
    print("- TimerTool: 计时器工具")
    print("- FileManagerTool: 文件管理工具")

    print("\n🚀 快速开始:")
    print("1. 运行简单演示: python simple_demo.py")
    print("2. 测试自定义工具: python examples/custom_tools.py")
    print("3. 体验多 Agent 协作: python examples/multi_agent.py")

    print("\n💡 提示:")
    print("- 设置 HUGGINGFACE_TOKEN 环境变量以使用外部模型")
    print("- 查看 README.md 了解详细说明")
    print("- 参考 config/agent_config.py 进行配置")

def show_smolagents_overview():
    """展示 smolagents 概览"""
    print("\n🏗️ smolagents 架构:")
    print("Agent (智能代理) + Model (语言模型) + Tools (工具) + Executor (执行器)")

    print("\n🔥 核心特性:")
    print("✨ 代码优先的 Agent 设计")
    print("🧑‍💻 支持多种语言模型")
    print("🛠️ 丰富的工具生态")
    print("🔒 安全的执行环境")
    print("🤗 Hub 集成")

if __name__ == "__main__":
    main()
    show_smolagents_overview()