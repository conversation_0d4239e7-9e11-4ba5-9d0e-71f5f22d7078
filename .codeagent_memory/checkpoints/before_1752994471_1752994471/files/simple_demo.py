"""
简单的 smolagents 演示
展示基本的工具使用，不依赖外部模型
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from tools.custom_tools import WeatherTool, DataAnalysisTool, TimerTool

def demo_custom_tools_directly():
    """直接演示自定义工具的功能"""
    print("🛠️ === 直接测试自定义工具 ===\n")
    
    # 1. 测试天气工具
    print("🌤️ 测试天气工具:")
    weather_tool = WeatherTool()
    result1 = weather_tool.forward("北京")
    print(f"  {result1}")
    result2 = weather_tool.forward("成都")  # 不在预设列表中
    print(f"  {result2}\n")
    
    # 2. 测试数据分析工具
    print("📊 测试数据分析工具:")
    data_tool = DataAnalysisTool()
    test_data = "[10, 20, 30, 40, 50, 60, 70, 80, 90, 100]"
    result3 = data_tool.forward(test_data, "basic_stats")
    print(f"  基础统计:\n{result3}")
    
    result4 = data_tool.forward(test_data, "distribution")
    print(f"  分布分析:\n{result4}")
    
    # 3. 测试计时器工具
    print("⏱️ 测试计时器工具:")
    timer_tool = TimerTool()
    result5 = timer_tool.forward("current")
    print(f"  {result5}")
    result6 = timer_tool.forward("start")
    print(f"  {result6}")
    result7 = timer_tool.forward("sleep", 1)
    print(f"  {result7}")
    result8 = timer_tool.forward("stop")
    print(f"  {result8}\n")

def show_smolagents_architecture():
    """展示 smolagents 的架构和概念"""
    print("🏗️ === smolagents 架构概览 ===\n")
    
    architecture = """
    smolagents 核心组件:
    
    1. 🤖 Agent (智能代理)
       ├── CodeAgent: 将动作写成 Python 代码
       └── ToolCallingAgent: 传统的工具调用方式
    
    2. 🧠 Model (语言模型)
       ├── InferenceClientModel: HF Inference API
       ├── OpenAIServerModel: OpenAI 兼容服务器
       ├── LiteLLMModel: 支持 100+ LLM
       └── TransformersModel: 本地 transformers 模型
    
    3. 🛠️ Tools (工具)
       ├── 内置工具: WebSearchTool, PythonInterpreterTool 等
       └── 自定义工具: 继承 Tool 类实现
    
    4. 🔧 Executors (执行器)
       ├── LocalPythonExecutor: 本地执行
       ├── E2BExecutor: E2B 沙箱
       └── DockerExecutor: Docker 容器
    
    工作流程:
    用户请求 → Agent 分析 → 生成代码 → 调用工具 → 执行代码 → 返回结果
    """
    
    print(architecture)

def show_code_agent_example():
    """展示 CodeAgent 的代码生成示例"""
    print("💻 === CodeAgent 代码生成示例 ===\n")
    
    example_code = '''
    # CodeAgent 生成的代码示例
    
    # 1. 数据处理
    data = [10, 25, 30, 45, 50]
    mean = sum(data) / len(data)
    print(f"平均值: {mean}")
    
    # 2. 工具调用
    weather_result = weather_tool("北京")
    print(weather_result)
    
    # 3. 复杂逻辑
    for city in ["北京", "上海", "广州"]:
        weather = weather_tool(city)
        analysis = data_analysis(f"[{weather.temperature}]", "basic_stats")
        print(f"{city}: {analysis}")
    
    # 4. 最终答案
    final_answer("分析完成，所有城市的天气数据已处理")
    '''
    
    print("CodeAgent 的优势:")
    print("✅ 更自然的代码表达")
    print("✅ 支持复杂的控制流程")
    print("✅ 可以组合多个工具调用")
    print("✅ 减少 30% 的步骤数")
    print("\n示例代码:")
    print(example_code)

def show_project_structure():
    """展示项目结构和使用指南"""
    print("📁 === 项目结构和使用指南 ===\n")
    
    structure = """
    项目文件说明:
    
    📄 learn.py                 # 主学习文件
    📄 requirements.txt         # 依赖列表
    📄 README.md               # 项目说明
    
    📁 examples/               # 示例代码
    ├── basic_agent.py         # 基础 Agent 使用
    ├── custom_tools.py        # 自定义工具示例
    ├── multi_agent.py         # 多 Agent 协作 (待创建)
    └── secure_execution.py    # 安全执行环境 (待创建)
    
    📁 tools/                  # 自定义工具
    ├── __init__.py
    └── custom_tools.py        # 工具实现
    
    📁 config/                 # 配置文件
    └── agent_config.py        # Agent 配置
    
    使用步骤:
    1. 安装依赖: pip install -r requirements.txt
    2. 设置环境变量 (可选): export HUGGINGFACE_TOKEN="your_token"
    3. 运行示例: python examples/basic_agent.py
    4. 创建自定义工具: 参考 tools/custom_tools.py
    5. 构建自己的 Agent: 参考 examples/ 中的示例
    """
    
    print(structure)

if __name__ == "__main__":
    print("🤖 欢迎使用 smolagents 学习项目！\n")
    
    # 直接测试自定义工具
    demo_custom_tools_directly()
    
    # 展示架构
    show_smolagents_architecture()
    
    # 展示代码示例
    show_code_agent_example()
    
    # 展示项目结构
    show_project_structure()
    
    print("✅ 演示完成！")
    print("\n💡 下一步:")
    print("1. 设置 HUGGINGFACE_TOKEN 环境变量")
    print("2. 运行 python examples/basic_agent.py")
    print("3. 尝试修改和创建自己的工具")
    print("4. 探索多 Agent 协作功能")
