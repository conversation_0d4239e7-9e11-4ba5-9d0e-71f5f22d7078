{"code_patterns": {}, "project_structures": {}, "user_preferences": {}, "learned_solutions": {}, "execution_errors": {"error_1752994470": {"value": {"user_input": "创建一个简单的 Python 函数来计算斐波那契数列", "error": "Error in generating model output:\nProvider 'featherless-ai' not supported. Available values: 'auto' or any provider from ['black-forest-labs', 'cerebras', 'cohere', 'fal-ai', 'fireworks-ai', 'hf-inference', 'hyperbolic', 'nebius', 'novita', 'openai', 'replicate', 'sambanova', 'together'].Passing 'auto' (default value) will automatically select the first provider available for the model, sorted by the user's order in https://hf.co/settings/inference-providers.", "timestamp": "2025-07-20T14:54:30.996048"}, "timestamp": "2025-07-20T14:54:30.996391"}, "error_1752994471": {"value": {"user_input": "生成一个简单的 Web API 项目结构", "error": "Error in generating model output:\nProvider 'featherless-ai' not supported. Available values: 'auto' or any provider from ['black-forest-labs', 'cerebras', 'cohere', 'fal-ai', 'fireworks-ai', 'hf-inference', 'hyperbolic', 'nebius', 'novita', 'openai', 'replicate', 'sambanova', 'together'].Passing 'auto' (default value) will automatically select the first provider available for the model, sorted by the user's order in https://hf.co/settings/inference-providers.", "timestamp": "2025-07-20T14:54:31.068427"}, "timestamp": "2025-07-20T14:54:31.068471"}}}