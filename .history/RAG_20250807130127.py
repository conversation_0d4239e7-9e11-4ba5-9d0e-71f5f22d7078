from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import Chroma
from langchain.chat_models import ChatOpenAI
from langchain.chains import ConversationalRetrievalChain
from langchain.memory import ConversationBufferMemory
from langchain.document_loaders import TextLoader
from langchain.text_splitter import CharacterTextSplitter
import os

# 设置OpenAI API密钥
os.environ["OPENAI_API_KEY"] = "your-api-key"

# 初始化文本加载器和分词器
loader = TextLoader("sql_examples.txt")  # 假设有一个包含SQL示例的文本文件
documents = loader.load()

text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
docs = text_splitter.split_documents(documents)

# 初始化embeddings和向量存储
embeddings = OpenAIEmbeddings()
vectorstore = Chroma.from_documents(docs, embeddings)

# 初始化对话模型和记忆
llm = ChatOpenAI(temperature=0, model_name="gpt-3.5-turbo")
memory = ConversationBufferMemory(
    memory_key="chat_history",
    return_messages=True
)

# 创建对话检索链
qa_chain = ConversationalRetrievalChain.from_llm(
    llm=llm,
    retriever=vectorstore.as_retriever(),
    memory=memory,
)

# 示例查询函数
def natural_language_to_sql(query: str) -> str:
    """
    将自然语言转换为SQL查询
    :param query: 自然语言查询
    :return: SQL查询语句
    """
    result = qa_chain({"question": f"将以下自然语言转换为SQL查询: {query}"})
    return result["answer"]

# 使用示例
if __name__ == "__main__":
    query = "查找所有年龄大于25岁的用户的姓名和邮箱"
    sql = natural_language_to_sql(query)
    print(f"自然语言查询: {query}")
    print(f"SQL查询: {sql}")
