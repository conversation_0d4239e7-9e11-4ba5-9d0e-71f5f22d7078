{"users": {"columns": [{"name": "id", "type": "INTEGER"}, {"name": "name", "type": "TEXT"}, {"name": "email", "type": "TEXT"}, {"name": "age", "type": "INTEGER"}, {"name": "created_at", "type": "TIMESTAMP"}], "foreign_keys": []}, "orders": {"columns": [{"name": "id", "type": "INTEGER"}, {"name": "user_id", "type": "INTEGER"}, {"name": "product_id", "type": "INTEGER"}, {"name": "quantity", "type": "INTEGER"}, {"name": "order_date", "type": "TIMESTAMP"}], "foreign_keys": [{"column": "user_id", "reference_table": "users", "reference_column": "id"}, {"column": "product_id", "reference_table": "products", "reference_column": "id"}]}, "products": {"columns": [{"name": "id", "type": "INTEGER"}, {"name": "name", "type": "TEXT"}, {"name": "price", "type": "REAL"}, {"name": "category", "type": "TEXT"}], "foreign_keys": []}, "reviews": {"columns": [{"name": "id", "type": "INTEGER"}, {"name": "user_id", "type": "INTEGER"}, {"name": "product_id", "type": "INTEGER"}, {"name": "rating", "type": "INTEGER"}, {"name": "comment", "type": "TEXT"}, {"name": "review_date", "type": "TIMESTAMP"}], "foreign_keys": [{"column": "user_id", "reference_table": "users", "reference_column": "id"}, {"column": "product_id", "reference_table": "products", "reference_column": "id"}]}}