"""
自定义工具使用示例
展示如何创建和使用自定义工具来扩展 Agent 能力
"""

import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool
from tools.custom_tools import WeatherTool, DataAnalysisTool, TimerTool, FileManagerTool

def create_agent_with_custom_tools():
    """创建带有自定义工具的 Agent"""
    
    # 1. 选择模型
    model = InferenceClientModel(
        model_id="Qwen/Qwen2.5-Coder-32B-Instruct",
        token=os.environ.get("HUGGINGFACE_TOKEN")
    )
    
    # 2. 组合内置工具和自定义工具
    tools = [
        PythonInterpreterTool(),    # 内置工具
        WeatherTool(),              # 自定义天气工具
        DataAnalysisTool(),         # 自定义数据分析工具
        TimerTool(),                # 自定义计时器工具
        FileManagerTool(),          # 自定义文件管理工具
    ]
    
    # 3. 创建 Agent
    agent = CodeAgent(
        tools=tools,
        model=model,
        max_iterations=15,
        stream_outputs=True,
        verbose=True
    )
    
    return agent

def demo_weather_tool():
    """演示天气工具"""
    agent = create_agent_with_custom_tools()
    
    print("🌤️ === 天气工具演示 ===\n")
    
    result = agent.run("""
    请帮我查询以下城市的天气情况：
    1. 北京
    2. 上海
    3. 成都（这个城市不在预设列表中）
    
    然后对这些城市的温度进行比较，告诉我哪个城市最热，哪个最冷。
    """)
    
    print(f"结果: {result}\n")

def demo_data_analysis_tool():
    """演示数据分析工具"""
    agent = create_agent_with_custom_tools()
    
    print("📊 === 数据分析工具演示 ===\n")
    
    result = agent.run("""
    我有一组销售数据：[120, 150, 180, 200, 175, 190, 210, 165, 185, 195]
    
    请使用数据分析工具：
    1. 计算基础统计信息
    2. 分析数据分布
    3. 用 Python 创建一个简单的可视化图表
    """)
    
    print(f"结果: {result}\n")

def demo_timer_tool():
    """演示计时器工具"""
    agent = create_agent_with_custom_tools()
    
    print("⏱️ === 计时器工具演示 ===\n")
    
    result = agent.run("""
    请演示计时器工具的使用：
    1. 获取当前时间
    2. 启动计时器
    3. 等待 2 秒
    4. 停止计时器并显示耗时
    """)
    
    print(f"结果: {result}\n")

def demo_file_manager_tool():
    """演示文件管理工具"""
    agent = create_agent_with_custom_tools()
    
    print("📁 === 文件管理工具演示 ===\n")
    
    result = agent.run("""
    请使用文件管理工具：
    1. 创建一个名为 'test_data.txt' 的文件，内容为一些示例数据
    2. 读取这个文件的内容
    3. 向文件中写入新的内容
    4. 再次读取文件确认内容已更新
    5. 列出当前目录的所有文件
    """)
    
    print(f"结果: {result}\n")

def demo_combined_workflow():
    """演示组合工作流程"""
    agent = create_agent_with_custom_tools()
    
    print("🔄 === 组合工作流程演示 ===\n")
    
    result = agent.run("""
    请完成一个完整的数据处理工作流程：
    
    1. 查询北京和上海的天气
    2. 创建一个包含这两个城市温度数据的文件
    3. 读取文件并对温度数据进行统计分析
    4. 生成一个简单的温度对比图表
    5. 将分析结果保存到新文件中
    
    整个过程请使用计时器记录耗时。
    """)
    
    print(f"结果: {result}\n")

def demo_tool_creation_guide():
    """展示如何创建自定义工具的指南"""
    print("🛠️ === 自定义工具创建指南 ===\n")
    
    guide = """
    创建自定义工具的步骤：
    
    1. 继承 smolagents.Tool 类
    2. 定义工具属性：
       - name: 工具名称
       - description: 工具描述
       - inputs: 输入参数定义
       - output_type: 输出类型
    
    3. 实现 forward() 方法
    
    示例代码：
    ```python
    from smolagents import Tool
    
    class MyCustomTool(Tool):
        name = "my_tool"
        description = "我的自定义工具"
        inputs = {
            "param1": {
                "type": "string",
                "description": "参数1的描述"
            }
        }
        output_type = "string"
        
        def forward(self, param1: str) -> str:
            # 实现工具逻辑
            return f"处理结果: {param1}"
    ```
    
    4. 在 Agent 中使用：
    ```python
    tools = [MyCustomTool()]
    agent = CodeAgent(tools=tools, model=model)
    ```
    """
    
    print(guide)

if __name__ == "__main__":
    try:
        print("🤖 欢迎使用 smolagents 自定义工具示例！\n")
        
        # 检查环境
        if not os.environ.get("HUGGINGFACE_TOKEN"):
            print("💡 提示: 设置 HUGGINGFACE_TOKEN 环境变量可以访问更多模型")
            print("   export HUGGINGFACE_TOKEN='your_token_here'\n")
        
        # 展示工具创建指南
        demo_tool_creation_guide()
        
        # 运行各种演示
        demo_weather_tool()
        demo_data_analysis_tool()
        demo_timer_tool()
        demo_file_manager_tool()
        demo_combined_workflow()
        
        print("✅ 所有自定义工具演示完成！")
        
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        print("\n💡 可能的解决方案:")
        print("1. 确保已安装 smolagents[toolkit]")
        print("2. 检查自定义工具的导入路径")
        print("3. 确认网络连接正常")
