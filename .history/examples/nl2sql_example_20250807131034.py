#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NL2SQL RAG系统使用示例

本示例展示如何使用NL2SQL系统将自然语言查询转换为SQL查询，并执行查询获取结果。
"""

import os
import sys
import json
import sqlite3

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入NL2SQL系统
from nl2sql_rag_system import NL2SQLSystem

# 设置OpenAI API密钥
os.environ["OPENAI_API_KEY"] = "your-api-key"

# 创建示例SQLite数据库
def create_sample_database(db_path):
    """
    创建示例SQLite数据库
    
    :param db_path: 数据库文件路径
    """
    # 连接到SQLite数据库（如果不存在则创建）
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建users表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT UNIQUE,
        age INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """)
    
    # 创建products表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        price REAL NOT NULL,
        category TEXT
    )
    """)
    
    # 创建orders表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS orders (
        id INTEGER PRIMARY KEY,
        user_id INTEGER,
        product_id INTEGER,
        quantity INTEGER DEFAULT 1,
        order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (product_id) REFERENCES products (id)
    )
    """)
    
    # 创建reviews表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS reviews (
        id INTEGER PRIMARY KEY,
        user_id INTEGER,
        product_id INTEGER,
        rating INTEGER CHECK(rating BETWEEN 1 AND 5),
        comment TEXT,
        review_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (product_id) REFERENCES products (id)
    )
    """)
    
    # 插入示例数据 - 用户
    users = [
        (1, "张三", "<EMAIL>", 28),
        (2, "李四", "<EMAIL>", 35),
        (3, "王五", "<EMAIL>", 22),
        (4, "赵六", "<EMAIL>", 45),
        (5, "钱七", "<EMAIL>", 19)
    ]
    cursor.executemany("INSERT OR REPLACE INTO users (id, name, email, age) VALUES (?, ?, ?, ?)", users)
    
    # 插入示例数据 - 产品
    products = [
        (1, "笔记本电脑", 5999.99, "电子产品"),
        (2, "智能手机", 3999.99, "电子产品"),
        (3, "耳机", 299.99, "电子产品"),
        (4, "书籍", 59.99, "文具"),
        (5, "背包", 199.99, "服饰"),
        (6, "水杯", 49.99, "日用品")
    ]
    cursor.executemany("INSERT OR REPLACE INTO products (id, name, price, category) VALUES (?, ?, ?, ?)", products)
    
    # 插入示例数据 - 订单
    orders = [
        (1, 1, 1, 1, "2023-01-15"),
        (2, 1, 3, 2, "2023-02-20"),
        (3, 2, 2, 1, "2023-01-10"),
        (4, 3, 5, 1, "2023-03-05"),
        (5, 4, 6, 3, "2023-02-28"),
        (6, 5, 4, 2, "2023-03-15"),
        (7, 2, 1, 1, "2023-04-10"),
        (8, 3, 3, 1, "2023-04-20")
    ]
    cursor.executemany("INSERT OR REPLACE INTO orders (id, user_id, product_id, quantity, order_date) VALUES (?, ?, ?, ?, ?)", orders)
    
    # 插入示例数据 - 评论
    reviews = [
        (1, 1, 1, 5, "非常好用的笔记本电脑", "2023-01-20"),
        (2, 1, 3, 4, "音质不错的耳机", "2023-02-25"),
        (3, 2, 2, 5, "很满意这款手机", "2023-01-15"),
        (4, 3, 5, 3, "背包质量一般", "2023-03-10"),
        (5, 4, 6, 4, "水杯保温效果好", "2023-03-05")
    ]
    cursor.executemany("INSERT OR REPLACE INTO reviews (id, user_id, product_id, rating, comment, review_date) VALUES (?, ?, ?, ?, ?, ?)", reviews)
    
    # 提交事务并关闭连接
    conn.commit()
    conn.close()
    
    print(f"示例数据库已创建: {db_path}")

# 主函数
def main():
    # 数据库文件路径
    db_path = "example.db"
    
    # 创建示例数据库
    create_sample_database(db_path)
    
    # 初始化NL2SQL系统
    nl2sql_system = NL2SQLSystem(
        examples_path="../sql_examples.txt",
        db_schema_path="../db_schema.json",
        model_name="gpt-3.5-turbo"
    )
    
    # 测试查询列表
    queries = [
        "查找所有年龄大于25岁的用户的姓名和邮箱",
        "统计每个产品类别的平均价格",
        "找出订单数量最多的前3名用户",
        "查询所有购买过电子产品的用户",
        "计算每个用户的总消费金额"
    ]
    
    # 执行查询并显示结果
    for i, query in enumerate(queries, 1):
        print(f"\n===== 查询 {i} =====")
        print(f"自然语言查询: {query}")
        
        # 转换为SQL
        sql = nl2sql_system.natural_language_to_sql(query)
        print(f"SQL查询: {sql}")
        
        # 执行SQL查询
        try:
            results = nl2sql_system.execute_sql(sql, db_path)
            print(f"查询结果: {json.dumps(results, ensure_ascii=False, indent=2)}")
        except Exception as e:
            print(f"执行查询失败: {e}")

if __name__ == "__main__":
    main()