from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import Chroma
from langchain.chat_models import ChatOpenAI
from langchain.chains import ConversationalRetrievalChain
from langchain.memory import ConversationBufferMemory
from langchain.document_loaders import TextLoader, CSVLoader
from langchain.text_splitter import CharacterTextSplitter
from langchain.prompts import PromptTemplate, ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langchain.chains.question_answering import load_qa_chain
from langchain.schema import Document
import os
import json
import sqlite3
from typing import List, Dict, Any, Optional

# 设置OpenAI API密钥
os.environ["OPENAI_API_KEY"] = "your-api-key"

class NL2SQLSystem:
    """
    基于LangChain与LLM的RAG系统，实现自然语言自动转换为SQL查询
    通过语义检索机制与动态Prompt模板设计，提高SQL生成准确率
    """
    
    def __init__(self, 
                 examples_path: str = "sql_examples.txt",
                 db_schema_path: Optional[str] = None,
                 vector_db_path: str = "./chroma_db",
                 model_name: str = "gpt-3.5-turbo",
                 temperature: float = 0):
        """
        初始化NL2SQL系统
        
        :param examples_path: SQL示例文件路径
        :param db_schema_path: 数据库结构描述文件路径
        :param vector_db_path: 向量数据库存储路径
        :param model_name: 使用的LLM模型名称
        :param temperature: 模型温度参数
        """
        self.examples_path = examples_path
        self.db_schema_path = db_schema_path
        self.vector_db_path = vector_db_path
        self.model_name = model_name
        self.temperature = temperature
        
        # 初始化组件
        self.llm = None
        self.memory = None
        self.vectorstore = None
        self.qa_chain = None
        self.db_schema = None
        
        # 加载数据库结构信息（如果提供）
        if db_schema_path:
            self._load_db_schema()
        
        # 初始化系统
        self._initialize_system()
    
    def _load_db_schema(self):
        """
        加载数据库结构信息
        """
        try:
            with open(self.db_schema_path, 'r') as f:
                self.db_schema = json.load(f)
        except Exception as e:
            print(f"加载数据库结构信息失败: {e}")
            self.db_schema = None
    
    def _initialize_system(self):
        """
        初始化RAG系统的各个组件
        """
        # 初始化文本加载器和分词器
        if self.examples_path.endswith('.csv'):
            loader = CSVLoader(self.examples_path)
        else:
            loader = TextLoader(self.examples_path)
        
        documents = loader.load()
        #文档加载，定义每个文本块的最大字符数
        text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
        docs = text_splitter.split_documents(documents)
        
        # 初始化embeddings和向量存储
        embeddings = OpenAIEmbeddings()
        self.vectorstore = Chroma.from_documents(
            documents=docs, 
            embedding=embeddings,
            persist_directory=self.vector_db_path
        )
        self.vectorstore.persist()
        
        # 初始化对话模型和记忆
        self.llm = ChatOpenAI(temperature=self.temperature, model_name=self.model_name)
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )
        
        # 创建对话检索链
        self._create_qa_chain()
    
    def _create_qa_chain(self):
        """
        创建问答链，使用自定义提示词模板
        """
        # 系统提示词模板
        system_template = """
        你是一个专业的SQL专家，擅长将自然语言转换为准确的SQL查询语句。
        
        {db_schema}
        
        请遵循以下规则：
        1. 仅返回有效的SQL查询语句，不要包含任何解释或注释
        2. 确保SQL语法正确，并与提供的数据库结构兼容
        3. 使用标准SQL语法，避免使用特定数据库的专有功能
        4. 如果查询涉及多个表，请使用适当的JOIN操作
        5. 如果自然语言查询不明确，请生成最合理的SQL查询
        """
        
        # 人类提示词模板
        human_template = """
        基于以下相似的示例和上下文信息，将自然语言查询转换为SQL查询语句：
        
        相似示例：
        {context}
        
        自然语言查询：{question}
        
        SQL查询：
        """
        
        # 创建系统消息提示词模板
        system_message_prompt = SystemMessagePromptTemplate.from_template(system_template)
        
        # 创建人类消息提示词模板
        human_message_prompt = HumanMessagePromptTemplate.from_template(human_template)
        
        # 组合成聊天提示词模板
        chat_prompt = ChatPromptTemplate.from_messages([system_message_prompt, human_message_prompt])
        
        # 创建问答链
        doc_chain = load_qa_chain(
            llm=self.llm,
            chain_type="stuff",
            prompt=chat_prompt
        )
        
        # 创建对话检索链
        self.qa_chain = ConversationalRetrievalChain(
            retriever=self.vectorstore.as_retriever(search_kwargs={"k": 5}),
            combine_docs_chain=doc_chain,
            memory=self.memory,
        )
    
    def _get_db_schema_str(self) -> str:
        """
        将数据库结构信息转换为字符串格式
        
        :return: 数据库结构描述字符串
        """
        if not self.db_schema:
            return "数据库结构信息未提供。"
        
        schema_str = "数据库结构信息：\n"
        
        for table_name, table_info in self.db_schema.items():
            schema_str += f"表名: {table_name}\n"
            schema_str += "列:"  
            
            for column in table_info["columns"]:
                col_name = column["name"]
                col_type = column["type"]
                schema_str += f" {col_name}({col_type}),"
            
            schema_str = schema_str.rstrip(",") + "\n"
            
            if "foreign_keys" in table_info and table_info["foreign_keys"]:
                schema_str += "外键: "
                for fk in table_info["foreign_keys"]:
                    schema_str += f"{fk['column']} -> {fk['reference_table']}.{fk['reference_column']}, "
                schema_str = schema_str.rstrip(", ") + "\n"
            
            schema_str += "\n"
        
        return schema_str
    
    def natural_language_to_sql(self, query: str) -> str:
        """
        将自然语言转换为SQL查询
        
        :param query: 自然语言查询
        :return: SQL查询语句
        """
        # 准备数据库结构信息
        db_schema_str = self._get_db_schema_str()
        
        # 执行查询
        result = self.qa_chain(
            {
                "question": query,
                "db_schema": db_schema_str
            }
        )
        
        return result["answer"]
    
    def execute_sql(self, sql_query: str, db_path: str) -> List[Dict[str, Any]]:
        """
        执行SQL查询并返回结果
        
        :param sql_query: SQL查询语句
        :param db_path: SQLite数据库文件路径
        :return: 查询结果列表
        """
        try:
            # 连接到SQLite数据库
            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 执行查询
            cursor.execute(sql_query)
            
            # 获取结果
            rows = cursor.fetchall()
            
            # 转换为字典列表
            results = [dict(row) for row in rows]
            
            # 关闭连接
            conn.close()
            
            return results
        except Exception as e:
            print(f"执行SQL查询失败: {e}")
            return []

# 使用示例
if __name__ == "__main__":
    # 创建示例数据库结构描述
    db_schema = {
        "users": {
            "columns": [
                {"name": "id", "type": "INTEGER"},
                {"name": "name", "type": "TEXT"},
                {"name": "email", "type": "TEXT"},
                {"name": "age", "type": "INTEGER"},
                {"name": "created_at", "type": "TIMESTAMP"}
            ],
            "foreign_keys": []
        },
        "orders": {
            "columns": [
                {"name": "id", "type": "INTEGER"},
                {"name": "user_id", "type": "INTEGER"},
                {"name": "product_id", "type": "INTEGER"},
                {"name": "quantity", "type": "INTEGER"},
                {"name": "order_date", "type": "TIMESTAMP"}
            ],
            "foreign_keys": [
                {"column": "user_id", "reference_table": "users", "reference_column": "id"},
                {"column": "product_id", "reference_table": "products", "reference_column": "id"}
            ]
        },
        "products": {
            "columns": [
                {"name": "id", "type": "INTEGER"},
                {"name": "name", "type": "TEXT"},
                {"name": "price", "type": "REAL"},
                {"name": "category", "type": "TEXT"}
            ],
            "foreign_keys": []
        }
    }
    
    # 将数据库结构保存到文件
    with open("db_schema.json", "w") as f:
        json.dump(db_schema, f, indent=2)
    
    # 初始化NL2SQL系统
    nl2sql_system = NL2SQLSystem(
        examples_path="sql_examples.txt",
        db_schema_path="db_schema.json",
        model_name="gpt-3.5-turbo"
    )
    
    # 测试查询
    queries = [
        "查找所有年龄大于25岁的用户的姓名和邮箱",
        "统计每个产品类别的平均价格",
        "找出订单数量最多的前5名用户",
        "查询2023年1月1日之后下单的所有订单及其产品名称"
    ]
    
    for query in queries:
        sql = nl2sql_system.natural_language_to_sql(query)
        print(f"\n自然语言查询: {query}")
        print(f"SQL查询: {sql}")