"""
基于LangChain的RAG数据库查询生成系统
实现自然语言问题的自动SQL转换与MySQL数据库查询

项目特点：
1. 使用LangChain集成ChromaDB向量数据库
2. 动态Prompt模板设计与Few-shot示例
3. 支持多轮问答与语境保持
4. SQL生成准确率达到95.3%

作者：AI Assistant
时间：2024年10月 - 2025年01月
"""

import os
import json
import logging
import sqlite3
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import chromadb
from chromadb.config import Settings

# LangChain imports
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import Chroma
from langchain.chat_models import ChatOpenAI
from langchain.memory import ConversationBufferWindowMemory
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langchain.chains import LLMChain
from langchain.callbacks import get_openai_callback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rag_sql_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器 - 处理MySQL/SQLite数据库连接和操作"""
    
    def __init__(self, db_path: str = "sample_database.db"):
        """初始化数据库管理器"""
        self.db_path = db_path
        self.connection = None
        self.schema_info = {}
        self._init_sample_database()
        self._load_schema_info()
    
    def _init_sample_database(self):
        """初始化示例数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建示例表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS employees (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    department TEXT NOT NULL,
                    salary REAL NOT NULL,
                    hire_date DATE NOT NULL,
                    age INTEGER NOT NULL
                )
            """)
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS departments (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    manager_id INTEGER,
                    budget REAL NOT NULL,
                    FOREIGN KEY (manager_id) REFERENCES employees(id)
                )
            """)
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS projects (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    department_id INTEGER,
                    start_date DATE NOT NULL,
                    end_date DATE,
                    budget REAL NOT NULL,
                    FOREIGN KEY (department_id) REFERENCES departments(id)
                )
            """)
            
            # 插入示例数据
            sample_employees = [
                (1, '张三', '技术部', 8000.0, '2022-01-15', 28),
                (2, '李四', '销售部', 6500.0, '2021-03-20', 32),
                (3, '王五', '人事部', 7000.0, '2020-06-10', 35),
                (4, '赵六', '技术部', 9500.0, '2019-08-05', 30),
                (5, '钱七', '财务部', 7500.0, '2021-11-12', 29)
            ]
            
            cursor.executemany("""
                INSERT OR REPLACE INTO employees (id, name, department, salary, hire_date, age)
                VALUES (?, ?, ?, ?, ?, ?)
            """, sample_employees)
            
            sample_departments = [
                (1, '技术部', 4, 500000.0),
                (2, '销售部', 2, 300000.0),
                (3, '人事部', 3, 200000.0),
                (4, '财务部', 5, 250000.0)
            ]
            
            cursor.executemany("""
                INSERT OR REPLACE INTO departments (id, name, manager_id, budget)
                VALUES (?, ?, ?, ?)
            """, sample_departments)
            
            sample_projects = [
                (1, '电商平台开发', 1, '2023-01-01', '2023-12-31', 1000000.0),
                (2, '移动应用开发', 1, '2023-06-01', '2024-03-31', 800000.0),
                (3, '市场推广活动', 2, '2023-03-01', '2023-09-30', 500000.0),
                (4, '人才招聘计划', 3, '2023-01-01', '2023-12-31', 300000.0)
            ]
            
            cursor.executemany("""
                INSERT OR REPLACE INTO projects (id, name, department_id, start_date, end_date, budget)
                VALUES (?, ?, ?, ?, ?, ?)
            """, sample_projects)
            
            conn.commit()
            conn.close()
            logger.info("示例数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _load_schema_info(self):
        """加载数据库结构信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            for table in tables:
                table_name = table[0]
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                self.schema_info[table_name] = {
                    'columns': [
                        {
                            'name': col[1],
                            'type': col[2],
                            'not_null': bool(col[3]),
                            'primary_key': bool(col[5])
                        }
                        for col in columns
                    ]
                }
                
                # 获取外键信息
                cursor.execute(f"PRAGMA foreign_key_list({table_name})")
                foreign_keys = cursor.fetchall()
                
                self.schema_info[table_name]['foreign_keys'] = [
                    {
                        'column': fk[3],
                        'references_table': fk[2],
                        'references_column': fk[4]
                    }
                    for fk in foreign_keys
                ]
            
            conn.close()
            logger.info(f"数据库结构信息加载完成，共{len(self.schema_info)}个表")
            
        except Exception as e:
            logger.error(f"加载数据库结构失败: {e}")
            raise
    
    def get_schema_description(self) -> str:
        """获取数据库结构的文本描述"""
        description = "数据库结构信息：\n\n"
        
        for table_name, info in self.schema_info.items():
            description += f"表名: {table_name}\n"
            description += "字段:\n"
            
            for col in info['columns']:
                pk_marker = " (主键)" if col['primary_key'] else ""
                not_null_marker = " (非空)" if col['not_null'] else ""
                description += f"  - {col['name']}: {col['type']}{pk_marker}{not_null_marker}\n"
            
            if info.get('foreign_keys'):
                description += "外键关系:\n"
                for fk in info['foreign_keys']:
                    description += f"  - {fk['column']} -> {fk['references_table']}.{fk['references_column']}\n"
            
            description += "\n"
        
        return description
    
    def execute_sql(self, sql: str) -> Tuple[List[Dict], str]:
        """执行SQL查询并返回结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 使用pandas执行查询，便于处理结果
            df = pd.read_sql_query(sql, conn)
            
            # 转换为字典列表
            results = df.to_dict('records')
            
            conn.close()
            
            # 格式化结果为可读字符串
            if results:
                result_str = f"查询成功，返回{len(results)}条记录：\n"
                result_str += df.to_string(index=False)
            else:
                result_str = "查询成功，但没有找到匹配的记录。"
            
            logger.info(f"SQL执行成功: {sql}")
            return results, result_str
            
        except Exception as e:
            error_msg = f"SQL执行失败: {e}"
            logger.error(f"{error_msg} - SQL: {sql}")
            return [], error_msg


class VectorStoreManager:
    """向量数据库管理器 - 使用ChromaDB存储和检索SQL示例"""

    def __init__(self, persist_directory: str = "./chroma_db"):
        """初始化向量数据库管理器"""
        self.persist_directory = persist_directory
        self.embeddings = OpenAIEmbeddings()
        self.vectorstore = None
        self._init_vectorstore()

    def _init_vectorstore(self):
        """初始化向量数据库"""
        try:
            # 创建或加载ChromaDB
            self.vectorstore = Chroma(
                persist_directory=self.persist_directory,
                embedding_function=self.embeddings
            )
            logger.info("向量数据库初始化完成")
        except Exception as e:
            logger.error(f"向量数据库初始化失败: {e}")
            raise

    def add_sql_examples(self, examples: List[Dict[str, str]]):
        """添加SQL示例到向量数据库"""
        try:
            texts = []
            metadatas = []

            for example in examples:
                # 组合问题和SQL作为文本
                text = f"问题: {example['question']}\nSQL: {example['sql']}"
                texts.append(text)

                metadata = {
                    'question': example['question'],
                    'sql': example['sql'],
                    'explanation': example.get('explanation', ''),
                    'difficulty': example.get('difficulty', 'medium')
                }
                metadatas.append(metadata)

            # 添加到向量数据库
            self.vectorstore.add_texts(texts=texts, metadatas=metadatas)
            self.vectorstore.persist()

            logger.info(f"成功添加{len(examples)}个SQL示例到向量数据库")

        except Exception as e:
            logger.error(f"添加SQL示例失败: {e}")
            raise

    def search_similar_examples(self, question: str, k: int = 3) -> List[Dict]:
        """根据问题搜索相似的SQL示例"""
        try:
            # 使用相似性搜索
            docs = self.vectorstore.similarity_search(question, k=k)

            examples = []
            for doc in docs:
                examples.append({
                    'question': doc.metadata['question'],
                    'sql': doc.metadata['sql'],
                    'explanation': doc.metadata.get('explanation', ''),
                    'difficulty': doc.metadata.get('difficulty', 'medium')
                })

            logger.info(f"找到{len(examples)}个相似示例")
            return examples

        except Exception as e:
            logger.error(f"搜索相似示例失败: {e}")
            return []


class FewShotExampleManager:
    """Few-shot示例管理器 - 管理SQL问答对数据集"""

    def __init__(self):
        """初始化示例管理器"""
        self.examples = self._load_default_examples()

    def _load_default_examples(self) -> List[Dict[str, str]]:
        """加载默认的SQL示例"""
        return [
            {
                "question": "查询所有员工的姓名和薪资",
                "sql": "SELECT name, salary FROM employees;",
                "explanation": "简单的SELECT查询，获取员工表中的姓名和薪资字段",
                "difficulty": "easy"
            },
            {
                "question": "查询技术部门的所有员工",
                "sql": "SELECT * FROM employees WHERE department = '技术部';",
                "explanation": "使用WHERE条件过滤特定部门的员工",
                "difficulty": "easy"
            },
            {
                "question": "查询薪资大于8000的员工姓名和部门",
                "sql": "SELECT name, department FROM employees WHERE salary > 8000;",
                "explanation": "使用WHERE条件过滤薪资大于指定值的员工",
                "difficulty": "easy"
            },
            {
                "question": "按部门统计员工数量",
                "sql": "SELECT department, COUNT(*) as employee_count FROM employees GROUP BY department;",
                "explanation": "使用GROUP BY和COUNT函数统计每个部门的员工数量",
                "difficulty": "medium"
            },
            {
                "question": "查询每个部门的平均薪资",
                "sql": "SELECT department, AVG(salary) as avg_salary FROM employees GROUP BY department;",
                "explanation": "使用GROUP BY和AVG函数计算每个部门的平均薪资",
                "difficulty": "medium"
            },
            {
                "question": "查询薪资最高的员工信息",
                "sql": "SELECT * FROM employees WHERE salary = (SELECT MAX(salary) FROM employees);",
                "explanation": "使用子查询找到薪资最高的员工",
                "difficulty": "medium"
            },
            {
                "question": "查询每个部门薪资最高的员工",
                "sql": """SELECT e1.* FROM employees e1
                         WHERE e1.salary = (SELECT MAX(e2.salary) FROM employees e2 WHERE e2.department = e1.department);""",
                "explanation": "使用相关子查询找到每个部门薪资最高的员工",
                "difficulty": "hard"
            },
            {
                "question": "查询员工及其所在部门的预算信息",
                "sql": """SELECT e.name, e.department, d.budget
                         FROM employees e
                         JOIN departments d ON e.department = d.name;""",
                "explanation": "使用JOIN连接员工表和部门表获取部门预算信息",
                "difficulty": "medium"
            },
            {
                "question": "查询参与项目的员工信息",
                "sql": """SELECT DISTINCT e.name, e.department
                         FROM employees e
                         JOIN departments d ON e.department = d.name
                         JOIN projects p ON d.id = p.department_id;""",
                "explanation": "使用多表JOIN查询参与项目的员工信息",
                "difficulty": "hard"
            },
            {
                "question": "查询2022年入职的员工数量",
                "sql": "SELECT COUNT(*) as count FROM employees WHERE hire_date LIKE '2022%';",
                "explanation": "使用LIKE操作符和通配符查询特定年份入职的员工",
                "difficulty": "medium"
            },
            {
                "question": "查询年龄在30岁以上的员工平均薪资",
                "sql": "SELECT AVG(salary) as avg_salary FROM employees WHERE age > 30;",
                "explanation": "使用WHERE条件过滤年龄，然后计算平均薪资",
                "difficulty": "easy"
            },
            {
                "question": "查询各部门预算总和",
                "sql": "SELECT SUM(budget) as total_budget FROM departments;",
                "explanation": "使用SUM函数计算所有部门的预算总和",
                "difficulty": "easy"
            }
        ]

    def add_example(self, question: str, sql: str, explanation: str = "", difficulty: str = "medium"):
        """添加新的示例"""
        example = {
            "question": question,
            "sql": sql,
            "explanation": explanation,
            "difficulty": difficulty
        }
        self.examples.append(example)
        logger.info(f"添加新示例: {question}")

    def get_examples_by_difficulty(self, difficulty: str) -> List[Dict]:
        """根据难度获取示例"""
        return [ex for ex in self.examples if ex['difficulty'] == difficulty]

    def get_all_examples(self) -> List[Dict]:
        """获取所有示例"""
        return self.examples
