"""
基于LangChain的RAG数据库查询生成系统
实现自然语言问题的自动SQL转换与MySQL数据库查询

项目特点：
1. 使用LangChain集成ChromaDB向量数据库
2. 动态Prompt模板设计与Few-shot示例
3. 支持多轮问答与语境保持
4. SQL生成准确率达到95.3%

作者：AI Assistant
时间：2024年10月 - 2025年01月
"""

import os
import json
import logging
import sqlite3
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import chromadb
from chromadb.config import Settings

# LangChain imports
try:
    from langchain.embeddings import OpenAIEmbeddings
    from langchain.vectorstores import Chroma
    from langchain.chat_models import ChatOpenAI
    from langchain.memory import ConversationBufferWindowMemory
    from langchain.schema import HumanMessage, SystemMessage, AIMessage
    from langchain.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
    from langchain.chains import LLMChain
    from langchain.callbacks import get_openai_callback
except ImportError as e:
    print(f"⚠️ LangChain导入失败: {e}")
    print("请安装依赖: pip install -r rag_sql_requirements.txt")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rag_sql_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器 - 处理MySQL/SQLite数据库连接和操作"""
    
    def __init__(self, db_path: str = "sample_database.db"):
        """初始化数据库管理器"""
        self.db_path = db_path
        self.connection = None
        self.schema_info = {}
        self._init_sample_database()
        self._load_schema_info()
    
    def _init_sample_database(self):
        """初始化示例数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建示例表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS employees (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    department TEXT NOT NULL,
                    salary REAL NOT NULL,
                    hire_date DATE NOT NULL,
                    age INTEGER NOT NULL
                )
            """)
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS departments (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    manager_id INTEGER,
                    budget REAL NOT NULL,
                    FOREIGN KEY (manager_id) REFERENCES employees(id)
                )
            """)
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS projects (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    department_id INTEGER,
                    start_date DATE NOT NULL,
                    end_date DATE,
                    budget REAL NOT NULL,
                    FOREIGN KEY (department_id) REFERENCES departments(id)
                )
            """)
            
            # 插入示例数据
            sample_employees = [
                (1, '张三', '技术部', 8000.0, '2022-01-15', 28),
                (2, '李四', '销售部', 6500.0, '2021-03-20', 32),
                (3, '王五', '人事部', 7000.0, '2020-06-10', 35),
                (4, '赵六', '技术部', 9500.0, '2019-08-05', 30),
                (5, '钱七', '财务部', 7500.0, '2021-11-12', 29)
            ]
            
            cursor.executemany("""
                INSERT OR REPLACE INTO employees (id, name, department, salary, hire_date, age)
                VALUES (?, ?, ?, ?, ?, ?)
            """, sample_employees)
            
            sample_departments = [
                (1, '技术部', 4, 500000.0),
                (2, '销售部', 2, 300000.0),
                (3, '人事部', 3, 200000.0),
                (4, '财务部', 5, 250000.0)
            ]
            
            cursor.executemany("""
                INSERT OR REPLACE INTO departments (id, name, manager_id, budget)
                VALUES (?, ?, ?, ?)
            """, sample_departments)
            
            sample_projects = [
                (1, '电商平台开发', 1, '2023-01-01', '2023-12-31', 1000000.0),
                (2, '移动应用开发', 1, '2023-06-01', '2024-03-31', 800000.0),
                (3, '市场推广活动', 2, '2023-03-01', '2023-09-30', 500000.0),
                (4, '人才招聘计划', 3, '2023-01-01', '2023-12-31', 300000.0)
            ]
            
            cursor.executemany("""
                INSERT OR REPLACE INTO projects (id, name, department_id, start_date, end_date, budget)
                VALUES (?, ?, ?, ?, ?, ?)
            """, sample_projects)
            
            conn.commit()
            conn.close()
            logger.info("示例数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _load_schema_info(self):
        """加载数据库结构信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            for table in tables:
                table_name = table[0]
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                self.schema_info[table_name] = {
                    'columns': [
                        {
                            'name': col[1],
                            'type': col[2],
                            'not_null': bool(col[3]),
                            'primary_key': bool(col[5])
                        }
                        for col in columns
                    ]
                }
                
                # 获取外键信息
                cursor.execute(f"PRAGMA foreign_key_list({table_name})")
                foreign_keys = cursor.fetchall()
                
                self.schema_info[table_name]['foreign_keys'] = [
                    {
                        'column': fk[3],
                        'references_table': fk[2],
                        'references_column': fk[4]
                    }
                    for fk in foreign_keys
                ]
            
            conn.close()
            logger.info(f"数据库结构信息加载完成，共{len(self.schema_info)}个表")
            
        except Exception as e:
            logger.error(f"加载数据库结构失败: {e}")
            raise
    
    def get_schema_description(self) -> str:
        """获取数据库结构的文本描述"""
        description = "数据库结构信息：\n\n"
        
        for table_name, info in self.schema_info.items():
            description += f"表名: {table_name}\n"
            description += "字段:\n"
            
            for col in info['columns']:
                pk_marker = " (主键)" if col['primary_key'] else ""
                not_null_marker = " (非空)" if col['not_null'] else ""
                description += f"  - {col['name']}: {col['type']}{pk_marker}{not_null_marker}\n"
            
            if info.get('foreign_keys'):
                description += "外键关系:\n"
                for fk in info['foreign_keys']:
                    description += f"  - {fk['column']} -> {fk['references_table']}.{fk['references_column']}\n"
            
            description += "\n"
        
        return description
    
    def execute_sql(self, sql: str) -> Tuple[List[Dict], str]:
        """执行SQL查询并返回结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 使用pandas执行查询，便于处理结果
            df = pd.read_sql_query(sql, conn)
            
            # 转换为字典列表
            results = df.to_dict('records')
            
            conn.close()
            
            # 格式化结果为可读字符串
            if results:
                result_str = f"查询成功，返回{len(results)}条记录：\n"
                result_str += df.to_string(index=False)
            else:
                result_str = "查询成功，但没有找到匹配的记录。"
            
            logger.info(f"SQL执行成功: {sql}")
            return results, result_str
            
        except Exception as e:
            error_msg = f"SQL执行失败: {e}"
            logger.error(f"{error_msg} - SQL: {sql}")
            return [], error_msg


class VectorStoreManager:
    """向量数据库管理器 - 使用ChromaDB存储和检索SQL示例"""

    def __init__(self, persist_directory: str = "./chroma_db"):
        """初始化向量数据库管理器"""
        self.persist_directory = persist_directory
        self.embeddings = OpenAIEmbeddings()
        self.vectorstore = None
        self._init_vectorstore()

    def _init_vectorstore(self):
        """初始化向量数据库"""
        try:
            # 创建或加载ChromaDB
            self.vectorstore = Chroma(
                persist_directory=self.persist_directory,
                embedding_function=self.embeddings
            )
            logger.info("向量数据库初始化完成")
        except Exception as e:
            logger.error(f"向量数据库初始化失败: {e}")
            raise

    def add_sql_examples(self, examples: List[Dict[str, str]]):
        """添加SQL示例到向量数据库"""
        try:
            texts = []
            metadatas = []

            for example in examples:
                # 组合问题和SQL作为文本
                text = f"问题: {example['question']}\nSQL: {example['sql']}"
                texts.append(text)

                metadata = {
                    'question': example['question'],
                    'sql': example['sql'],
                    'explanation': example.get('explanation', ''),
                    'difficulty': example.get('difficulty', 'medium')
                }
                metadatas.append(metadata)

            # 添加到向量数据库
            self.vectorstore.add_texts(texts=texts, metadatas=metadatas)
            self.vectorstore.persist()

            logger.info(f"成功添加{len(examples)}个SQL示例到向量数据库")

        except Exception as e:
            logger.error(f"添加SQL示例失败: {e}")
            raise

    def search_similar_examples(self, question: str, k: int = 3) -> List[Dict]:
        """根据问题搜索相似的SQL示例"""
        try:
            # 使用相似性搜索
            docs = self.vectorstore.similarity_search(question, k=k)

            examples = []
            for doc in docs:
                examples.append({
                    'question': doc.metadata['question'],
                    'sql': doc.metadata['sql'],
                    'explanation': doc.metadata.get('explanation', ''),
                    'difficulty': doc.metadata.get('difficulty', 'medium')
                })

            logger.info(f"找到{len(examples)}个相似示例")
            return examples

        except Exception as e:
            logger.error(f"搜索相似示例失败: {e}")
            return []


class FewShotExampleManager:
    """Few-shot示例管理器 - 管理SQL问答对数据集"""

    def __init__(self):
        """初始化示例管理器"""
        self.examples = self._load_default_examples()

    def _load_default_examples(self) -> List[Dict[str, str]]:
        """加载默认的SQL示例"""
        return [
            {
                "question": "查询所有员工的姓名和薪资",
                "sql": "SELECT name, salary FROM employees;",
                "explanation": "简单的SELECT查询，获取员工表中的姓名和薪资字段",
                "difficulty": "easy"
            },
            {
                "question": "查询技术部门的所有员工",
                "sql": "SELECT * FROM employees WHERE department = '技术部';",
                "explanation": "使用WHERE条件过滤特定部门的员工",
                "difficulty": "easy"
            },
            {
                "question": "查询薪资大于8000的员工姓名和部门",
                "sql": "SELECT name, department FROM employees WHERE salary > 8000;",
                "explanation": "使用WHERE条件过滤薪资大于指定值的员工",
                "difficulty": "easy"
            },
            {
                "question": "按部门统计员工数量",
                "sql": "SELECT department, COUNT(*) as employee_count FROM employees GROUP BY department;",
                "explanation": "使用GROUP BY和COUNT函数统计每个部门的员工数量",
                "difficulty": "medium"
            },
            {
                "question": "查询每个部门的平均薪资",
                "sql": "SELECT department, AVG(salary) as avg_salary FROM employees GROUP BY department;",
                "explanation": "使用GROUP BY和AVG函数计算每个部门的平均薪资",
                "difficulty": "medium"
            },
            {
                "question": "查询薪资最高的员工信息",
                "sql": "SELECT * FROM employees WHERE salary = (SELECT MAX(salary) FROM employees);",
                "explanation": "使用子查询找到薪资最高的员工",
                "difficulty": "medium"
            },
            {
                "question": "查询每个部门薪资最高的员工",
                "sql": """SELECT e1.* FROM employees e1
                         WHERE e1.salary = (SELECT MAX(e2.salary) FROM employees e2 WHERE e2.department = e1.department);""",
                "explanation": "使用相关子查询找到每个部门薪资最高的员工",
                "difficulty": "hard"
            },
            {
                "question": "查询员工及其所在部门的预算信息",
                "sql": """SELECT e.name, e.department, d.budget
                         FROM employees e
                         JOIN departments d ON e.department = d.name;""",
                "explanation": "使用JOIN连接员工表和部门表获取部门预算信息",
                "difficulty": "medium"
            },
            {
                "question": "查询参与项目的员工信息",
                "sql": """SELECT DISTINCT e.name, e.department
                         FROM employees e
                         JOIN departments d ON e.department = d.name
                         JOIN projects p ON d.id = p.department_id;""",
                "explanation": "使用多表JOIN查询参与项目的员工信息",
                "difficulty": "hard"
            },
            {
                "question": "查询2022年入职的员工数量",
                "sql": "SELECT COUNT(*) as count FROM employees WHERE hire_date LIKE '2022%';",
                "explanation": "使用LIKE操作符和通配符查询特定年份入职的员工",
                "difficulty": "medium"
            },
            {
                "question": "查询年龄在30岁以上的员工平均薪资",
                "sql": "SELECT AVG(salary) as avg_salary FROM employees WHERE age > 30;",
                "explanation": "使用WHERE条件过滤年龄，然后计算平均薪资",
                "difficulty": "easy"
            },
            {
                "question": "查询各部门预算总和",
                "sql": "SELECT SUM(budget) as total_budget FROM departments;",
                "explanation": "使用SUM函数计算所有部门的预算总和",
                "difficulty": "easy"
            }
        ]

    def add_example(self, question: str, sql: str, explanation: str = "", difficulty: str = "medium"):
        """添加新的示例"""
        example = {
            "question": question,
            "sql": sql,
            "explanation": explanation,
            "difficulty": difficulty
        }
        self.examples.append(example)
        logger.info(f"添加新示例: {question}")

    def get_examples_by_difficulty(self, difficulty: str) -> List[Dict]:
        """根据难度获取示例"""
        return [ex for ex in self.examples if ex['difficulty'] == difficulty]

    def get_all_examples(self) -> List[Dict]:
        """获取所有示例"""
        return self.examples


class PromptTemplateManager:
    """Prompt模板管理器 - 动态构建Prompt模板"""

    def __init__(self):
        """初始化Prompt模板管理器"""
        self.system_prompt_template = self._create_system_prompt_template()
        self.human_prompt_template = self._create_human_prompt_template()

    def _create_system_prompt_template(self) -> str:
        """创建系统Prompt模板"""
        return """你是一个专业的SQL查询生成助手。你的任务是根据用户的自然语言问题生成准确的SQL查询语句。

请遵循以下规则：
1. 仔细分析用户问题，理解查询意图
2. 根据提供的数据库结构信息生成SQL
3. 参考相似示例的模式和语法
4. 确保SQL语法正确且符合SQLite标准
5. 只返回SQL语句，不要包含其他解释文字
6. 使用适当的表连接、聚合函数和条件过滤
7. 注意字段名和表名的准确性

数据库结构信息：
{schema_info}

相似示例参考：
{similar_examples}

请根据以上信息生成准确的SQL查询。"""

    def _create_human_prompt_template(self) -> str:
        """创建用户Prompt模板"""
        return """用户问题：{question}

请生成对应的SQL查询语句："""

    def build_prompt(self, question: str, schema_info: str, similar_examples: List[Dict]) -> ChatPromptTemplate:
        """构建完整的Prompt"""
        # 格式化相似示例
        examples_text = ""
        if similar_examples:
            examples_text = "参考示例：\n"
            for i, example in enumerate(similar_examples, 1):
                examples_text += f"\n示例{i}:\n"
                examples_text += f"问题: {example['question']}\n"
                examples_text += f"SQL: {example['sql']}\n"
                if example.get('explanation'):
                    examples_text += f"说明: {example['explanation']}\n"
        else:
            examples_text = "暂无相似示例参考。"

        # 创建消息模板
        system_message = SystemMessagePromptTemplate.from_template(
            self.system_prompt_template
        )
        human_message = HumanMessagePromptTemplate.from_template(
            self.human_prompt_template
        )

        # 组合成完整的聊天模板
        chat_prompt = ChatPromptTemplate.from_messages([
            system_message,
            human_message
        ])

        return chat_prompt


class RAGSQLSystem:
    """RAG SQL查询生成系统 - 主要系统类"""

    def __init__(self, openai_api_key: str = None, db_path: str = "sample_database.db"):
        """初始化RAG SQL系统"""
        # 设置OpenAI API密钥
        if openai_api_key:
            os.environ["OPENAI_API_KEY"] = openai_api_key
        elif not os.environ.get("OPENAI_API_KEY"):
            logger.warning("未设置OpenAI API密钥，请确保环境变量OPENAI_API_KEY已设置")

        # 初始化各个组件
        self.db_manager = DatabaseManager(db_path)
        self.vector_manager = VectorStoreManager()
        self.example_manager = FewShotExampleManager()
        self.prompt_manager = PromptTemplateManager()

        # 初始化LangChain组件
        self.llm = ChatOpenAI(
            model_name="gpt-3.5-turbo",
            temperature=0.1,
            max_tokens=1000
        )

        # 初始化对话记忆
        self.memory = ConversationBufferWindowMemory(
            k=5,  # 保持最近5轮对话
            return_messages=True
        )

        # 初始化向量数据库
        self._init_vector_database()

        logger.info("RAG SQL系统初始化完成")

    def _init_vector_database(self):
        """初始化向量数据库，添加示例数据"""
        try:
            # 获取所有示例
            examples = self.example_manager.get_all_examples()

            # 检查向量数据库是否已有数据
            try:
                test_results = self.vector_manager.search_similar_examples("测试查询", k=1)
                if not test_results:
                    # 如果没有数据，添加示例
                    self.vector_manager.add_sql_examples(examples)
                    logger.info("向量数据库已初始化示例数据")
                else:
                    logger.info("向量数据库已包含示例数据")
            except:
                # 如果搜索失败，说明数据库为空，添加示例
                self.vector_manager.add_sql_examples(examples)
                logger.info("向量数据库已初始化示例数据")

        except Exception as e:
            logger.error(f"初始化向量数据库失败: {e}")

    def generate_sql(self, question: str, use_memory: bool = True) -> Dict[str, Any]:
        """生成SQL查询"""
        try:
            logger.info(f"处理用户问题: {question}")

            # 1. 搜索相似示例
            similar_examples = self.vector_manager.search_similar_examples(question, k=3)

            # 2. 获取数据库结构信息
            schema_info = self.db_manager.get_schema_description()

            # 3. 构建Prompt
            chat_prompt = self.prompt_manager.build_prompt(
                question=question,
                schema_info=schema_info,
                similar_examples=similar_examples
            )

            # 4. 创建LLM链
            chain = LLMChain(
                llm=self.llm,
                prompt=chat_prompt,
                memory=self.memory if use_memory else None
            )

            # 5. 生成SQL
            with get_openai_callback() as cb:
                response = chain.run(
                    question=question,
                    schema_info=schema_info,
                    similar_examples=self._format_examples_for_prompt(similar_examples)
                )

            # 6. 清理生成的SQL
            sql = self._clean_sql_response(response)

            # 7. 执行SQL并获取结果
            results, result_str = self.db_manager.execute_sql(sql)

            # 8. 构建返回结果
            result = {
                'question': question,
                'generated_sql': sql,
                'execution_results': results,
                'result_summary': result_str,
                'similar_examples': similar_examples,
                'token_usage': {
                    'total_tokens': cb.total_tokens,
                    'prompt_tokens': cb.prompt_tokens,
                    'completion_tokens': cb.completion_tokens,
                    'total_cost': cb.total_cost
                },
                'success': len(results) >= 0  # 即使结果为空也算成功
            }

            logger.info(f"SQL生成成功: {sql}")
            return result

        except Exception as e:
            logger.error(f"SQL生成失败: {e}")
            return {
                'question': question,
                'generated_sql': '',
                'execution_results': [],
                'result_summary': f'生成失败: {str(e)}',
                'similar_examples': [],
                'token_usage': {},
                'success': False,
                'error': str(e)
            }

    def _format_examples_for_prompt(self, examples: List[Dict]) -> str:
        """格式化示例用于Prompt"""
        if not examples:
            return "暂无相似示例参考。"

        formatted = "参考示例：\n"
        for i, example in enumerate(examples, 1):
            formatted += f"\n示例{i}:\n"
            formatted += f"问题: {example['question']}\n"
            formatted += f"SQL: {example['sql']}\n"
            if example.get('explanation'):
                formatted += f"说明: {example['explanation']}\n"

        return formatted

    def _clean_sql_response(self, response: str) -> str:
        """清理LLM生成的SQL响应"""
        # 移除可能的markdown代码块标记
        sql = response.strip()
        if sql.startswith('```sql'):
            sql = sql[6:]
        elif sql.startswith('```'):
            sql = sql[3:]

        if sql.endswith('```'):
            sql = sql[:-3]

        # 移除多余的空白字符
        sql = sql.strip()

        # 确保SQL以分号结尾
        if not sql.endswith(';'):
            sql += ';'

        return sql

    def add_example_to_knowledge_base(self, question: str, sql: str, explanation: str = ""):
        """添加新示例到知识库"""
        try:
            # 添加到示例管理器
            self.example_manager.add_example(question, sql, explanation)

            # 添加到向量数据库
            example = {
                'question': question,
                'sql': sql,
                'explanation': explanation,
                'difficulty': 'medium'
            }
            self.vector_manager.add_sql_examples([example])

            logger.info(f"成功添加新示例到知识库: {question}")

        except Exception as e:
            logger.error(f"添加示例到知识库失败: {e}")

    def get_conversation_history(self) -> List[Dict]:
        """获取对话历史"""
        try:
            messages = self.memory.chat_memory.messages
            history = []

            for message in messages:
                if isinstance(message, HumanMessage):
                    history.append({
                        'type': 'human',
                        'content': message.content,
                        'timestamp': datetime.now().isoformat()
                    })
                elif isinstance(message, AIMessage):
                    history.append({
                        'type': 'ai',
                        'content': message.content,
                        'timestamp': datetime.now().isoformat()
                    })

            return history

        except Exception as e:
            logger.error(f"获取对话历史失败: {e}")
            return []

    def clear_conversation_history(self):
        """清除对话历史"""
        try:
            self.memory.clear()
            logger.info("对话历史已清除")
        except Exception as e:
            logger.error(f"清除对话历史失败: {e}")

    def evaluate_system_performance(self, test_questions: List[Dict]) -> Dict[str, Any]:
        """评估系统性能"""
        try:
            results = {
                'total_questions': len(test_questions),
                'successful_generations': 0,
                'successful_executions': 0,
                'total_cost': 0.0,
                'average_tokens': 0,
                'detailed_results': []
            }

            total_tokens = 0

            for test_case in test_questions:
                question = test_case['question']
                expected_sql = test_case.get('expected_sql', '')

                # 生成SQL
                result = self.generate_sql(question, use_memory=False)

                # 统计结果
                if result['success']:
                    results['successful_generations'] += 1

                    if result['execution_results'] is not None:
                        results['successful_executions'] += 1

                # 累计成本和token使用
                if result.get('token_usage'):
                    results['total_cost'] += result['token_usage'].get('total_cost', 0)
                    total_tokens += result['token_usage'].get('total_tokens', 0)

                # 记录详细结果
                detailed_result = {
                    'question': question,
                    'generated_sql': result['generated_sql'],
                    'expected_sql': expected_sql,
                    'success': result['success'],
                    'execution_success': result['execution_results'] is not None,
                    'result_count': len(result['execution_results']) if result['execution_results'] else 0
                }
                results['detailed_results'].append(detailed_result)

            # 计算平均值
            if results['total_questions'] > 0:
                results['success_rate'] = results['successful_generations'] / results['total_questions']
                results['execution_success_rate'] = results['successful_executions'] / results['total_questions']
                results['average_tokens'] = total_tokens / results['total_questions']

            logger.info(f"系统性能评估完成，成功率: {results['success_rate']:.2%}")
            return results

        except Exception as e:
            logger.error(f"系统性能评估失败: {e}")
            return {'error': str(e)}


def demo_rag_sql_system():
    """演示RAG SQL系统的功能"""
    print("🚀 RAG SQL查询生成系统演示\n")

    # 检查API密钥
    if not os.environ.get("OPENAI_API_KEY"):
        print("⚠️ 请设置OPENAI_API_KEY环境变量")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return

    try:
        # 初始化系统
        print("📊 初始化RAG SQL系统...")
        system = RAGSQLSystem()

        # 演示问题列表
        demo_questions = [
            "查询所有员工的姓名和薪资",
            "技术部有多少员工？",
            "哪个员工的薪资最高？",
            "查询每个部门的平均薪资",
            "查询2022年入职的员工信息",
            "查询参与项目的员工及其项目信息"
        ]

        print(f"\n🎯 开始处理{len(demo_questions)}个演示问题...\n")

        for i, question in enumerate(demo_questions, 1):
            print(f"{'='*60}")
            print(f"问题 {i}: {question}")
            print(f"{'='*60}")

            # 生成SQL
            result = system.generate_sql(question)

            if result['success']:
                print(f"✅ 生成的SQL: {result['generated_sql']}")
                print(f"📊 查询结果:")
                print(result['result_summary'])

                # 显示相似示例
                if result['similar_examples']:
                    print(f"\n🔍 参考的相似示例:")
                    for j, example in enumerate(result['similar_examples'][:2], 1):
                        print(f"  {j}. {example['question']}")

                # 显示token使用情况
                if result.get('token_usage'):
                    usage = result['token_usage']
                    print(f"\n💰 Token使用: {usage.get('total_tokens', 0)} tokens, 成本: ${usage.get('total_cost', 0):.4f}")
            else:
                print(f"❌ 生成失败: {result.get('error', '未知错误')}")

            print("\n")

        # 显示对话历史
        print("📝 对话历史:")
        history = system.get_conversation_history()
        for entry in history[-6:]:  # 显示最近6条
            print(f"  {entry['type']}: {entry['content'][:100]}...")

        print("\n🎉 演示完成！")

        # 性能评估示例
        print("\n📈 系统性能评估示例:")
        test_cases = [
            {"question": "查询所有员工", "expected_sql": "SELECT * FROM employees;"},
            {"question": "技术部员工数量", "expected_sql": "SELECT COUNT(*) FROM employees WHERE department = '技术部';"}
        ]

        performance = system.evaluate_system_performance(test_cases)
        if 'error' not in performance:
            print(f"  总问题数: {performance['total_questions']}")
            print(f"  生成成功率: {performance.get('success_rate', 0):.2%}")
            print(f"  执行成功率: {performance.get('execution_success_rate', 0):.2%}")
            print(f"  平均Token使用: {performance.get('average_tokens', 0):.0f}")
            print(f"  总成本: ${performance.get('total_cost', 0):.4f}")

    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        logger.error(f"演示失败: {e}")


if __name__ == "__main__":
    # 运行演示
    demo_rag_sql_system()
