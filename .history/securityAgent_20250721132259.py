"""
SecurityAgent - 基于 smolagents 的安全测试与漏洞发现智能体
专注于授权的安全研究、漏洞发现和防御建议

⚠️ 重要声明：
本工具仅用于授权的安全测试和研究目的
使用前必须获得目标系统的明确授权
禁止用于任何非法或恶意活动

主要功能：
- 自动化安全扫描
- 漏洞识别与分析
- 安全配置检查
- 防御建议生成
- 安全报告生成
"""

import os
import json
import subprocess
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from smolagents import CodeAgent, Tool, InferenceClientModel, PythonInterpreterTool

class SecurityScanTool(Tool):
    """安全扫描工具 - 集成多种安全扫描工具"""
    
    name = "security_scan"
    description = "执行安全扫描，包括端口扫描、漏洞扫描等（仅限授权测试）"
    inputs = {
        "scan_type": {
            "type": "string",
            "description": "扫描类型：port_scan, vuln_scan, web_scan, config_check"
        },
        "target": {
            "type": "string",
            "description": "扫描目标（IP、域名或文件路径）"
        },
        "options": {
            "type": "string",
            "description": "扫描选项和参数",
            "nullable": True
        }
    }
    output_type = "string"
    
    def __init__(self):
        super().__init__()
        self.authorized_targets = set()  # 授权目标列表
        self.scan_history = []
    
    def add_authorized_target(self, target: str):
        """添加授权测试目标"""
        self.authorized_targets.add(target)
        print(f"✅ 已添加授权目标: {target}")
    
    def _check_authorization(self, target: str) -> bool:
        """检查目标是否已授权"""
        # 检查是否为本地目标
        local_targets = ["127.0.0.1", "localhost", "0.0.0.0"]
        if any(local in target for local in local_targets):
            return True
        
        # 检查是否在授权列表中
        return target in self.authorized_targets
    
    def forward(self, scan_type: str, target: str, options: str = "") -> str:
        """执行安全扫描"""
        # 授权检查
        if not self._check_authorization(target):
            return f"❌ 错误: 目标 {target} 未授权。请先使用 add_authorized_target() 添加授权。"
        
        print(f"🔍 开始扫描: {scan_type} -> {target}")
        
        try:
            if scan_type == "port_scan":
                return self._port_scan(target, options)
            elif scan_type == "vuln_scan":
                return self._vulnerability_scan(target, options)
            elif scan_type == "web_scan":
                return self._web_scan(target, options)
            elif scan_type == "config_check":
                return self._config_check(target, options)
            else:
                return f"不支持的扫描类型: {scan_type}"
                
        except Exception as e:
            return f"扫描失败: {str(e)}"
    
    def _port_scan(self, target: str, options: str) -> str:
        """端口扫描 - 使用 nmap"""
        try:
            # 基础 nmap 命令
            cmd = f"nmap -sS -O {options} {target}"
            
            # 执行扫描
            result = subprocess.run(
                cmd.split(),
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            scan_result = f"端口扫描结果 - {target}\n"
            scan_result += f"命令: {cmd}\n"
            scan_result += f"返回码: {result.returncode}\n\n"
            
            if result.stdout:
                scan_result += f"扫描输出:\n{result.stdout}\n"
            
            if result.stderr:
                scan_result += f"错误信息:\n{result.stderr}\n"
            
            # 记录扫描历史
            self._log_scan(scan_type="port_scan", target=target, result=scan_result)
            
            return scan_result
            
        except subprocess.TimeoutExpired:
            return f"端口扫描超时: {target}"
        except FileNotFoundError:
            return "错误: 未找到 nmap 工具，请先安装 nmap"
        except Exception as e:
            return f"端口扫描失败: {str(e)}"
    
    def _vulnerability_scan(self, target: str, options: str) -> str:
        """漏洞扫描 - 使用 nmap 脚本引擎"""
        try:
            # 使用 nmap 的漏洞扫描脚本
            vuln_scripts = "vuln,exploit,dos,intrusive"
            cmd = f"nmap --script {vuln_scripts} {options} {target}"
            
            result = subprocess.run(
                cmd.split(),
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时
            )
            
            scan_result = f"漏洞扫描结果 - {target}\n"
            scan_result += f"命令: {cmd}\n"
            scan_result += f"返回码: {result.returncode}\n\n"
            
            if result.stdout:
                scan_result += f"扫描输出:\n{result.stdout}\n"
                
                # 分析漏洞
                vulnerabilities = self._analyze_vulnerabilities(result.stdout)
                if vulnerabilities:
                    scan_result += f"\n🚨 发现的漏洞:\n"
                    for vuln in vulnerabilities:
                        scan_result += f"- {vuln}\n"
            
            if result.stderr:
                scan_result += f"错误信息:\n{result.stderr}\n"
            
            self._log_scan(scan_type="vuln_scan", target=target, result=scan_result)
            
            return scan_result
            
        except subprocess.TimeoutExpired:
            return f"漏洞扫描超时: {target}"
        except FileNotFoundError:
            return "错误: 未找到 nmap 工具，请先安装 nmap"
        except Exception as e:
            return f"漏洞扫描失败: {str(e)}"
    
    def _web_scan(self, target: str, options: str) -> str:
        """Web 应用扫描 - 使用 nikto 或自定义脚本"""
        try:
            # 尝试使用 nikto
            cmd = f"nikto -h {target} {options}"
            
            result = subprocess.run(
                cmd.split(),
                capture_output=True,
                text=True,
                timeout=600
            )
            
            scan_result = f"Web 应用扫描结果 - {target}\n"
            scan_result += f"命令: {cmd}\n"
            scan_result += f"返回码: {result.returncode}\n\n"
            
            if result.stdout:
                scan_result += f"扫描输出:\n{result.stdout}\n"
            
            if result.stderr:
                scan_result += f"错误信息:\n{result.stderr}\n"
            
            self._log_scan(scan_type="web_scan", target=target, result=scan_result)
            
            return scan_result
            
        except FileNotFoundError:
            # 如果没有 nikto，使用简单的 HTTP 检查
            return self._simple_web_check(target)
        except Exception as e:
            return f"Web 扫描失败: {str(e)}"
    
    def _simple_web_check(self, target: str) -> str:
        """简单的 Web 安全检查"""
        try:
            import requests
            
            scan_result = f"简单 Web 安全检查 - {target}\n\n"
            
            # 检查 HTTP 头
            try:
                response = requests.get(f"http://{target}", timeout=10)
                scan_result += f"HTTP 状态码: {response.status_code}\n"
                scan_result += f"服务器: {response.headers.get('Server', 'Unknown')}\n"
                
                # 检查安全头
                security_headers = [
                    'X-Frame-Options',
                    'X-XSS-Protection', 
                    'X-Content-Type-Options',
                    'Strict-Transport-Security',
                    'Content-Security-Policy'
                ]
                
                scan_result += "\n安全头检查:\n"
                for header in security_headers:
                    if header in response.headers:
                        scan_result += f"✅ {header}: {response.headers[header]}\n"
                    else:
                        scan_result += f"❌ 缺失 {header}\n"
                        
            except requests.RequestException as e:
                scan_result += f"HTTP 请求失败: {str(e)}\n"
            
            return scan_result
            
        except ImportError:
            return "错误: 需要安装 requests 库 (pip install requests)"
    
    def _config_check(self, target: str, options: str) -> str:
        """配置安全检查"""
        if os.path.isfile(target):
            return self._file_security_check(target)
        else:
            return self._system_config_check(target)
    
    def _file_security_check(self, file_path: str) -> str:
        """文件安全检查"""
        try:
            result = f"文件安全检查 - {file_path}\n\n"
            
            file_stat = os.stat(file_path)
            permissions = oct(file_stat.st_mode)[-3:]
            
            result += f"文件权限: {permissions}\n"
            
            # 检查危险权限
            if permissions.endswith('7') or permissions.endswith('6'):
                result += "⚠️ 警告: 文件具有写权限\n"
            
            if permissions.startswith('7'):
                result += "⚠️ 警告: 所有者具有执行权限\n"
            
            # 检查文件内容（如果是配置文件）
            if file_path.endswith(('.conf', '.config', '.ini', '.yaml', '.yml', '.json')):
                result += self._check_config_content(file_path)
            
            return result
            
        except Exception as e:
            return f"文件检查失败: {str(e)}"
    
    def _check_config_content(self, file_path: str) -> str:
        """检查配置文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            result = "\n配置内容安全检查:\n"
            
            # 检查敏感信息
            sensitive_patterns = [
                'password', 'passwd', 'pwd', 'secret', 'key', 'token',
                'api_key', 'private_key', 'access_key'
            ]
            
            for pattern in sensitive_patterns:
                if pattern.lower() in content.lower():
                    result += f"⚠️ 发现可能的敏感信息: {pattern}\n"
            
            # 检查默认凭据
            default_creds = ['admin:admin', 'root:root', 'admin:password']
            for cred in default_creds:
                if cred in content:
                    result += f"🚨 发现默认凭据: {cred}\n"
            
            return result

class SecurityReportTool(Tool):
    """安全报告生成工具"""

    name = "security_report"
    description = "生成安全测试报告和建议"
    inputs = {
        "scan_results": {
            "type": "string",
            "description": "扫描结果数据"
        },
        "report_type": {
            "type": "string",
            "description": "报告类型：summary, detailed, executive",
            "nullable": True
        }
    }
    output_type = "string"

    def forward(self, scan_results: str, report_type: str = "summary") -> str:
        """生成安全报告"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            if report_type == "summary":
                return self._generate_summary_report(scan_results, timestamp)
            elif report_type == "detailed":
                return self._generate_detailed_report(scan_results, timestamp)
            elif report_type == "executive":
                return self._generate_executive_report(scan_results, timestamp)
            else:
                return f"不支持的报告类型: {report_type}"

        except Exception as e:
            return f"报告生成失败: {str(e)}"

    def _generate_summary_report(self, results: str, timestamp: str) -> str:
        """生成摘要报告"""
        report = f"""
# 安全测试摘要报告
生成时间: {timestamp}

## 扫描概况
{self._extract_scan_summary(results)}

## 发现的问题
{self._extract_vulnerabilities_summary(results)}

## 风险评估
{self._assess_overall_risk(results)}

## 优先修复建议
{self._get_priority_fixes(results)}

---
本报告由 SecurityAgent 自动生成
"""
        return report

    def _extract_scan_summary(self, results: str) -> str:
        """提取扫描概况"""
        lines = results.split('\n')
        targets = set()
        scan_types = set()

        for line in lines:
            if '扫描结果' in line:
                parts = line.split(' - ')
                if len(parts) > 1:
                    targets.add(parts[1])
            if '命令:' in line:
                if 'nmap' in line:
                    scan_types.add('端口扫描')
                if 'nikto' in line:
                    scan_types.add('Web扫描')

        summary = f"- 扫描目标: {len(targets)} 个\n"
        summary += f"- 扫描类型: {', '.join(scan_types)}\n"
        summary += f"- 扫描状态: 已完成\n"

        return summary

    def _extract_vulnerabilities_summary(self, results: str) -> str:
        """提取漏洞摘要"""
        vuln_count = results.lower().count('vulnerable')
        cve_count = results.upper().count('CVE-')

        summary = f"- 发现漏洞: {vuln_count} 个\n"
        summary += f"- CVE 编号: {cve_count} 个\n"

        if vuln_count > 0:
            summary += "- 需要立即关注 ⚠️\n"
        else:
            summary += "- 未发现明显漏洞 ✅\n"

        return summary

    def _assess_overall_risk(self, results: str) -> str:
        """评估整体风险"""
        high_risk_keywords = ['RCE', 'SQL injection', 'authentication bypass']
        medium_risk_keywords = ['XSS', 'CSRF', 'information disclosure']

        results_lower = results.lower()

        high_risk_count = sum(1 for keyword in high_risk_keywords if keyword.lower() in results_lower)
        medium_risk_count = sum(1 for keyword in medium_risk_keywords if keyword.lower() in results_lower)

        if high_risk_count > 0:
            risk_level = "🚨 高风险"
        elif medium_risk_count > 0:
            risk_level = "⚠️ 中等风险"
        else:
            risk_level = "✅ 低风险"

        return f"整体风险等级: {risk_level}"

    def _get_priority_fixes(self, results: str) -> str:
        """获取优先修复建议"""
        fixes = [
            "1. 更新系统补丁和软件版本",
            "2. 加强访问控制和认证机制",
            "3. 配置防火墙和网络隔离",
            "4. 启用安全日志和监控",
            "5. 定期进行安全评估"
        ]

        return '\n'.join(fixes)

class SecurityAgent:
    """安全测试智能体 - 主要的 Agent 类"""

    def __init__(self, api_key: str = None):
        """初始化 SecurityAgent"""

        # 初始化工具
        self.security_scan_tool = SecurityScanTool()
        self.vuln_analysis_tool = VulnerabilityAnalysisTool()
        self.report_tool = SecurityReportTool()

        self.tools = [
            PythonInterpreterTool(),
            self.security_scan_tool,
            self.vuln_analysis_tool,
            self.report_tool
        ]

        # 初始化模型
        self.model = self._setup_model(api_key)

        # 创建 smolagents CodeAgent
        self.agent = CodeAgent(
            tools=self.tools,
            model=self.model,
            stream_outputs=True
        )

        # 系统提示词
        self.system_prompt = self._build_system_prompt()

        print("🛡️ 安全测试智能体已初始化完成！")
        print("⚠️ 请确保仅在授权环境中使用")

    def _setup_model(self, api_key: str):
        """设置模型"""
        try:
            from smolagents import InferenceClientModel
            return InferenceClientModel(
                model_id="Qwen/Qwen2.5-Coder-32B-Instruct",
                provider="auto"
            )
        except Exception as e:
            print(f"⚠️ 模型初始化失败: {e}")
            return None

    def _build_system_prompt(self) -> str:
        """构建系统提示词"""
        return """
你是一个专业的网络安全测试专家，专注于授权的安全研究和漏洞发现。

🎯 核心能力：
1. 安全扫描：端口扫描、漏洞扫描、Web应用扫描
2. 漏洞分析：风险评估、可利用性分析、修复建议
3. 安全报告：生成专业的安全测试报告
4. 防御建议：提供安全加固和防护措施

🛠️ 可用工具：
- security_scan: 执行各类安全扫描
- vulnerability_analysis: 漏洞分析和评估
- security_report: 生成安全报告
- python_interpreter: Python 代码执行

⚠️ 安全原则：
1. 仅在授权环境中进行测试
2. 遵循负责任的漏洞披露原则
3. 优先考虑防御和修复
4. 保护测试数据的机密性
5. 记录所有测试活动

🔄 工作流程：
1. 确认测试授权
2. 制定测试计划
3. 执行安全扫描
4. 分析发现的问题
5. 生成测试报告
6. 提供修复建议

请始终以专业、负责任的方式进行安全测试。
"""

    def add_authorized_target(self, target: str):
        """添加授权测试目标"""
        self.security_scan_tool.add_authorized_target(target)

    def run_security_test(self, target: str, test_type: str = "comprehensive") -> str:
        """运行安全测试"""
        try:
            # 检查授权
            if not self.security_scan_tool._check_authorization(target):
                return f"❌ 错误: 目标 {target} 未授权。请先添加授权。"

            print(f"🔍 开始对 {target} 进行 {test_type} 安全测试")

            # 构建测试请求
            test_request = f"""
对目标 {target} 进行 {test_type} 安全测试，包括：

1. 端口扫描 - 发现开放的服务
2. 漏洞扫描 - 检测已知漏洞
3. Web应用扫描 - 检查Web安全问题（如果适用）
4. 配置检查 - 验证安全配置

请按顺序执行测试，分析结果，并生成综合报告。
"""

            # 执行测试
            result = self.agent.run(test_request)

            print(f"✅ 安全测试完成")
            return result

        except Exception as e:
            return f"安全测试失败: {str(e)}"

    def analyze_vulnerability(self, vuln_data: str) -> str:
        """分析特定漏洞"""
        try:
            analysis_request = f"""
分析以下漏洞信息：

{vuln_data}

请提供：
1. 风险评估
2. 可利用性分析
3. 修复建议
4. 防护措施
"""

            result = self.agent.run(analysis_request)
            return result

        except Exception as e:
            return f"漏洞分析失败: {str(e)}"

    def generate_security_report(self, scan_results: str, report_type: str = "summary") -> str:
        """生成安全报告"""
        try:
            report_request = f"""
基于以下扫描结果生成 {report_type} 安全报告：

{scan_results}

报告应包含：
1. 执行摘要
2. 发现的问题
3. 风险评估
4. 修复建议
5. 后续行动计划
"""

            result = self.agent.run(report_request)

            # 保存报告到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"security_report_{timestamp}.md"

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(result)

            print(f"📄 报告已保存到: {report_file}")

            return result

        except Exception as e:
            return f"报告生成失败: {str(e)}"

# 示例使用和演示
def demo_security_agent():
    """演示 SecurityAgent 的使用"""
    print("🛡️ SecurityAgent 演示开始\n")

    # 创建安全测试智能体
    agent = SecurityAgent()

    # 添加授权目标（仅限本地测试）
    print("📋 添加授权测试目标...")
    agent.add_authorized_target("127.0.0.1")
    agent.add_authorized_target("localhost")

    # 演示任务
    demo_tasks = [
        "对 127.0.0.1 进行端口扫描",
        "检查本地系统的安全配置",
        "分析发现的安全问题并提供修复建议",
        "生成安全测试摘要报告"
    ]

    for i, task in enumerate(demo_tasks, 1):
        print(f"\n🎯 任务 {i}: {task}")
        try:
            result = agent.agent.run(task)
            print(f"✅ 结果: {result[:300]}...\n")
        except Exception as e:
            print(f"❌ 错误: {e}\n")

    print("🎉 演示完成！")
    print("\n⚠️ 重要提醒:")
    print("- 仅在授权环境中使用此工具")
    print("- 遵循负责任的安全研究原则")
    print("- 及时修复发现的安全问题")

if __name__ == "__main__":
    # 显示免责声明
    print("=" * 60)
    print("🛡️ SecurityAgent - 安全测试智能体")
    print("=" * 60)
    print("⚠️ 重要声明:")
    print("本工具仅用于授权的安全测试和研究目的")
    print("使用前必须获得目标系统的明确授权")
    print("禁止用于任何非法或恶意活动")
    print("=" * 60)

    # 运行演示
    demo_security_agent()
            
        except Exception as e:
            return f"配置内容检查失败: {str(e)}"
    
    def _system_config_check(self, target: str) -> str:
        """系统配置检查"""
        result = f"系统配置检查 - {target}\n\n"
        
        # 检查常见的不安全配置
        checks = [
            ("SSH 配置", "/etc/ssh/sshd_config"),
            ("Apache 配置", "/etc/apache2/apache2.conf"),
            ("Nginx 配置", "/etc/nginx/nginx.conf"),
        ]
        
        for name, path in checks:
            if os.path.exists(path):
                result += f"检查 {name}: {path}\n"
                result += self._check_config_content(path)
                result += "\n"
        
        return result
    
    def _analyze_vulnerabilities(self, scan_output: str) -> List[str]:
        """分析扫描输出中的漏洞"""
        vulnerabilities = []
        
        # 常见漏洞关键词
        vuln_keywords = [
            'VULNERABLE', 'CVE-', 'EXPLOIT', 'CRITICAL', 'HIGH',
            'SQL injection', 'XSS', 'CSRF', 'RCE', 'LFI', 'RFI'
        ]
        
        lines = scan_output.split('\n')
        for line in lines:
            for keyword in vuln_keywords:
                if keyword.lower() in line.lower():
                    vulnerabilities.append(line.strip())
                    break
        
        return vulnerabilities
    
    def _log_scan(self, scan_type: str, target: str, result: str):
        """记录扫描历史"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "scan_type": scan_type,
            "target": target,
            "result_summary": result[:200] + "..." if len(result) > 200 else result
        }
        
        self.scan_history.append(log_entry)
        
        # 保存到文件
        log_file = Path("security_scan_log.json")
        try:
            if log_file.exists():
                with open(log_file, 'r') as f:
                    logs = json.load(f)
            else:
                logs = []
            
            logs.append(log_entry)
            
            with open(log_file, 'w') as f:
                json.dump(logs, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"日志保存失败: {e}")

class VulnerabilityAnalysisTool(Tool):
    """漏洞分析工具 - 分析和评估发现的漏洞"""
    
    name = "vulnerability_analysis"
    description = "分析漏洞信息，提供风险评估和修复建议"
    inputs = {
        "vulnerability_data": {
            "type": "string",
            "description": "漏洞数据或扫描结果"
        },
        "analysis_type": {
            "type": "string",
            "description": "分析类型：risk_assessment, fix_recommendation, exploit_analysis",
            "nullable": True
        }
    }
    output_type = "string"
    
    def forward(self, vulnerability_data: str, analysis_type: str = "risk_assessment") -> str:
        """分析漏洞"""
        try:
            if analysis_type == "risk_assessment":
                return self._assess_risk(vulnerability_data)
            elif analysis_type == "fix_recommendation":
                return self._recommend_fixes(vulnerability_data)
            elif analysis_type == "exploit_analysis":
                return self._analyze_exploitability(vulnerability_data)
            else:
                return f"不支持的分析类型: {analysis_type}"
                
        except Exception as e:
            return f"漏洞分析失败: {str(e)}"
    
    def _assess_risk(self, vuln_data: str) -> str:
        """风险评估"""
        result = "🔍 漏洞风险评估\n\n"
        
        # 基于关键词的风险评级
        high_risk_keywords = ['RCE', 'SQL injection', 'authentication bypass', 'privilege escalation']
        medium_risk_keywords = ['XSS', 'CSRF', 'information disclosure', 'directory traversal']
        low_risk_keywords = ['version disclosure', 'banner grabbing', 'weak cipher']
        
        vuln_lower = vuln_data.lower()
        
        risk_level = "LOW"
        risk_factors = []
        
        for keyword in high_risk_keywords:
            if keyword.lower() in vuln_lower:
                risk_level = "HIGH"
                risk_factors.append(f"发现高危漏洞: {keyword}")
        
        if risk_level != "HIGH":
            for keyword in medium_risk_keywords:
                if keyword.lower() in vuln_lower:
                    risk_level = "MEDIUM"
                    risk_factors.append(f"发现中危漏洞: {keyword}")
        
        result += f"风险等级: {risk_level}\n\n"
        
        if risk_factors:
            result += "风险因素:\n"
            for factor in risk_factors:
                result += f"- {factor}\n"
        
        result += "\n建议措施:\n"
        if risk_level == "HIGH":
            result += "- 🚨 立即修复，暂停相关服务\n"
            result += "- 📋 制定应急响应计划\n"
            result += "- 🔍 进行全面安全审计\n"
        elif risk_level == "MEDIUM":
            result += "- ⚠️ 尽快修复，加强监控\n"
            result += "- 🛡️ 实施临时防护措施\n"
        else:
            result += "- 📝 计划修复，持续监控\n"
        
        return result
    
    def _recommend_fixes(self, vuln_data: str) -> str:
        """修复建议"""
        result = "🔧 漏洞修复建议\n\n"
        
        # 基于漏洞类型的修复建议
        fix_recommendations = {
            'sql injection': [
                "使用参数化查询或预编译语句",
                "输入验证和过滤",
                "最小权限原则配置数据库用户",
                "启用 WAF 防护"
            ],
            'xss': [
                "输出编码和转义",
                "内容安全策略 (CSP)",
                "输入验证和过滤",
                "使用安全的模板引擎"
            ],
            'rce': [
                "禁用危险函数",
                "输入验证和沙箱执行",
                "最小权限运行服务",
                "网络隔离和访问控制"
            ],
            'weak password': [
                "强制复杂密码策略",
                "启用多因素认证",
                "定期密码更换",
                "账户锁定策略"
            ]
        }
        
        vuln_lower = vuln_data.lower()
        found_fixes = []
        
        for vuln_type, fixes in fix_recommendations.items():
            if vuln_type in vuln_lower:
                result += f"针对 {vuln_type.upper()} 的修复建议:\n"
                for fix in fixes:
                    result += f"- {fix}\n"
                result += "\n"
                found_fixes.extend(fixes)
        
        if not found_fixes:
            result += "通用安全加固建议:\n"
            result += "- 及时更新系统和软件补丁\n"
            result += "- 实施最小权限原则\n"
            result += "- 启用安全日志和监控\n"
            result += "- 定期进行安全评估\n"
            result += "- 建立安全响应流程\n"
        
        return result
    
    def _analyze_exploitability(self, vuln_data: str) -> str:
        """可利用性分析"""
        result = "⚡ 漏洞可利用性分析\n\n"
        
        # 分析利用难度
        easy_exploit_indicators = ['default credentials', 'no authentication', 'public exploit']
        medium_exploit_indicators = ['authentication required', 'local access needed']
        hard_exploit_indicators = ['complex conditions', 'race condition', 'timing attack']
        
        vuln_lower = vuln_data.lower()
        
        exploit_difficulty = "UNKNOWN"
        
        for indicator in easy_exploit_indicators:
            if indicator in vuln_lower:
                exploit_difficulty = "EASY"
                break
        
        if exploit_difficulty == "UNKNOWN":
            for indicator in medium_exploit_indicators:
                if indicator in vuln_lower:
                    exploit_difficulty = "MEDIUM"
                    break
        
        if exploit_difficulty == "UNKNOWN":
            for indicator in hard_exploit_indicators:
                if indicator in vuln_lower:
                    exploit_difficulty = "HARD"
                    break
        
        result += f"利用难度: {exploit_difficulty}\n\n"
        
        # 提供防护建议
        result += "防护措施:\n"
        if exploit_difficulty == "EASY":
            result += "- 🚨 立即实施访问控制\n"
            result += "- 🔒 启用强认证机制\n"
            result += "- 📊 加强实时监控\n"
        elif exploit_difficulty == "MEDIUM":
            result += "- 🛡️ 加强边界防护\n"
            result += "- 🔍 增强日志审计\n"
            result += "- ⚠️ 限制网络访问\n"
        else:
            result += "- 📝 持续监控异常行为\n"
            result += "- 🔄 定期安全评估\n"
        
        return result
