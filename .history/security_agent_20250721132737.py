"""
SecurityAgent - 基于 smolagents 的安全测试与漏洞发现智能体

⚠️ 重要声明：
本工具仅用于授权的安全测试和研究目的
使用前必须获得目标系统的明确授权
禁止用于任何非法或恶意活动

主要功能：
- 自动化安全扫描（端口扫描、漏洞扫描）
- 漏洞识别与分析
- 安全配置检查
- 防御建议生成
- 安全报告生成
"""

import os
import socket
from datetime import datetime
from typing import List

from smolagents import CodeAgent, Tool, InferenceClientModel, PythonInterpreterTool

class SecurityScanTool(Tool):
    """安全扫描工具 - 集成多种安全扫描工具"""
    
    name = "security_scan"
    description = "执行安全扫描，包括端口扫描、漏洞扫描等（仅限授权测试）"
    inputs = {
        "scan_type": {
            "type": "string",
            "description": "扫描类型：port_scan, vuln_scan, web_scan, config_check"
        },
        "target": {
            "type": "string",
            "description": "扫描目标（IP、域名或文件路径）"
        },
        "options": {
            "type": "string",
            "description": "扫描选项和参数",
            "nullable": True
        }
    }
    output_type = "string"
    
    def __init__(self):
        super().__init__()
        # 仅允许本地目标，确保安全使用
        self.authorized_targets = {"127.0.0.1", "localhost", "0.0.0.0"}
    
    def add_authorized_target(self, target: str):
        """添加授权测试目标"""
        self.authorized_targets.add(target)
        print(f"✅ 已添加授权目标: {target}")
    
    def _check_authorization(self, target: str) -> bool:
        """检查目标是否已授权"""
        return target in self.authorized_targets
    
    def forward(self, scan_type: str, target: str, options: str = "") -> str:
        """执行安全扫描"""
        # 授权检查
        if not self._check_authorization(target):
            return f"❌ 错误: 目标 {target} 未授权。仅允许扫描本地目标。"
        
        try:
            if scan_type == "port_scan":
                return self._port_scan(target, options)
            elif scan_type == "vuln_scan":
                return self._vulnerability_scan(target, options)
            elif scan_type == "web_scan":
                return self._web_scan(target, options)
            elif scan_type == "config_check":
                return self._config_check(target, options)
            else:
                return f"不支持的扫描类型: {scan_type}"
        except Exception as e:
            return f"扫描失败: {str(e)}"
    
    def _port_scan(self, target: str, options: str) -> str:
        """端口扫描 - 使用 Python socket 实现"""
        common_ports = [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 3389, 5432, 3306]
        open_ports = []
        
        scan_result = f"🔍 端口扫描结果 - {target}\n\n"
        
        for port in common_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((target, port))
                
                if result == 0:
                    open_ports.append(port)
                    service = self._identify_service(port)
                    scan_result += f"✅ 端口 {port}: 开放 ({service})\n"
                
                sock.close()
                
            except Exception as e:
                scan_result += f"❌ 端口 {port}: 扫描失败 - {str(e)}\n"
        
        scan_result += f"\n📊 总结: 发现 {len(open_ports)} 个开放端口\n"
        scan_result += f"开放端口列表: {open_ports}\n"
        
        if open_ports:
            scan_result += "\n🛡️ 安全建议:\n"
            scan_result += "- 关闭不必要的服务\n"
            scan_result += "- 使用防火墙限制访问\n"
            scan_result += "- 定期更新服务软件\n"
        
        return scan_result
    
    def _identify_service(self, port: int) -> str:
        """识别端口对应的服务"""
        service_map = {
            21: "FTP", 22: "SSH", 23: "Telnet", 25: "SMTP",
            53: "DNS", 80: "HTTP", 110: "POP3", 143: "IMAP",
            443: "HTTPS", 993: "IMAPS", 995: "POP3S",
            3389: "RDP", 5432: "PostgreSQL", 3306: "MySQL"
        }
        return service_map.get(port, "Unknown")
    
    def _vulnerability_scan(self, target: str, options: str) -> str:
        """漏洞扫描 - 基于开放端口的安全检查"""
        scan_result = f"🔍 漏洞扫描结果 - {target}\n\n"
        
        try:
            # 检查常见服务的安全问题
            if self._check_port_open(target, 22):
                scan_result += "🔍 SSH 服务安全检查 (端口 22):\n"
                scan_result += "- SSH 服务已启用\n"
                scan_result += "⚠️ 建议: 禁用密码认证，使用密钥认证\n"
                scan_result += "⚠️ 建议: 更改默认端口\n\n"
            
            if self._check_port_open(target, 80):
                scan_result += "🔍 HTTP 服务安全检查 (端口 80):\n"
                scan_result += "- HTTP 服务已启用\n"
                scan_result += "⚠️ 建议: 使用 HTTPS 替代 HTTP\n"
                scan_result += "⚠️ 建议: 配置安全头\n\n"
            
            if self._check_port_open(target, 3306):
                scan_result += "🔍 MySQL 服务安全检查 (端口 3306):\n"
                scan_result += "- MySQL 服务已启用\n"
                scan_result += "⚠️ 建议: 限制数据库访问\n"
                scan_result += "⚠️ 建议: 使用强密码和加密连接\n\n"
            
            if self._check_port_open(target, 21):
                scan_result += "🚨 FTP 服务安全检查 (端口 21):\n"
                scan_result += "- FTP 服务已启用\n"
                scan_result += "🚨 高危: FTP 传输未加密\n"
                scan_result += "⚠️ 建议: 使用 SFTP 或 FTPS\n\n"
                
        except Exception as e:
            scan_result += f"漏洞扫描过程中出错: {str(e)}\n"
        
        scan_result += "\n🛡️ 通用安全建议:\n"
        scan_result += "- 定期更新系统补丁\n"
        scan_result += "- 使用强密码策略\n"
        scan_result += "- 启用防火墙保护\n"
        scan_result += "- 定期进行安全审计\n"
        scan_result += "- 实施最小权限原则\n"
        
        return scan_result
    
    def _check_port_open(self, target: str, port: int) -> bool:
        """检查端口是否开放"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((target, port))
            sock.close()
            return result == 0
        except:
            return False
    
    def _web_scan(self, target: str, options: str) -> str:
        """Web 应用安全扫描"""
        scan_result = f"🔍 Web 安全检查 - {target}\n\n"
        
        try:
            import requests
            
            try:
                response = requests.get(f"http://{target}", timeout=5)
                scan_result += f"HTTP 状态码: {response.status_code}\n"
                scan_result += f"服务器: {response.headers.get('Server', 'Unknown')}\n\n"
                
                # 检查安全头
                scan_result += "🔒 安全头检查:\n"
                security_headers = {
                    'X-Frame-Options': '防止点击劫持',
                    'X-XSS-Protection': 'XSS 保护',
                    'X-Content-Type-Options': '内容类型保护',
                    'Strict-Transport-Security': 'HTTPS 强制',
                    'Content-Security-Policy': '内容安全策略'
                }
                
                missing_headers = []
                for header, description in security_headers.items():
                    if header in response.headers:
                        scan_result += f"✅ {header}: {response.headers[header]}\n"
                    else:
                        scan_result += f"❌ 缺失 {header} ({description})\n"
                        missing_headers.append(header)
                
                if missing_headers:
                    scan_result += f"\n⚠️ 发现 {len(missing_headers)} 个缺失的安全头\n"
                    scan_result += "建议配置这些安全头以提高安全性\n"
                
            except requests.RequestException as e:
                scan_result += f"HTTP 请求失败: {str(e)}\n"
                scan_result += "可能原因: 服务未运行或网络不可达\n"
                
        except ImportError:
            scan_result += "⚠️ 需要安装 requests 库进行 Web 扫描\n"
            scan_result += "安装命令: pip install requests\n"
        
        return scan_result
    
    def _config_check(self, target: str, options: str) -> str:
        """配置安全检查"""
        if os.path.isfile(target):
            return self._file_security_check(target)
        else:
            return self._system_config_check()
    
    def _file_security_check(self, file_path: str) -> str:
        """文件安全检查"""
        try:
            scan_result = f"🔍 文件安全检查 - {file_path}\n\n"
            
            if not os.path.exists(file_path):
                return f"❌ 文件不存在: {file_path}"
            
            file_stat = os.stat(file_path)
            permissions = oct(file_stat.st_mode)[-3:]
            
            scan_result += f"文件权限: {permissions}\n"
            
            # 检查危险权限
            security_issues = []
            if permissions.endswith('7') or permissions.endswith('6'):
                security_issues.append("文件对其他用户可写")
            
            if permissions.startswith('7'):
                security_issues.append("文件对所有者可执行")
            
            if security_issues:
                scan_result += "⚠️ 安全问题:\n"
                for issue in security_issues:
                    scan_result += f"- {issue}\n"
            else:
                scan_result += "✅ 文件权限配置合理\n"
            
            size = file_stat.st_size
            scan_result += f"文件大小: {size} 字节\n"
            
            return scan_result
            
        except Exception as e:
            return f"文件检查失败: {str(e)}"
    
    def _system_config_check(self) -> str:
        """系统配置安全检查"""
        scan_result = "🔍 系统配置安全检查\n\n"
        
        # 检查敏感文件
        sensitive_files = [
            ".env", ".config", "config.ini", "settings.py",
            "database.yml", "secrets.json", "id_rsa", "id_dsa"
        ]
        
        scan_result += "🔍 敏感文件检查:\n"
        found_files = []
        
        for filename in sensitive_files:
            if os.path.exists(filename):
                found_files.append(filename)
                scan_result += f"⚠️ 发现敏感文件: {filename}\n"
        
        if not found_files:
            scan_result += "✅ 未发现明显的敏感文件\n"
        
        scan_result += "\n🛡️ 安全建议:\n"
        scan_result += "- 保护敏感配置文件，设置适当权限\n"
        scan_result += "- 使用环境变量存储密钥和密码\n"
        scan_result += "- 定期检查文件权限\n"
        scan_result += "- 避免在代码中硬编码敏感信息\n"
        
        return scan_result

class SecurityAgent:
    """安全测试智能体 - 主要的 Agent 类"""
    
    def __init__(self):
        """初始化 SecurityAgent"""
        self.security_scan_tool = SecurityScanTool()
        
        self.tools = [
            PythonInterpreterTool(),
            self.security_scan_tool
        ]
        
        # 初始化模型
        try:
            self.model = InferenceClientModel(
                model_id="Qwen/Qwen2.5-Coder-32B-Instruct",
                provider="auto"
            )
            
            self.agent = CodeAgent(
                tools=self.tools,
                model=self.model,
                stream_outputs=False
            )
            
            print("🛡️ 安全测试智能体已初始化完成！")
            
        except Exception as e:
            print(f"⚠️ 模型初始化失败: {e}")
            print("将使用基础扫描功能")
            self.agent = None
        
        print("⚠️ 仅限授权环境使用")
    
    def run_security_scan(self, target: str = "127.0.0.1", scan_type: str = "port_scan") -> str:
        """运行安全扫描"""
        try:
            print(f"🔍 开始对 {target} 进行 {scan_type} 扫描")
            result = self.security_scan_tool.forward(scan_type, target)
            print("✅ 扫描完成")
            return result
        except Exception as e:
            return f"扫描失败: {str(e)}"
    
    def comprehensive_scan(self, target: str = "127.0.0.1") -> str:
        """综合安全扫描"""
        print(f"🔍 开始对 {target} 进行综合安全扫描")
        
        results = []
        scan_types = ["port_scan", "vuln_scan", "web_scan", "config_check"]
        
        for scan_type in scan_types:
            print(f"执行 {scan_type}...")
            result = self.security_scan_tool.forward(scan_type, target)
            results.append(f"=== {scan_type.upper()} ===\n{result}\n")
        
        final_report = "\n".join(results)
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"security_scan_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(final_report)
        
        print(f"📄 报告已保存到: {report_file}")
        return final_report
    
    def add_target(self, target: str):
        """添加授权扫描目标"""
        self.security_scan_tool.add_authorized_target(target)

def demo_security_agent():
    """演示 SecurityAgent 的使用"""
    print("🛡️ SecurityAgent 演示开始\n")
    print("⚠️ 本演示仅在本地环境进行，符合安全使用原则\n")
    
    # 创建安全测试智能体
    agent = SecurityAgent()
    
    # 演示各种扫描
    print("1. 端口扫描演示:")
    result1 = agent.run_security_scan("127.0.0.1", "port_scan")
    print(result1[:500] + "...\n")
    
    print("2. 漏洞扫描演示:")
    result2 = agent.run_security_scan("127.0.0.1", "vuln_scan")
    print(result2[:500] + "...\n")
    
    print("3. 配置检查演示:")
    result3 = agent.run_security_scan("127.0.0.1", "config_check")
    print(result3[:500] + "...\n")
    
    print("🎉 演示完成！")

if __name__ == "__main__":
    print("=" * 60)
    print("🛡️ SecurityAgent - 安全测试智能体")
    print("=" * 60)
    print("⚠️ 重要声明:")
    print("本工具仅用于授权的安全测试和研究目的")
    print("使用前必须获得目标系统的明确授权")
    print("禁止用于任何非法或恶意活动")
    print("=" * 60)
    
    demo_security_agent()
