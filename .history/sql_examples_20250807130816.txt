# 用户查询示例

自然语言查询: 查找所有年龄大于25岁的用户的姓名和邮箱
SQL查询: SELECT name, email FROM users WHERE age > 25;

自然语言查询: 统计每个产品类别的平均价格
SQL查询: SELECT category, AVG(price) as average_price FROM products GROUP BY category;

自然语言查询: 找出订单数量最多的前5名用户
SQL查询: SELECT u.name, COUNT(o.id) as order_count FROM users u JOIN orders o ON u.id = o.user_id GROUP BY u.id, u.name ORDER BY order_count DESC LIMIT 5;

自然语言查询: 查询2023年1月1日之后下单的所有订单及其产品名称
SQL查询: SELECT o.id as order_id, p.name as product_name FROM orders o JOIN products p ON o.product_id = p.id WHERE o.order_date > '2023-01-01';

自然语言查询: 计算每个用户的总消费金额
SQL查询: SELECT u.name, SUM(p.price * o.quantity) as total_spent FROM users u JOIN orders o ON u.id = o.user_id JOIN products p ON o.product_id = p.id GROUP BY u.id, u.name;

自然语言查询: 查找没有下过订单的用户
SQL查询: SELECT name, email FROM users u WHERE NOT EXISTS (SELECT 1 FROM orders o WHERE o.user_id = u.id);

自然语言查询: 找出价格最高的三种产品
SQL查询: SELECT name, price FROM products ORDER BY price DESC LIMIT 3;

自然语言查询: 查询每个用户的最近一次订单
SQL查询: SELECT u.name, o.id as order_id, o.order_date FROM users u JOIN orders o ON u.id = o.user_id WHERE o.order_date = (SELECT MAX(order_date) FROM orders WHERE user_id = u.id);

自然语言查询: 统计每月的订单总数
SQL查询: SELECT strftime('%Y-%m', order_date) as month, COUNT(*) as order_count FROM orders GROUP BY month ORDER BY month;

自然语言查询: 查找购买过"电子产品"类别商品的用户
SQL查询: SELECT DISTINCT u.name, u.email FROM users u JOIN orders o ON u.id = o.user_id JOIN products p ON o.product_id = p.id WHERE p.category = '电子产品';

自然语言查询: 计算每个产品的销售总量
SQL查询: SELECT p.name, SUM(o.quantity) as total_sold FROM products p JOIN orders o ON p.id = o.product_id GROUP BY p.id, p.name;

自然语言查询: 查找年龄在18到30岁之间且有过订单的用户
SQL查询: SELECT DISTINCT u.name, u.age FROM users u JOIN orders o ON u.id = o.user_id WHERE u.age BETWEEN 18 AND 30;

自然语言查询: 统计不同年龄段用户的数量
SQL查询: SELECT CASE WHEN age < 18 THEN '未成年' WHEN age BETWEEN 18 AND 30 THEN '青年' WHEN age BETWEEN 31 AND 50 THEN '中年' ELSE '老年' END as age_group, COUNT(*) as user_count FROM users GROUP BY age_group;

自然语言查询: 查找订单金额超过1000元的订单及其用户
SQL查询: SELECT o.id as order_id, u.name as user_name, SUM(p.price * o.quantity) as total_amount FROM orders o JOIN users u ON o.user_id = u.id JOIN products p ON o.product_id = p.id GROUP BY o.id, u.name HAVING total_amount > 1000;

自然语言查询: 查询每个用户的平均订单金额
SQL查询: SELECT u.name, AVG(p.price * o.quantity) as avg_order_amount FROM users u JOIN orders o ON u.id = o.user_id JOIN products p ON o.product_id = p.id GROUP BY u.id, u.name;

自然语言查询: 找出最受欢迎的产品类别
SQL查询: SELECT p.category, COUNT(o.id) as order_count FROM products p JOIN orders o ON p.id = o.product_id GROUP BY p.category ORDER BY order_count DESC LIMIT 1;

自然语言查询: 查询用户的注册时间和首次下单时间
SQL查询: SELECT u.name, u.created_at as registration_time, MIN(o.order_date) as first_order_time FROM users u LEFT JOIN orders o ON u.id = o.user_id GROUP BY u.id, u.name, u.created_at;

自然语言查询: 统计每个用户的不同产品购买数量
SQL查询: SELECT u.name, COUNT(DISTINCT o.product_id) as unique_products FROM users u JOIN orders o ON u.id = o.user_id GROUP BY u.id, u.name;

自然语言查询: 查找从未购买过某类别产品的用户
SQL查询: SELECT u.name FROM users u WHERE NOT EXISTS (SELECT 1 FROM orders o JOIN products p ON o.product_id = p.id WHERE o.user_id = u.id AND p.category = '指定类别');

自然语言查询: 计算产品的平均评分
SQL查询: SELECT p.name, AVG(r.rating) as average_rating FROM products p JOIN reviews r ON p.id = r.product_id GROUP BY p.id, p.name;

自然语言查询: 查找同时购买了产品A和产品B的用户
SQL查询: SELECT u.name FROM users u WHERE EXISTS (SELECT 1 FROM orders o JOIN products p ON o.product_id = p.id WHERE o.user_id = u.id AND p.name = '产品A') AND EXISTS (SELECT 1 FROM orders o JOIN products p ON o.product_id = p.id WHERE o.user_id = u.id AND p.name = '产品B');

自然语言查询: 统计每个季度的销售额
SQL查询: SELECT strftime('%Y-Q%d', order_date, 'start of month', '+2 month', 'start of month', '-1 day') as quarter, SUM(p.price * o.quantity) as total_sales FROM orders o JOIN products p ON o.product_id = p.id GROUP BY quarter ORDER BY quarter;

自然语言查询: 查找最近30天内活跃的用户
SQL查询: SELECT DISTINCT u.name FROM users u JOIN orders o ON u.id = o.user_id WHERE o.order_date >= date('now', '-30 days');

自然语言查询: 计算每个用户的最大单笔订单金额
SQL查询: SELECT u.name, MAX(p.price * o.quantity) as max_order_amount FROM users u JOIN orders o ON u.id = o.user_id JOIN products p ON o.product_id = p.id GROUP BY u.id, u.name;

自然语言查询: 查找购买过所有类别产品的用户
SQL查询: SELECT u.name FROM users u WHERE (SELECT COUNT(DISTINCT p.category) FROM orders o JOIN products p ON o.product_id = p.id WHERE o.user_id = u.id) = (SELECT COUNT(DISTINCT category) FROM products);

自然语言查询: 统计每个用户的订单频率
SQL查询: SELECT u.name, COUNT(o.id) as order_count, (julianday(MAX(o.order_date)) - julianday(MIN(o.order_date))) / COUNT(o.id) as avg_days_between_orders FROM users u JOIN orders o ON u.id = o.user_id GROUP BY u.id, u.name HAVING COUNT(o.id) > 1;

自然语言查询: 查找价格高于平均价格的产品
SQL查询: SELECT name, price FROM products WHERE price > (SELECT AVG(price) FROM products);

自然语言查询: 查询用户的累计消费排名
SQL查询: SELECT u.name, SUM(p.price * o.quantity) as total_spent, RANK() OVER (ORDER BY SUM(p.price * o.quantity) DESC) as spending_rank FROM users u JOIN orders o ON u.id = o.user_id JOIN products p ON o.product_id = p.id GROUP BY u.id, u.name;

自然语言查询: 找出每个月销售额最高的产品
SQL查询: WITH monthly_sales AS (SELECT strftime('%Y-%m', o.order_date) as month, p.id, p.name, SUM(p.price * o.quantity) as sales, RANK() OVER (PARTITION BY strftime('%Y-%m', o.order_date) ORDER BY SUM(p.price * o.quantity) DESC) as sales_rank FROM orders o JOIN products p ON o.product_id = p.id GROUP BY month, p.id, p.name) SELECT month, name, sales FROM monthly_sales WHERE sales_rank = 1 ORDER BY month;