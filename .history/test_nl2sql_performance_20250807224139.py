#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NL2SQL RAG系统性能测试脚本

本脚本用于测试NL2SQL系统在一组测试查询上的性能，包括准确率和响应时间。
"""

import os
import time
import json
from typing import List, Dict, Any, Tuple
from nl2sql_rag_system import NL2SQLSystem

# 设置OpenAI API密钥
os.environ["OPENAI_API_KEY"] = "your-api-key"

# 测试查询集
TEST_QUERIES = [
    "查找所有年龄大于25岁的用户的姓名和邮箱",
    "统计每个产品类别的平均价格",
    "找出订单数量最多的前5名用户",
    "查询2023年1月1日之后下单的所有订单及其产品名称",
    "计算每个用户的总消费金额",
    "查找没有下过订单的用户",
    "找出价格最高的三种产品",
    "查询每个用户的最近一次订单",
    "统计每月的订单总数",
    "查找购买过'电子产品'类别商品的用户"
]

# 预期的SQL查询结果（简化版，仅用于测试）
EXPECTED_RESULTS = [
    "SELECT name, email FROM users WHERE age > 25;",
    "SELECT category, AVG(price) as average_price FROM products GROUP BY category;",
    "SELECT u.name, COUNT(o.id) as order_count FROM users u JOIN orders o ON u.id = o.user_id GROUP BY u.id, u.name ORDER BY order_count DESC LIMIT 5;",
    "SELECT o.id as order_id, p.name as product_name FROM orders o JOIN products p ON o.product_id = p.id WHERE o.order_date > '2023-01-01';",
    "SELECT u.name, SUM(p.price * o.quantity) as total_spent FROM users u JOIN orders o ON u.id = o.user_id JOIN products p ON o.product_id = p.id GROUP BY u.id, u.name;",
    "SELECT name, email FROM users u WHERE NOT EXISTS (SELECT 1 FROM orders o WHERE o.user_id = u.id);",
    "SELECT name, price FROM products ORDER BY price DESC LIMIT 3;",
    "SELECT u.name, o.id as order_id, o.order_date FROM users u JOIN orders o ON u.id = o.user_id WHERE o.order_date = (SELECT MAX(order_date) FROM orders WHERE user_id = u.id);",
    "SELECT strftime('%Y-%m', order_date) as month, COUNT(*) as order_count FROM orders GROUP BY month ORDER BY month;",
    "SELECT DISTINCT u.name, u.email FROM users u JOIN orders o ON u.id = o.user_id JOIN products p ON o.product_id = p.id WHERE p.category = '电子产品';"
]

def normalize_sql(sql: str) -> str:
    """
    规范化SQL查询字符串，以便进行比较
    
    :param sql: SQL查询字符串
    :return: 规范化后的SQL查询字符串
    """
    # 移除所有空白字符
    sql = ' '.join(sql.split())
    # 转换为小写
    sql = sql.lower()
    # 移除结尾的分号
    if sql.endswith(';'):
        sql = sql[:-1]
    # 移除多余的空格
    sql = ' '.join(sql.split())
    return sql

def calculate_similarity(sql1: str, sql2: str) -> float:
    """
    计算两个SQL查询的相似度（简化版）
    
    :param sql1: 第一个SQL查询
    :param sql2: 第二个SQL查询
    :return: 相似度分数（0-1）
    """
    # 规范化SQL
    sql1 = normalize_sql(sql1)
    sql2 = normalize_sql(sql2)
    
    # 将SQL拆分为单词
    words1 = set(sql1.split())
    words2 = set(sql2.split())
    
    # 计算Jaccard相似度
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))
    
    return intersection / union if union > 0 else 0.0

def evaluate_nl2sql_system(system: NL2SQLSystem, queries: List[str], expected_results: List[str]) -> Dict[str, Any]:
    """
    评估NL2SQL系统的性能
    
    :param system: NL2SQL系统实例
    :param queries: 测试查询列表
    :param expected_results: 预期结果列表
    :return: 评估结果字典
    """
    results = []
    total_time = 0
    correct_count = 0
    similarity_scores = []
    
    for i, (query, expected) in enumerate(zip(queries, expected_results)):
        print(f"\n测试查询 {i+1}/{len(queries)}: {query}")
        
        # 计时开始
        start_time = time.time()
        
        # 执行查询
        generated_sql = system.natural_language_to_sql(query)
        
        # 计时结束
        end_time = time.time()
        query_time = end_time - start_time
        total_time += query_time
        
        # 计算相似度
        similarity = calculate_similarity(generated_sql, expected)
        similarity_scores.append(similarity)
        
        # 判断是否正确（相似度大于阈值）
        is_correct = similarity > 0.8
        if is_correct:
            correct_count += 1
        
        # 记录结果
        results.append({
            "query": query,
            "generated_sql": generated_sql,
            "expected_sql": expected,
            "similarity": similarity,
            "is_correct": is_correct,
            "time": query_time
        })
        
        print(f"生成的SQL: {generated_sql}")
        print(f"预期的SQL: {expected}")
        print(f"相似度: {similarity:.2f}")
        print(f"是否正确: {is_correct}")
        print(f"响应时间: {query_time:.2f}秒")
    
    # 计算总体指标
    accuracy = correct_count / len(queries) if queries else 0
    avg_time = total_time / len(queries) if queries else 0
    avg_similarity = sum(similarity_scores) / len(similarity_scores) if similarity_scores else 0
    
    return {
        "accuracy": accuracy,
        "avg_time": avg_time,
        "avg_similarity": avg_similarity,
        "results": results
    }

def main():
    # 初始化NL2SQL系统
    nl2sql_system = NL2SQLSystem(
        examples_path="sql_examples.txt",
        db_schema_path="db_schema.json",
        model_name="gpt-3.5-turbo"
    )
    
    # 评估系统性能
    evaluation_results = evaluate_nl2sql_system(nl2sql_system, TEST_QUERIES, EXPECTED_RESULTS)
    
    # 打印总体结果
    print("\n===== 性能评估结果 =====")
    print(f"准确率: {evaluation_results['accuracy']:.2%}")
    print(f"平均响应时间: {evaluation_results['avg_time']:.2f}秒")
    print(f"平均相似度: {evaluation_results['avg_similarity']:.2f}")
    
    # 保存结果到文件
    with open("evaluation_results.json", "w", encoding="utf-8") as f:
        json.dump(evaluation_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n评估结果已保存到 evaluation_results.json")

if __name__ == "__main__":
    main()