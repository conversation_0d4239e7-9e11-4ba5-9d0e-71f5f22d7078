"""
简单的 smolagents 测试
验证基本功能是否正常工作
"""

import os
from smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试 smolagents 基本功能...\n")
    
    try:
        # 创建模型
        model = InferenceClientModel(
            model_id="Qwen/Qwen2.5-Coder-32B-Instruct",
            token=os.environ.get("HUGGINGFACE_TOKEN"),
            provider="auto"  # 自动选择可用的提供商
        )
        print("✅ 模型创建成功")
        
        # 创建工具
        tools = [PythonInterpreterTool()]
        print("✅ 工具创建成功")
        
        # 创建 Agent
        agent = CodeAgent(
            tools=tools,
            model=model,
            stream_outputs=False,  # 关闭流输出以便测试
        )
        print("✅ Agent 创建成功")
        
        # 测试简单任务
        print("\n🔍 测试简单计算任务...")
        result = agent.run("计算 2 + 3 * 4 的结果")
        print(f"结果: {result}")
        
        print("\n✅ 所有测试通过！smolagents 工作正常。")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    if success:
        print("\n🎉 可以继续运行完整的示例了！")
    else:
        print("\n💡 请检查:")
        print("1. 网络连接")
        print("2. smolagents 安装")
        print("3. 环境变量设置")
