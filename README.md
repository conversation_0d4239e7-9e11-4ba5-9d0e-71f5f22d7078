# Agent 学习项目

这是一个基于 Hugging Face smolagents 库和 <PERSON><PERSON><PERSON><PERSON> 的 Agent 开发学习项目。

## 项目结构

```
.
├── README.md                 # 项目说明
├── requirements.txt          # 依赖列表
├── learn.py                 # 主学习文件
├── RAG.py                   # 基础RAG实现
├── nl2sql_rag_system.py     # NL2SQL RAG系统实现
├── sql_examples.txt         # SQL示例数据
├── examples/                # 示例代码
│   ├── basic_agent.py       # 基础 Agent 示例
│   ├── custom_tools.py      # 自定义工具示例
│   ├── multi_agent.py       # 多 Agent 协作示例
│   └── secure_execution.py  # 安全执行环境示例
├── tools/                   # 自定义工具
│   └── __init__.py
└── config/                  # 配置文件
    └── agent_config.py
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

1. 设置环境变量（如果使用外部模型）：
```bash
export OPENAI_API_KEY="your_api_key_here"
# 或者其他模型提供商的 API key
```

2. 运行基础示例：
```bash
python examples/basic_agent.py
```

3. 运行NL2SQL交互式演示：
```bash
python interactive_nl2sql.py
```

4. 运行NL2SQL示例：
```bash
python examples/nl2sql_example.py
```

5. 测试NL2SQL系统性能：
```bash
python test_nl2sql_performance.py
```

## smolagents 核心概念

### Agent 类型
- **CodeAgent**: 将动作写成 Python 代码片段
- **ToolCallingAgent**: 使用传统的工具调用方式

### 核心特性
- 🧑‍💻 代码优先的 Agent 设计
- 🛠️ 丰富的工具生态系统
- 🌐 支持多种模型提供商
- 🔒 安全的代码执行环境
- 🤗 Hub 集成

## 学习路径

1. [基础 Agent 使用](examples/basic_agent.py)
2. [创建自定义工具](examples/custom_tools.py)
3. [多 Agent 协作](examples/multi_agent.py)
4. [安全执行环境](examples/secure_execution.py)
5. [基础RAG实现](RAG.py)
6. [NL2SQL RAG系统](nl2sql_rag_system.py)

## NL2SQL RAG 系统 (增强版)

### 项目背景

本项目构建了一个基于LangChain与LLM的增强型RAG（检索增强生成）系统，实现自然语言自动转换为SQL查询。系统现已支持多种训练数据源的向量化处理，包括DDL语句、SQL问答对以及数据库描述文档，进一步提升了SQL生成的准确率和适应性。

### 🚀 新增功能特性

#### 多数据源向量化支持
- **SQL问答对**: 支持自然语言-SQL查询对的向量化
- **DDL语句**: 支持数据库定义语言语句的向量化
- **数据库文档**: 支持数据库描述文档的向量化
- **结构化Schema**: 支持JSON格式数据库结构的向量化

#### 智能文档处理
- **差异化分割策略**: 针对不同类型的文档使用不同的分割参数
- **元数据标记**: 为不同类型的训练数据添加元数据标记
- **内容分类**: 自动识别和分类不同类型的训练内容

### 系统架构

系统使用LangChain集成ChromaDB向量数据库，实现从用户自然语言提问到向量检索、Prompt组装、SQL生成与执行的完整链路：

1. **文档加载与处理**：加载SQL示例和数据库结构信息
2. **向量化与存储**：使用OpenAI Embeddings将文本转换为向量并存储在ChromaDB中
3. **语义检索**：根据用户查询检索相似的SQL示例
4. **动态Prompt构建**：结合数据库结构和检索到的示例构建提示词
5. **SQL生成**：使用LLM生成SQL查询
6. **查询执行**：执行生成的SQL并返回结果

### 核心功能

#### Prompt工程优化

系统根据用户提问与数据库结构的检索结果，动态构建Prompt模板，并集成Few-shot示例提升模型对SQL语法结构的理解能力。具体包括：

- **系统提示词**：包含数据库结构信息和SQL生成规则
- **Few-shot示例**：从向量数据库中检索与当前查询最相似的SQL示例
- **动态组装**：将数据库结构、检索到的示例和用户查询组合成最终提示词

#### 模型集成

通过LangChain接入GPT模型，实现高准确度的SQL查询生成：

- 配置System Prompt与ChatMemory模块，支持多轮问答与语境保持
- 使用温度参数为0，确保生成结果的一致性和确定性
- 支持自定义模型选择，可根据需求切换不同的LLM

### 使用方法

#### 基础使用（增强版）

```python
from nl2sql_rag_system import NL2SQLSystem

# 初始化增强的NL2SQL系统，支持多种数据源
nl2sql_system = NL2SQLSystem(
    examples_path="sql_examples.txt",           # SQL问答对示例
    db_schema_path="db_schema.json",            # 数据库结构JSON文件
    ddl_path="ddl_statements.sql",              # DDL语句文件
    db_docs_path="database_documentation.md",   # 数据库描述文档
    model_name="gpt-3.5-turbo",
    vector_db_path="./enhanced_chroma_db"
)
```

#### 准备训练数据

系统支持四种类型的训练数据：

**1. SQL问答对 (`sql_examples.txt`)**
```
自然语言查询: 查找所有年龄大于25岁的用户
SQL查询: SELECT * FROM users WHERE age > 25;

自然语言查询: 统计每个分类的商品数量
SQL查询: SELECT category, COUNT(*) FROM products GROUP BY category;
```

**2. DDL语句 (`ddl_statements.sql`)**
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT UNIQUE,
    age INTEGER
);

CREATE INDEX idx_users_email ON users(email);
```

**3. 数据库文档 (`database_documentation.md`)**
```markdown
# 数据库系统文档

## 用户表 (users)
用户表存储系统中所有用户的基本信息...

## 业务规则
- 用户邮箱必须唯一
- 年龄必须为正数
```

**4. 数据库结构 (`db_schema.json`)**
```json
{
  "users": {
    "columns": [
      {"name": "id", "type": "INTEGER"},
      {"name": "name", "type": "TEXT"}
    ],
    "foreign_keys": []
  }
}
```

#### 自然语言转SQL

```python
# 自然语言查询
query = "查找所有年龄大于25岁的用户的姓名和邮箱"

# 转换为SQL
sql = nl2sql_system.natural_language_to_sql(query)
print(f"SQL查询: {sql}")
```

#### 执行SQL查询

```python
# 执行SQL查询
results = nl2sql_system.execute_sql(sql, "your_database.db")
print("查询结果:", results)
```

#### 测试系统

运行测试脚本验证增强功能：

```bash
python test_enhanced_nl2sql.py
```

### 性能评估

在400条SQL问答对数据集上进行测试，系统表现如下：

- **准确率**：95.3%（相比基线模型的75%有显著提升）
- **响应时间**：平均0.8秒/查询
- **鲁棒性**：对复杂查询和不同表达方式有良好的适应能力
