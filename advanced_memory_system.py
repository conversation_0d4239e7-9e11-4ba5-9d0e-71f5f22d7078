#!/usr/bin/env python3
"""
高级记忆系统实现
展示Agent如何处理复杂的多轮对话场景
"""

import json
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from langchain.memory import ConversationBufferWindowMemory
from langchain.schema import BaseMessage, HumanMessage, AIMessage

@dataclass
class ConversationContext:
    """对话上下文数据结构"""
    sql_queries: List[str]  # 生成的SQL查询
    table_names: List[str]  # 涉及的表名
    last_results: Optional[Dict]  # 最近的查询结果
    current_task: Optional[str]  # 当前任务类型
    user_intent: Optional[str]  # 用户意图
    timestamp: datetime

class AdvancedMemorySystem:
    """
    高级记忆系统
    实现智能的多轮对话处理
    """
    
    def __init__(self, window_size: int = 10):
        self.window_size = window_size
        self.memory = ConversationBufferWindowMemory(
            k=window_size,
            memory_key="chat_history", 
            return_messages=True
        )
        
        # 上下文状态
        self.context = ConversationContext(
            sql_queries=[],
            table_names=[],
            last_results=None,
            current_task=None,
            user_intent=None,
            timestamp=datetime.now()
        )
        
        # 指代词映射
        self.reference_patterns = {
            r'这个查询|该查询|此查询': 'last_sql_query',
            r'这些结果|这些数据|结果': 'last_results',
            r'这个表|该表|此表': 'last_table',
            r'上面的|前面的|刚才的': 'previous_item',
            r'它|这个|那个': 'context_item'
        }
    
    def add_conversation_turn(self, user_input: str, ai_response: str, 
                           metadata: Optional[Dict] = None):
        """添加一轮对话"""
        # 添加到LangChain记忆
        self.memory.chat_memory.add_user_message(user_input)
        self.memory.chat_memory.add_ai_message(ai_response)
        
        # 更新上下文状态
        self._update_context(user_input, ai_response, metadata)
    
    def _update_context(self, user_input: str, ai_response: str, 
                       metadata: Optional[Dict] = None):
        """更新对话上下文"""
        # 提取SQL查询
        sql_queries = self._extract_sql_queries(ai_response)
        if sql_queries:
            self.context.sql_queries.extend(sql_queries)
            # 只保留最近的5个查询
            self.context.sql_queries = self.context.sql_queries[-5:]
        
        # 提取表名
        table_names = self._extract_table_names(user_input + " " + ai_response)
        if table_names:
            self.context.table_names.extend(table_names)
            self.context.table_names = list(set(self.context.table_names))
        
        # 更新任务类型
        self.context.current_task = self._identify_task_type(user_input)
        
        # 更新用户意图
        self.context.user_intent = self._analyze_user_intent(user_input)
        
        # 保存结果数据
        if metadata and 'results' in metadata:
            self.context.last_results = metadata['results']
        
        self.context.timestamp = datetime.now()
    
    def _extract_sql_queries(self, text: str) -> List[str]:
        """从文本中提取SQL查询"""
        # 匹配SQL查询的正则表达式
        sql_pattern = r'(SELECT|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER)\s+.*?(?=;|$|\n\n)'
        matches = re.findall(sql_pattern, text, re.IGNORECASE | re.DOTALL)
        
        sql_queries = []
        for match in matches:
            # 清理和格式化SQL
            sql = re.sub(r'\s+', ' ', match.strip())
            if len(sql) > 10:  # 过滤太短的匹配
                sql_queries.append(sql)
        
        return sql_queries
    
    def _extract_table_names(self, text: str) -> List[str]:
        """从文本中提取表名"""
        # 常见的表名模式
        table_patterns = [
            r'FROM\s+(\w+)',
            r'JOIN\s+(\w+)',
            r'UPDATE\s+(\w+)',
            r'INSERT\s+INTO\s+(\w+)',
            r'表\s*(\w+)',
            r'(\w+)表'
        ]
        
        table_names = []
        for pattern in table_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            table_names.extend(matches)
        
        # 过滤常见的非表名词汇
        excluded_words = {'select', 'where', 'order', 'group', 'having', 'limit'}
        return [name for name in set(table_names) if name.lower() not in excluded_words]
    
    def _identify_task_type(self, user_input: str) -> str:
        """识别任务类型"""
        task_keywords = {
            'query': ['查询', '查找', '搜索', 'select', 'find'],
            'analysis': ['分析', '统计', '计算', 'analyze', 'count', 'sum', 'avg'],
            'visualization': ['图表', '可视化', '画图', 'chart', 'plot', 'graph'],
            'optimization': ['优化', '性能', '慢', 'optimize', 'performance'],
            'quality': ['质量', '检查', '验证', 'quality', 'check', 'validate']
        }
        
        user_input_lower = user_input.lower()
        for task_type, keywords in task_keywords.items():
            if any(keyword in user_input_lower for keyword in keywords):
                return task_type
        
        return 'general'
    
    def _analyze_user_intent(self, user_input: str) -> str:
        """分析用户意图"""
        intent_patterns = {
            'execute_query': r'执行|运行|查询',
            'modify_query': r'修改|改|调整',
            'explain_result': r'解释|说明|为什么',
            'continue_task': r'继续|接下来|然后',
            'new_request': r'现在|新的|另外'
        }
        
        for intent, pattern in intent_patterns.items():
            if re.search(pattern, user_input):
                return intent
        
        return 'unknown'
    
    def resolve_references(self, user_input: str) -> str:
        """解析用户输入中的指代词"""
        resolved_input = user_input
        
        for pattern, reference_type in self.reference_patterns.items():
            if re.search(pattern, user_input):
                replacement = self._get_reference_replacement(reference_type)
                if replacement:
                    resolved_input = re.sub(pattern, replacement, resolved_input)
        
        return resolved_input
    
    def _get_reference_replacement(self, reference_type: str) -> Optional[str]:
        """获取指代词的具体内容"""
        if reference_type == 'last_sql_query' and self.context.sql_queries:
            return f"查询: {self.context.sql_queries[-1]}"
        
        elif reference_type == 'last_results' and self.context.last_results:
            return f"结果数据（{len(self.context.last_results)} 条记录）"
        
        elif reference_type == 'last_table' and self.context.table_names:
            return f"表 {self.context.table_names[-1]}"
        
        elif reference_type == 'previous_item':
            # 根据上下文返回最相关的项目
            if self.context.sql_queries:
                return f"之前的查询: {self.context.sql_queries[-1]}"
        
        return None
    
    def get_contextual_prompt(self, user_input: str) -> str:
        """生成包含上下文的提示词"""
        # 解析指代词
        resolved_input = self.resolve_references(user_input)
        
        # 构建上下文信息
        context_info = []
        
        if self.context.sql_queries:
            context_info.append(f"最近的SQL查询: {self.context.sql_queries[-1]}")
        
        if self.context.table_names:
            context_info.append(f"涉及的表: {', '.join(self.context.table_names)}")
        
        if self.context.current_task:
            context_info.append(f"当前任务类型: {self.context.current_task}")
        
        if self.context.last_results:
            result_count = len(self.context.last_results) if isinstance(self.context.last_results, list) else "未知"
            context_info.append(f"最近查询结果: {result_count} 条记录")
        
        # 组合提示词
        if context_info:
            context_str = "\n".join([f"- {info}" for info in context_info])
            prompt = f"""
当前对话上下文:
{context_str}

用户输入: {resolved_input}

请基于上述上下文理解用户意图并选择合适的工具执行任务。
"""
        else:
            prompt = f"用户输入: {resolved_input}"
        
        return prompt
    
    def get_conversation_summary(self) -> str:
        """获取对话摘要"""
        messages = self.memory.chat_memory.messages
        if not messages:
            return "暂无对话历史"
        
        summary = f"对话摘要 (最近 {len(messages)//2} 轮):\n"
        summary += f"当前任务: {self.context.current_task or '未知'}\n"
        summary += f"涉及表: {', '.join(self.context.table_names) if self.context.table_names else '无'}\n"
        summary += f"SQL查询数: {len(self.context.sql_queries)}\n"
        
        if self.context.sql_queries:
            summary += f"最新查询: {self.context.sql_queries[-1][:100]}...\n"
        
        return summary
    
    def clear_context(self):
        """清除上下文（保留记忆）"""
        self.context = ConversationContext(
            sql_queries=[],
            table_names=[],
            last_results=None,
            current_task=None,
            user_intent=None,
            timestamp=datetime.now()
        )
    
    def export_conversation(self) -> Dict:
        """导出对话数据"""
        return {
            "messages": [
                {
                    "type": msg.type,
                    "content": msg.content,
                    "timestamp": datetime.now().isoformat()
                }
                for msg in self.memory.chat_memory.messages
            ],
            "context": {
                "sql_queries": self.context.sql_queries,
                "table_names": self.context.table_names,
                "current_task": self.context.current_task,
                "user_intent": self.context.user_intent,
                "timestamp": self.context.timestamp.isoformat()
            }
        }

def demonstrate_advanced_memory():
    """演示高级记忆系统"""
    print("🧠 高级记忆系统演示")
    print("=" * 80)
    
    memory_system = AdvancedMemorySystem(window_size=8)
    
    # 模拟复杂的多轮对话
    conversation_turns = [
        {
            "user": "查询用户表中的所有数据",
            "ai": "我为您生成SQL查询: SELECT * FROM users;",
            "metadata": {"tool_used": "rag_sql_generator"}
        },
        {
            "user": "执行这个查询",
            "ai": "正在执行查询 SELECT * FROM users; 返回了150条用户记录",
            "metadata": {"tool_used": "sql_executor", "results": [{"id": 1, "name": "张三"}, {"id": 2, "name": "李四"}]}
        },
        {
            "user": "只要年龄大于25的",
            "ai": "修改查询为: SELECT * FROM users WHERE age > 25;",
            "metadata": {"tool_used": "rag_sql_generator"}
        },
        {
            "user": "分析这些结果",
            "ai": "正在分析年龄大于25的用户数据，发现平均年龄为32岁...",
            "metadata": {"tool_used": "data_analyzer"}
        },
        {
            "user": "创建年龄分布图",
            "ai": "正在创建年龄分布的柱状图...",
            "metadata": {"tool_used": "data_visualizer"}
        }
    ]
    
    print("📝 对话进程:")
    print("-" * 50)
    
    for i, turn in enumerate(conversation_turns, 1):
        print(f"\n第 {i} 轮:")
        print(f"用户: {turn['user']}")
        
        # 展示指代词解析
        resolved = memory_system.resolve_references(turn['user'])
        if resolved != turn['user']:
            print(f"解析后: {resolved}")
        
        # 生成上下文提示词
        contextual_prompt = memory_system.get_contextual_prompt(turn['user'])
        print(f"上下文提示词: {contextual_prompt[:100]}...")
        
        print(f"助手: {turn['ai']}")
        
        # 添加到记忆系统
        memory_system.add_conversation_turn(
            turn['user'], 
            turn['ai'], 
            turn.get('metadata')
        )
        
        # 显示当前上下文状态
        print(f"📊 上下文状态:")
        print(f"  SQL查询数: {len(memory_system.context.sql_queries)}")
        print(f"  涉及表: {memory_system.context.table_names}")
        print(f"  当前任务: {memory_system.context.current_task}")
        print(f"  用户意图: {memory_system.context.user_intent}")
    
    print(f"\n📋 最终对话摘要:")
    print(memory_system.get_conversation_summary())

if __name__ == "__main__":
    demonstrate_advanced_memory()
