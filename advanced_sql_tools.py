#!/usr/bin/env python3
"""
高级SQL工具集
为SQL Agent提供更多专业功能
"""

import os
import json
import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import io
import base64

from langchain.tools import BaseTool, tool
from pydantic import BaseModel, Field

class QueryOptimizationInput(BaseModel):
    """查询优化工具输入"""
    sql_query: str = Field(description="需要优化的SQL查询")
    database_path: str = Field(description="数据库路径", default="database.db")

class DataVisualizationInput(BaseModel):
    """数据可视化工具输入"""
    sql_query: str = Field(description="用于获取数据的SQL查询")
    chart_type: str = Field(description="图表类型：bar, line, pie, scatter, heatmap")
    database_path: str = Field(description="数据库路径", default="database.db")

class DataQualityInput(BaseModel):
    """数据质量检查工具输入"""
    table_name: str = Field(description="要检查的表名")
    database_path: str = Field(description="数据库路径", default="database.db")

class SQLValidationInput(BaseModel):
    """SQL验证工具输入"""
    sql_query: str = Field(description="要验证的SQL查询")
    database_path: str = Field(description="数据库路径", default="database.db")

class PerformanceAnalysisInput(BaseModel):
    """性能分析工具输入"""
    sql_query: str = Field(description="要分析性能的SQL查询")
    database_path: str = Field(description="数据库路径", default="database.db")

class AdvancedSQLTools:
    """高级SQL工具集合"""
    
    def __init__(self, llm=None):
        self.llm = llm
        
    @tool("query_optimizer", args_schema=QueryOptimizationInput)
    def optimize_query(sql_query: str, database_path: str = "database.db") -> str:
        """
        分析和优化SQL查询性能
        提供索引建议和查询重写建议
        """
        try:
            conn = sqlite3.connect(database_path)
            cursor = conn.cursor()
            
            # 获取查询执行计划
            explain_query = f"EXPLAIN QUERY PLAN {sql_query}"
            cursor.execute(explain_query)
            plan = cursor.fetchall()
            
            # 分析查询
            analysis = {
                "original_query": sql_query,
                "execution_plan": plan,
                "optimization_suggestions": []
            }
            
            # 检查是否使用了索引
            plan_text = " ".join([str(row) for row in plan])
            if "SCAN" in plan_text.upper():
                analysis["optimization_suggestions"].append(
                    "检测到表扫描操作，建议在WHERE子句中的列上创建索引"
                )
            
            # 检查JOIN操作
            if "JOIN" in sql_query.upper():
                analysis["optimization_suggestions"].append(
                    "对于JOIN操作，确保连接列上有索引以提高性能"
                )
            
            # 检查ORDER BY
            if "ORDER BY" in sql_query.upper():
                analysis["optimization_suggestions"].append(
                    "ORDER BY操作可能需要排序，考虑在排序列上创建索引"
                )
            
            conn.close()
            
            result = f"查询优化分析：\n"
            result += f"原始查询：{sql_query}\n\n"
            result += f"执行计划：\n"
            for step in plan:
                result += f"  {step}\n"
            
            if analysis["optimization_suggestions"]:
                result += f"\n优化建议：\n"
                for suggestion in analysis["optimization_suggestions"]:
                    result += f"  • {suggestion}\n"
            else:
                result += f"\n✓ 查询已经较为优化"
            
            return result
            
        except Exception as e:
            return f"查询优化分析失败: {str(e)}"
    
    @tool("data_visualizer", args_schema=DataVisualizationInput)
    def create_visualization(sql_query: str, chart_type: str, database_path: str = "database.db") -> str:
        """
        根据SQL查询结果创建数据可视化图表
        支持多种图表类型
        """
        try:
            # 执行查询获取数据
            conn = sqlite3.connect(database_path)
            df = pd.read_sql_query(sql_query, conn)
            conn.close()
            
            if df.empty:
                return "查询结果为空，无法创建图表"
            
            # 设置图表样式
            plt.style.use('seaborn-v0_8')
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # 根据图表类型创建可视化
            if chart_type.lower() == "bar":
                if len(df.columns) >= 2:
                    df.plot(x=df.columns[0], y=df.columns[1], kind='bar', ax=ax)
                    ax.set_title(f"{df.columns[1]} by {df.columns[0]}")
                else:
                    return "柱状图需要至少两列数据"
                    
            elif chart_type.lower() == "line":
                if len(df.columns) >= 2:
                    df.plot(x=df.columns[0], y=df.columns[1], kind='line', ax=ax)
                    ax.set_title(f"{df.columns[1]} 趋势图")
                else:
                    return "折线图需要至少两列数据"
                    
            elif chart_type.lower() == "pie":
                if len(df.columns) >= 2:
                    df.set_index(df.columns[0])[df.columns[1]].plot(kind='pie', ax=ax)
                    ax.set_title(f"{df.columns[1]} 分布")
                else:
                    return "饼图需要至少两列数据"
                    
            elif chart_type.lower() == "scatter":
                if len(df.columns) >= 2:
                    df.plot(x=df.columns[0], y=df.columns[1], kind='scatter', ax=ax)
                    ax.set_title(f"{df.columns[0]} vs {df.columns[1]}")
                else:
                    return "散点图需要至少两列数据"
                    
            elif chart_type.lower() == "heatmap":
                # 创建相关性热力图
                numeric_df = df.select_dtypes(include=['number'])
                if not numeric_df.empty:
                    correlation = numeric_df.corr()
                    sns.heatmap(correlation, annot=True, cmap='coolwarm', ax=ax)
                    ax.set_title("数据相关性热力图")
                else:
                    return "热力图需要数值型数据"
            else:
                return f"不支持的图表类型: {chart_type}"
            
            plt.tight_layout()
            
            # 保存图表
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"chart_{chart_type}_{timestamp}.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            plt.close()
            
            return f"图表已创建并保存为: {filename}\n数据概览:\n{df.describe()}"
            
        except Exception as e:
            return f"创建可视化失败: {str(e)}"
    
    @tool("data_quality_checker", args_schema=DataQualityInput)
    def check_data_quality(table_name: str, database_path: str = "database.db") -> str:
        """
        检查数据质量问题
        包括空值、重复值、异常值等
        """
        try:
            conn = sqlite3.connect(database_path)
            
            # 获取表的基本信息
            df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
            
            if df.empty:
                return f"表 {table_name} 为空"
            
            quality_report = f"数据质量报告 - {table_name}\n"
            quality_report += "=" * 50 + "\n\n"
            
            # 基本统计
            quality_report += f"总记录数: {len(df)}\n"
            quality_report += f"总字段数: {len(df.columns)}\n\n"
            
            # 空值检查
            null_counts = df.isnull().sum()
            if null_counts.any():
                quality_report += "空值统计:\n"
                for col, count in null_counts.items():
                    if count > 0:
                        percentage = (count / len(df)) * 100
                        quality_report += f"  {col}: {count} ({percentage:.1f}%)\n"
            else:
                quality_report += "✓ 没有发现空值\n"
            
            quality_report += "\n"
            
            # 重复值检查
            duplicate_count = df.duplicated().sum()
            if duplicate_count > 0:
                quality_report += f"⚠ 发现 {duplicate_count} 条重复记录\n"
            else:
                quality_report += "✓ 没有发现重复记录\n"
            
            quality_report += "\n"
            
            # 数值字段的异常值检查
            numeric_columns = df.select_dtypes(include=['number']).columns
            if len(numeric_columns) > 0:
                quality_report += "数值字段统计:\n"
                for col in numeric_columns:
                    q1 = df[col].quantile(0.25)
                    q3 = df[col].quantile(0.75)
                    iqr = q3 - q1
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    
                    outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
                    
                    quality_report += f"  {col}:\n"
                    quality_report += f"    范围: {df[col].min()} - {df[col].max()}\n"
                    quality_report += f"    平均值: {df[col].mean():.2f}\n"
                    quality_report += f"    异常值: {len(outliers)} 个\n"
            
            conn.close()
            return quality_report
            
        except Exception as e:
            return f"数据质量检查失败: {str(e)}"
    
    @tool("sql_validator", args_schema=SQLValidationInput)
    def validate_sql(sql_query: str, database_path: str = "database.db") -> str:
        """
        验证SQL语句的语法和安全性
        检查潜在的安全风险
        """
        try:
            validation_result = "SQL验证结果:\n"
            validation_result += "=" * 30 + "\n\n"
            
            # 语法检查
            try:
                conn = sqlite3.connect(database_path)
                cursor = conn.cursor()
                
                # 使用EXPLAIN来检查语法
                cursor.execute(f"EXPLAIN {sql_query}")
                validation_result += "✓ 语法检查通过\n"
                
            except sqlite3.Error as e:
                validation_result += f"✗ 语法错误: {str(e)}\n"
                return validation_result
            
            # 安全性检查
            dangerous_keywords = [
                'DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 
                'INSERT', 'UPDATE', 'EXEC', 'EXECUTE'
            ]
            
            sql_upper = sql_query.upper()
            found_dangerous = [kw for kw in dangerous_keywords if kw in sql_upper]
            
            if found_dangerous:
                validation_result += f"⚠ 检测到潜在危险操作: {', '.join(found_dangerous)}\n"
                validation_result += "  请确认这些操作是必要的\n"
            else:
                validation_result += "✓ 安全性检查通过\n"
            
            # 性能建议
            if "SELECT *" in sql_upper:
                validation_result += "💡 建议: 避免使用 SELECT *，明确指定需要的列\n"
            
            if "WHERE" not in sql_upper and "SELECT" in sql_upper:
                validation_result += "💡 建议: 考虑添加 WHERE 条件以限制结果集\n"
            
            if "LIMIT" not in sql_upper and "SELECT" in sql_upper:
                validation_result += "💡 建议: 对于大表查询，考虑添加 LIMIT 限制\n"
            
            conn.close()
            return validation_result
            
        except Exception as e:
            return f"SQL验证失败: {str(e)}"
    
    @tool("performance_analyzer", args_schema=PerformanceAnalysisInput)
    def analyze_performance(sql_query: str, database_path: str = "database.db") -> str:
        """
        分析SQL查询的性能表现
        测量执行时间和资源使用
        """
        try:
            import time
            
            conn = sqlite3.connect(database_path)
            cursor = conn.cursor()
            
            performance_report = "性能分析报告:\n"
            performance_report += "=" * 30 + "\n\n"
            
            # 执行时间测试
            start_time = time.time()
            cursor.execute(sql_query)
            results = cursor.fetchall()
            end_time = time.time()
            
            execution_time = end_time - start_time
            
            performance_report += f"查询语句: {sql_query}\n\n"
            performance_report += f"执行时间: {execution_time:.4f} 秒\n"
            performance_report += f"返回记录数: {len(results)}\n"
            
            # 性能评级
            if execution_time < 0.1:
                performance_report += "性能评级: 优秀 ⭐⭐⭐⭐⭐\n"
            elif execution_time < 0.5:
                performance_report += "性能评级: 良好 ⭐⭐⭐⭐\n"
            elif execution_time < 1.0:
                performance_report += "性能评级: 一般 ⭐⭐⭐\n"
            elif execution_time < 2.0:
                performance_report += "性能评级: 较慢 ⭐⭐\n"
            else:
                performance_report += "性能评级: 需要优化 ⭐\n"
            
            # 获取执行计划
            cursor.execute(f"EXPLAIN QUERY PLAN {sql_query}")
            plan = cursor.fetchall()
            
            performance_report += "\n执行计划:\n"
            for step in plan:
                performance_report += f"  {step}\n"
            
            # 性能建议
            performance_report += "\n性能建议:\n"
            if execution_time > 1.0:
                performance_report += "  • 查询执行时间较长，建议优化\n"
                performance_report += "  • 检查是否需要添加索引\n"
                performance_report += "  • 考虑重写查询逻辑\n"
            
            if len(results) > 1000:
                performance_report += "  • 返回记录数较多，考虑添加分页\n"
            
            conn.close()
            return performance_report
            
        except Exception as e:
            return f"性能分析失败: {str(e)}"

def get_advanced_tools(llm=None) -> List:
    """获取所有高级工具"""
    tools_instance = AdvancedSQLTools(llm)
    
    return [
        tools_instance.optimize_query,
        tools_instance.create_visualization,
        tools_instance.check_data_quality,
        tools_instance.validate_sql,
        tools_instance.analyze_performance
    ]
