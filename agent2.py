"""
CodeAgent - 基于 smolagents 的智能代码编写助手

"""

import os
import json
import time
import hashlib
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from smolagents import CodeAgent, Tool, LiteLLMModel
from smolagents import PythonInterpreterTool

class MemoryManager:
    """简化的记忆管理系统"""
    
    def __init__(self, memory_dir: str = ".codeagent_memory"):
        self.memory_dir = Path(memory_dir)
        self.memory_dir.mkdir(exist_ok=True)
        
        self.conversation_file = self.memory_dir / "conversations.json"
        self.knowledge_file = self.memory_dir / "knowledge_base.json"
        
        self._init_memory_files()
    
    def _init_memory_files(self):
        """初始化记忆文件"""
        if not self.conversation_file.exists():
            self._save_json(self.conversation_file, {"conversations": []})
        
        if not self.knowledge_file.exists():
            self._save_json(self.knowledge_file, {
                "user_preferences": {},
                "code_patterns": {}
            })
    
    def _save_json(self, file_path: Path, data: Dict):
        """保存 JSON 数据"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def _load_json(self, file_path: Path) -> Dict:
        """加载 JSON 数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}
    
    def store_conversation(self, user_input: str, agent_response: str):
        """存储对话记录"""
        conversations = self._load_json(self.conversation_file)
        
        #构造对话记录
        conversation_entry = {
            "timestamp": datetime.now().isoformat(),#时间戳
            "user_input": user_input,#用户输入
            "agent_response": agent_response#保存agent给的响应
        }
        
        conversations["conversations"].append(conversation_entry)
        
        # 保持最近 50 条对话
        if len(conversations["conversations"]) > 50:
            conversations["conversations"] = conversations["conversations"][-50:]
        
        self._save_json(self.conversation_file, conversations)
    
    def store_knowledge(self, category: str, key: str, value: Any):
        """存储知识"""
        knowledge = self._load_json(self.knowledge_file)
        
        if category not in knowledge:
            knowledge[category] = {}
        
        knowledge[category][key] = {
            "value": value,
            "timestamp": datetime.now().isoformat()
        }
        
        self._save_json(self.knowledge_file, knowledge)
    
    def retrieve_knowledge(self, category: str, key: str = None) -> Any:
        """检索知识"""
        knowledge = self._load_json(self.knowledge_file)
        
        if category not in knowledge:
            return None
        
        if key is None:
            return knowledge[category]
        
        return knowledge[category].get(key, {}).get("value")
    
    def search_conversations(self, query: str, limit: int = 3) -> List[Dict]:
        """搜索相关对话"""
        conversations = self._load_json(self.conversation_file)
        
        relevant_conversations = []
        query_lower = query.lower()
        
        for conv in conversations.get("conversations", []):
            if (query_lower in conv["user_input"].lower() or 
                query_lower in conv["agent_response"].lower()):
                relevant_conversations.append(conv)
        
        return relevant_conversations[-limit:]

class CodeGenerationTool(Tool):
    """代码生成工具"""

    name = "code_generation"
    description = "根据自然语言描述生成代码"
    inputs = {
        "description": {
            "type": "string",
            "description": "代码功能的自然语言描述"
        },
        "language": {
            "type": "string",
            "description": "编程语言",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, memory_manager: MemoryManager):
        super().__init__()
        self.memory_manager = memory_manager

    def forward(self, description: str, language: str = "python") -> str:
        """生成代码"""
        # 搜索相关历史经验
        similar_conversations = self.memory_manager.search_conversations(description, limit=2)
        
        # 获取用户偏好
        user_preferences = self.memory_manager.retrieve_knowledge("user_preferences") or {}
        
        # 构建提示
        prompt = f"""
基于以下描述生成 {language} 代码：

需求：{description}
用户偏好：{json.dumps(user_preferences, ensure_ascii=False)}
相关历史：{json.dumps(similar_conversations, ensure_ascii=False)}

请生成清晰、可读的代码。
"""
        
        # 存储生成的代码模式
        self.memory_manager.store_knowledge(
            "code_patterns",
            f"{language}_{hashlib.md5(description.encode()).hexdigest()[:8]}",
            {"description": description, "language": language}
        )
        
        return f"# 生成的 {language} 代码\n# 需求：{description}\n\ndef generated_function():\n    pass"

class FileOperationTool(Tool):
    """文件操作工具"""

    name = "file_operation"
    description = "执行文件操作：创建、读取、写入、删除文件"
    inputs = {
        "operation": {
            "type": "string",
            "description": "操作类型：create, read, write, delete"
        },
        "file_path": {
            "type": "string",
            "description": "文件路径"
        },
        "content": {
            "type": "string",
            "description": "文件内容（用于 write 操作）",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, memory_manager: MemoryManager):
        super().__init__()
        self.memory_manager = memory_manager

    def forward(self, operation: str, file_path: str, content: str = "") -> str:
        """执行文件操作"""
        try:
            path = Path(file_path)

            if operation == "create":
                path.parent.mkdir(parents=True, exist_ok=True)
                path.touch()
                return f"文件 {file_path} 创建成功"

            elif operation == "read":
                if not path.exists():
                    return f"错误：文件 {file_path} 不存在"
                
                with open(path, 'r', encoding='utf-8') as f:
                    return f.read()

            elif operation == "write":
                path.parent.mkdir(parents=True, exist_ok=True)
                with open(path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return f"内容已写入文件 {file_path}"

            elif operation == "delete":
                if path.exists():
                    path.unlink()
                    return f"文件 {file_path} 删除成功"
                else:
                    return f"错误：文件 {file_path} 不存在"

            else:
                return f"不支持的操作：{operation}"

        except Exception as e:
            return f"文件操作失败：{str(e)}"

class CommandExecutionTool(Tool):
    """命令执行工具"""

    name = "command_execution"
    description = "安全执行系统命令"
    inputs = {
        "command": {
            "type": "string",
            "description": "要执行的命令"
        },
        "timeout": {
            "type": "number",
            "description": "超时时间（秒）",
            "nullable": True
        }
    }
    output_type = "string"

    def __init__(self, memory_manager: MemoryManager):
        super().__init__()
        self.memory_manager = memory_manager
        self.dangerous_commands = ["rm -rf", "del /f", "format", "shutdown", "reboot"]

    def _is_safe_command(self, command: str) -> bool:
        """检查命令是否安全"""
        command_lower = command.lower()
        return not any(dangerous in command_lower for dangerous in self.dangerous_commands)

    def forward(self, command: str, timeout: float = 30.0) -> str:
        """执行命令"""
        if not self._is_safe_command(command):
            return f"危险命令被阻止：{command}"

        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout
            )

            output = f"命令执行完成\n返回码: {result.returncode}\n"
            if result.stdout:
                output += f"输出:\n{result.stdout}\n"
            if result.stderr:
                output += f"错误:\n{result.stderr}\n"

            return output

        except subprocess.TimeoutExpired:
            return f"命令执行超时（{timeout}秒）：{command}"
        except Exception as e:
            return f"命令执行失败：{str(e)}"

class TaskDecompositionTool(Tool):
    """任务分解工具"""

    name = "task_decomposition"
    description = "将复杂任务分解为子任务"
    inputs = {
        "task_description": {
            "type": "string",
            "description": "复杂任务的描述"
        }
    }
    output_type = "string"

    def __init__(self, memory_manager: MemoryManager):
        super().__init__()
        self.memory_manager = memory_manager

    def forward(self, task_description: str) -> str:
        """分解任务"""
        # 基于关键词的简单任务分解
        task_lower = task_description.lower()
        
        if "web" in task_lower or "网站" in task_lower:
            subtasks = [
                "1. 创建项目结构",
                "2. 编写前端代码",
                "3. 实现后端逻辑",
                "4. 测试和部署"
            ]
        elif "api" in task_lower:
            subtasks = [
                "1. 设计API接口",
                "2. 实现API端点",
                "3. 添加文档"
            ]
        elif "数据分析" in task_lower:
            subtasks = [
                "1. 数据收集和加载",
                "2. 数据清洗",
                "3. 数据分析和可视化",
                "4. 生成报告"
            ]
        else:
            subtasks = [
                "1. 需求分析",
                "2. 代码实现",
                "3. 测试验证"
            ]
        
        result = f"任务分解：{task_description}\n\n子任务：\n" + "\n".join(subtasks)
        
        # 存储任务分解
        self.memory_manager.store_knowledge(
            "task_decompositions",
            hashlib.md5(task_description.encode()).hexdigest()[:8],
            {"task": task_description, "subtasks": subtasks}
        )
        
        return result

class IntelligentCodeAgent:
    """简化的智能代码助手"""

    def __init__(self, api_key: str = None, model_name: str = "deepseek-chat"):
        """初始化 CodeAgent"""
        self.memory_manager = MemoryManager()
        
        # 初始化工具
        self.tools = [
            PythonInterpreterTool(),
            CodeGenerationTool(self.memory_manager),
            FileOperationTool(self.memory_manager),
            CommandExecutionTool(self.memory_manager),
            TaskDecompositionTool(self.memory_manager)
        ]

        # 初始化模型
        self.model = self._setup_model(api_key, model_name)
        
        # 创建 agent
        self.agent = CodeAgent(
            tools=self.tools,
            model=self.model,
            stream_outputs=True
        )

        print("🤖 智能代码助手已初始化完成！")

    def _setup_model(self, api_key: str, model_name: str):
        """设置模型"""
        try:
            model = LiteLLMModel(
                model_id=model_name,
                api_key=api_key or os.environ.get("DEEPSEEK_API_KEY"),
                api_base="https://api.deepseek.com/v1",
                temperature=0.1,
                max_tokens=4000
            )
            return model
        except Exception as e:
            print(f"⚠️ 模型初始化失败，使用默认配置: {e}")
            from smolagents import InferenceClientModel
            return InferenceClientModel(
                model_id="Qwen/Qwen2.5-Coder-32B-Instruct",
                provider="auto"
            )

    def run(self, user_input: str) -> str:
        """运行用户请求"""
        try:
            start_time = time.time()
            
            # 搜索相关历史记录
            relevant_history = self.memory_manager.search_conversations(user_input, limit=2)
            
            # 构建增强输入
            enhanced_input = self._enhance_input(user_input, relevant_history)
            
            # 执行任务
            result = self.agent.run(enhanced_input)
            
            # 存储对话记录
            self.memory_manager.store_conversation(user_input, result)
            
            # 学习用户偏好
            self._learn_from_interaction(user_input)
            
            print(f"⏱️ 执行时间: {time.time() - start_time:.2f}秒")
            
            return result

        except Exception as e:
            error_msg = f"执行失败: {str(e)}"
            print(error_msg)
            return error_msg

    def _enhance_input(self, user_input: str, relevant_history: List[Dict]) -> str:
        """增强用户输入"""
        enhanced = "你是一个专业的代码编写助手。\n\n"
        
        if relevant_history:
            enhanced += "相关历史记录:\n"
            for conv in relevant_history:
                enhanced += f"- {conv['user_input'][:100]}...\n"
        
        # 获取用户偏好
        user_prefs = self.memory_manager.retrieve_knowledge("user_preferences") or {}
        if user_prefs:
            enhanced += f"用户偏好: {json.dumps(user_prefs, ensure_ascii=False)}\n\n"
        
        enhanced += f"当前任务: {user_input}\n\n请高效完成用户的请求。"
        
        return enhanced

    def _learn_from_interaction(self, user_input: str):
        """从交互中学习"""
        input_lower = user_input.lower()
        
        # 编程语言偏好
        languages = ["python", "javascript", "java", "cpp", "go", "rust"]
        for lang in languages:
            if lang in input_lower:
                self.memory_manager.store_knowledge("user_preferences", "preferred_language", lang)
                break
        
        # 代码风格偏好
        if "注释" in user_input or "comment" in input_lower:
            self.memory_manager.store_knowledge("user_preferences", "wants_comments", True)
        
        if "测试" in user_input or "test" in input_lower:
            self.memory_manager.store_knowledge("user_preferences", "wants_tests", True)

    def get_status(self) -> Dict:
        """获取助手状态"""
        conversations = self.memory_manager._load_json(self.memory_manager.conversation_file)
        
        return {
            "conversations_count": len(conversations.get("conversations", [])),
            "tools_count": len(self.tools),
            "memory_dir": str(self.memory_manager.memory_dir)
        }

    def clear_memory(self, confirm: bool = False):
        """清除记忆"""
        if not confirm:
            print("⚠️ 请使用 clear_memory(confirm=True) 确认清除记忆")
            return
        
        import shutil
        if self.memory_manager.memory_dir.exists():
            shutil.rmtree(self.memory_manager.memory_dir)
            self.memory_manager._init_memory_files()
            print("🗑️ 记忆数据已清除")

def demo_code_agent():
    """演示 CodeAgent 的使用"""
    print("🚀 CodeAgent 演示开始\n")
    
    agent = IntelligentCodeAgent()
    
    # 显示状态
    status = agent.get_status()
    print(f"📊 助手状态: {json.dumps(status, ensure_ascii=False, indent=2)}\n")
    
    # 示例任务
    demo_tasks = [
        "创建一个简单的 Python 函数来计算斐波那契数列",
        "分析当前目录的文件结构",
        "生成一个简单的 Web API 项目结构"
    ]
    
    for i, task in enumerate(demo_tasks, 1):
        print(f"🎯 任务 {i}: {task}")
        try:
            result = agent.run(task)
            print(f"✅ 结果: {result[:200]}...\n")
        except Exception as e:
            print(f"❌ 错误: {e}\n")
    
    print("🎉 演示完成！")

if __name__ == "__main__":
    demo_code_agent()
