#!/usr/bin/env python3
"""
完整的SQL Agent系统
集成RAG、工具调用、对话记忆等功能
"""

import os
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_community.vectorstores import Chroma
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.tools import BaseTool, tool
from langchain.schema import Document
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import CharacterTextSplitter
from langchain.prompts import ChatPromptTemplate
from langchain.memory import ConversationBufferWindowMemory
from pydantic import BaseModel, Field

# 导入我们的高级工具
from advanced_sql_tools import get_advanced_tools
from nl2sql_rag_system import NL2SQLSystem

# 设置OpenAI API密钥
os.environ["OPENAI_API_KEY"] = "your-api-key"

class CompleteSQLAgent:
    """
    完整的SQL Agent系统
    
    功能特性：
    1. 基于RAG的SQL生成
    2. SQL执行和验证
    3. 数据质量检查
    4. 性能优化建议
    5. 数据可视化
    6. 对话记忆
    7. 多轮交互
    """
    
    def __init__(self, 
                 examples_path: str = "sql_examples.txt",
                 db_schema_path: Optional[str] = None,
                 ddl_path: Optional[str] = None,
                 db_docs_path: Optional[str] = None,
                 vector_db_path: str = "./complete_agent_chroma_db",
                 model_name: str = "gpt-3.5-turbo",
                 temperature: float = 0,
                 memory_window: int = 10):
        
        self.examples_path = examples_path
        self.db_schema_path = db_schema_path
        self.ddl_path = ddl_path
        self.db_docs_path = db_docs_path
        self.vector_db_path = vector_db_path
        self.model_name = model_name
        self.temperature = temperature
        self.memory_window = memory_window
        
        # 初始化组件
        self.llm = None
        self.rag_system = None
        self.agent_executor = None
        self.memory = None
        self.tools = []
        
        # 初始化系统
        self._initialize_system()
    
    def _initialize_system(self):
        """初始化完整系统"""
        print("🚀 初始化完整SQL Agent系统...")
        
        # 1. 初始化LLM
        self.llm = ChatOpenAI(
            temperature=self.temperature, 
            model_name=self.model_name
        )
        
        # 2. 初始化RAG系统
        try:
            self.rag_system = NL2SQLSystem(
                examples_path=self.examples_path,
                db_schema_path=self.db_schema_path,
                ddl_path=self.ddl_path,
                db_docs_path=self.db_docs_path,
                vector_db_path=self.vector_db_path,
                model_name=self.model_name,
                temperature=self.temperature
            )
            print("✅ RAG系统初始化成功")
        except Exception as e:
            print(f"⚠️ RAG系统初始化失败: {e}")
            self.rag_system = None
        
        # 3. 初始化记忆
        self.memory = ConversationBufferWindowMemory(
            k=self.memory_window,
            memory_key="chat_history",
            return_messages=True
        )
        
        # 4. 创建工具
        self._create_tools()
        
        # 5. 创建Agent
        self._create_agent()
        
        print("🎉 SQL Agent系统初始化完成!")
    
    def _create_tools(self):
        """创建所有工具"""
        
        # 基础工具
        @tool("rag_sql_generator")
        def generate_sql_with_rag(natural_language_query: str) -> str:
            """
            使用RAG系统生成SQL查询
            基于向量检索的示例和文档
            """
            if not self.rag_system:
                return "RAG系统未初始化，无法生成SQL"
            
            try:
                sql = self.rag_system.natural_language_to_sql(natural_language_query)
                return f"生成的SQL查询：\n{sql}"
            except Exception as e:
                return f"SQL生成失败: {str(e)}"
        
        @tool("conversation_summary")
        def get_conversation_summary() -> str:
            """
            获取当前对话的摘要
            帮助理解上下文
            """
            try:
                if self.memory and self.memory.chat_memory.messages:
                    messages = self.memory.chat_memory.messages
                    summary = "对话摘要：\n"
                    
                    for i, msg in enumerate(messages[-6:]):  # 最近3轮对话
                        role = "用户" if msg.type == "human" else "助手"
                        content = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
                        summary += f"{role}: {content}\n"
                    
                    return summary
                else:
                    return "暂无对话历史"
            except Exception as e:
                return f"获取对话摘要失败: {str(e)}"
        
        @tool("help_and_capabilities")
        def show_capabilities() -> str:
            """
            显示Agent的功能和使用帮助
            """
            help_text = """
            🤖 SQL Agent 功能介绍
            
            📊 核心功能：
            • SQL生成：将自然语言转换为SQL查询
            • SQL执行：安全执行SQL语句
            • 数据分析：对查询结果进行深入分析
            • 数据可视化：创建各种类型的图表
            • 性能优化：分析和优化查询性能
            • 数据质量检查：检测数据质量问题
            • SQL验证：验证语法和安全性
            
            🛠️ 可用工具：
            1. rag_sql_generator - 基于RAG的SQL生成
            2. sql_executor - SQL执行器
            3. query_optimizer - 查询优化器
            4. data_visualizer - 数据可视化
            5. data_quality_checker - 数据质量检查
            6. sql_validator - SQL验证器
            7. performance_analyzer - 性能分析器
            8. database_schema_inspector - 数据库结构查看
            9. data_analyzer - 数据分析器
            
            💡 使用示例：
            • "查询所有用户信息"
            • "分析用户年龄分布"
            • "创建销售趋势图表"
            • "检查订单表的数据质量"
            • "优化这个查询的性能"
            """
            return help_text
        
        # 组合所有工具
        basic_tools = [
            generate_sql_with_rag,
            get_conversation_summary,
            show_capabilities
        ]
        
        # 获取高级工具
        advanced_tools = get_advanced_tools(self.llm)
        
        # 如果RAG系统可用，添加其工具
        if self.rag_system:
            # 包装RAG系统的方法为工具
            @tool("sql_executor")
            def execute_sql(sql_query: str, database_path: str = "database.db") -> str:
                """执行SQL查询并返回结果"""
                try:
                    results = self.rag_system.execute_sql(sql_query, database_path)
                    if results:
                        return f"查询成功，返回 {len(results)} 条记录：\n{json.dumps(results, ensure_ascii=False, indent=2)}"
                    else:
                        return "查询成功，但没有返回结果"
                except Exception as e:
                    return f"SQL执行失败: {str(e)}"
            
            @tool("database_schema_inspector")
            def inspect_schema() -> str:
                """查看数据库结构"""
                try:
                    schema_str = self.rag_system._get_db_schema_str()
                    return f"数据库结构：\n{schema_str}"
                except Exception as e:
                    return f"查看数据库结构失败: {str(e)}"
            
            basic_tools.extend([execute_sql, inspect_schema])
        
        # 合并所有工具
        self.tools = basic_tools + advanced_tools
        
        print(f"📦 已加载 {len(self.tools)} 个工具")
    
    def _create_agent(self):
        """创建Agent执行器"""
        
        # 定义系统提示词
        system_prompt = """
        你是一个专业的SQL数据库助手Agent，名为"SQL Master"。你具备以下核心能力：
        
        🎯 **主要职责**：
        1. **智能SQL生成**：将自然语言准确转换为SQL查询
        2. **数据库管理**：执行查询、检查结构、验证安全性
        3. **数据分析**：深入分析查询结果，提供业务洞察
        4. **性能优化**：识别性能瓶颈，提供优化建议
        5. **数据可视化**：创建图表帮助理解数据
        6. **质量保证**：检查数据质量，确保数据可靠性
        
        🧠 **工作方式**：
        - 理解用户需求，选择最合适的工具
        - 提供清晰、准确的分析结果
        - 主动提供优化建议和最佳实践
        - 确保所有操作的安全性
        - 记住对话上下文，提供连贯的服务
        
        ⚡ **交互原则**：
        - 友好专业，易于理解
        - 主动询问不明确的需求
        - 提供具体可行的建议
        - 解释复杂的技术概念
        - 确保用户理解每个步骤
        
        🔒 **安全准则**：
        - 验证所有SQL语句的安全性
        - 警告潜在的危险操作
        - 建议使用最佳实践
        - 保护数据隐私和完整性
        
        开始对话时，请简要介绍你的能力，并询问用户需要什么帮助。
        """
        
        # 创建提示词模板
        prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            ("placeholder", "{chat_history}"),
            ("human", "{input}"),
            ("placeholder", "{agent_scratchpad}")
        ])
        
        # 创建Agent
        agent = create_openai_tools_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )
        
        # 创建Agent执行器
        self.agent_executor = AgentExecutor(
            agent=agent,
            tools=self.tools,
            memory=self.memory,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=5,
            early_stopping_method="generate"
        )
    
    def chat(self, user_input: str) -> str:
        """
        与Agent对话
        """
        try:
            response = self.agent_executor.invoke({
                "input": user_input,
                "chat_history": self.memory.chat_memory.messages
            })
            return response["output"]
        except Exception as e:
            return f"处理请求时出错: {str(e)}"
    
    def get_tools_info(self) -> Dict[str, str]:
        """获取工具信息"""
        tools_info = {}
        for tool in self.tools:
            tools_info[tool.name] = tool.description
        return tools_info
    
    def reset_conversation(self):
        """重置对话历史"""
        self.memory.clear()
        print("🔄 对话历史已重置")
    
    def save_conversation(self, filename: str = None):
        """保存对话历史"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"conversation_{timestamp}.json"
        
        try:
            messages = []
            for msg in self.memory.chat_memory.messages:
                messages.append({
                    "type": msg.type,
                    "content": msg.content,
                    "timestamp": datetime.now().isoformat()
                })
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(messages, f, ensure_ascii=False, indent=2)
            
            return f"💾 对话已保存到: {filename}"
        except Exception as e:
            return f"保存对话失败: {str(e)}"

# 交互式聊天界面
def interactive_chat():
    """启动交互式聊天"""
    print("🤖 SQL Master Agent 启动中...")
    print("=" * 60)
    
    # 初始化Agent
    try:
        agent = CompleteSQLAgent(
            examples_path="sql_examples.txt",
            db_schema_path="db_schema.json",
            ddl_path="ddl_statements.sql",
            db_docs_path="database_documentation.md"
        )
    except Exception as e:
        print(f"❌ Agent初始化失败: {e}")
        return
    
    print("\n🎉 SQL Master Agent 已就绪!")
    print("💡 输入 'help' 查看功能，输入 'quit' 退出")
    print("=" * 60)
    
    # 开始对话
    while True:
        try:
            user_input = input("\n👤 您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            elif user_input.lower() == 'reset':
                agent.reset_conversation()
                continue
            elif user_input.lower() == 'save':
                result = agent.save_conversation()
                print(f"💾 {result}")
                continue
            elif not user_input:
                continue
            
            print("\n🤖 SQL Master: ", end="")
            response = agent.chat(user_input)
            print(response)
            
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")

if __name__ == "__main__":
    interactive_chat()
