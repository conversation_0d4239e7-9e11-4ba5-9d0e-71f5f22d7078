"""
Agent 配置文件
包含各种模型和工具的配置选项
"""

import os

# 模型配置
MODEL_CONFIGS = {
    "huggingface": {
        "model_id": "Qwen/Qwen2.5-Coder-32B-Instruct",
        "token": os.environ.get("HUGGINGFACE_TOKEN"),
    },
    "openai": {
        "model_id": "gpt-4o",
        "api_key": os.environ.get("OPENAI_API_KEY"),
    },
    "anthropic": {
        "model_id": "claude-3-5-sonnet-latest",
        "api_key": os.environ.get("ANTHROPIC_API_KEY"),
    }
}

# Agent 配置
AGENT_CONFIG = {
    "max_iterations": 10,
    "stream_outputs": True,
    "verbose": True,
}

# 工具配置
TOOL_CONFIG = {
    "web_search": {
        "enabled": True,
    },
    "python_interpreter": {
        "enabled": True,
        "secure": True,
    }
}
