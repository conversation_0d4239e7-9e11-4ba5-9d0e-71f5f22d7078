# 电商数据库系统文档

## 数据库概述

本数据库是一个完整的电商系统数据库，支持用户管理、商品管理、订单处理、评价系统、购物车功能和优惠券系统。数据库采用SQLite设计，具有良好的数据完整性和性能优化。

## 核心业务流程

### 用户注册与管理
- 用户可以注册账户，提供姓名、邮箱等基本信息
- 系统自动记录用户创建时间和更新时间
- 用户可以管理多个收货地址，包括配送地址和账单地址
- 每个用户可以设置一个默认地址

### 商品管理
- 商品按分类组织，支持多级分类结构
- 每个商品包含名称、描述、价格、库存数量等信息
- 商品价格必须为非负数，库存数量不能为负
- 系统自动维护商品的创建和更新时间

### 购物流程
1. **浏览商品**: 用户可以按分类浏览商品，查看商品详情
2. **加入购物车**: 用户可以将商品添加到购物车，指定数量
3. **下单**: 用户从购物车或直接购买商品，生成订单
4. **订单处理**: 订单状态包括待处理、已确认、已发货、已送达、已取消
5. **评价**: 用户可以对购买的商品进行评分和评论

### 订单系统
- 订单包含用户信息、商品列表、总金额等
- 订单项记录每个商品的数量、单价和小计
- 系统自动计算订单总金额
- 支持订单状态跟踪，包括发货时间和送达时间

### 评价系统
- 用户可以对购买过的商品进行评价
- 评分范围为1-5星
- 每个用户对同一商品只能评价一次
- 系统记录评价时间和验证状态

### 优惠券系统
- 支持百分比折扣和固定金额折扣两种类型
- 可以设置最低订单金额和最大折扣金额
- 优惠券有使用期限和使用次数限制
- 系统记录用户的优惠券使用历史

## 数据表关系

### 主要实体关系
- **用户 (users)** ←→ **订单 (orders)**: 一对多关系
- **订单 (orders)** ←→ **订单项 (order_items)**: 一对多关系
- **商品 (products)** ←→ **订单项 (order_items)**: 一对多关系
- **用户 (users)** ←→ **评价 (reviews)**: 一对多关系
- **商品 (products)** ←→ **评价 (reviews)**: 一对多关系
- **用户 (users)** ←→ **购物车 (shopping_cart)**: 一对多关系
- **分类 (categories)** ←→ **分类 (categories)**: 自引用关系（父子分类）

### 外键约束
- 所有外键都设置了适当的约束行为
- 删除用户时，相关的订单、评价、购物车项会被级联删除
- 删除商品时，订单项会被限制删除以保护历史数据
- 删除分类时，子分类的父分类ID会被设置为NULL

## 索引优化

### 性能优化策略
- 为经常查询的字段创建索引，如用户邮箱、商品分类、订单状态等
- 为外键字段创建索引以提高JOIN操作性能
- 为时间字段创建索引以支持时间范围查询
- 为评分字段创建索引以支持评价统计查询

### 查询优化建议
- 使用索引字段进行WHERE条件过滤
- 避免在大表上进行全表扫描
- 合理使用JOIN操作，优先使用INNER JOIN
- 对于统计查询，考虑使用预计算的视图

## 数据完整性

### 约束规则
- **非空约束**: 关键字段如用户姓名、商品名称等不能为空
- **唯一约束**: 用户邮箱、优惠券代码等必须唯一
- **检查约束**: 年龄、价格、评分等字段有合理的取值范围
- **外键约束**: 确保数据引用的完整性

### 触发器机制
- 自动更新时间戳触发器
- 订单金额自动计算触发器
- 数据一致性维护触发器

## 视图和统计

### 用户订单统计视图 (user_order_stats)
提供每个用户的订单统计信息：
- 总订单数
- 总消费金额
- 平均订单金额
- 首次和最近订单时间

### 商品销售统计视图 (product_sales_stats)
提供每个商品的销售统计信息：
- 总销售数量
- 总销售收入
- 平均评分
- 评价数量

## 业务规则

### 库存管理
- 商品库存不能为负数
- 下单时需要检查库存充足性
- 订单取消时需要恢复库存

### 价格管理
- 所有价格字段必须为非负数
- 订单项的总价等于数量乘以单价
- 优惠券折扣不能超过订单金额

### 用户权限
- 用户只能查看和修改自己的订单
- 用户只能对购买过的商品进行评价
- 管理员可以查看所有数据和统计信息

### 数据归档
- 建议定期归档历史订单数据
- 保留用户评价数据以维护商品信誉
- 定期清理过期的购物车数据

## 扩展性考虑

### 水平扩展
- 可以按用户ID或时间范围进行数据分片
- 读写分离：主库处理写操作，从库处理读操作
- 缓存热点数据如商品信息、用户信息

### 功能扩展
- 支持商品变体（如尺寸、颜色）
- 添加库存预警功能
- 实现推荐系统
- 支持多语言和多货币

### 监控和维护
- 监控数据库性能指标
- 定期备份重要数据
- 监控存储空间使用情况
- 定期优化查询性能
