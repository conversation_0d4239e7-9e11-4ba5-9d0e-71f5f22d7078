#!/usr/bin/env python3
"""
演示增强的NL2SQL RAG系统的新功能
展示多数据源向量化处理能力
"""

import os
import sys
from typing import List, Dict, Any
from langchain.schema import Document

# 模拟NL2SQLSystem的文档加载功能
class MockNL2SQLSystem:
    """
    模拟NL2SQLSystem类，用于演示文档加载和处理功能
    不需要真实的OpenAI API密钥
    """
    
    def __init__(self, 
                 examples_path: str = "sql_examples.txt",
                 db_schema_path: str = None,
                 ddl_path: str = None,
                 db_docs_path: str = None):
        self.examples_path = examples_path
        self.db_schema_path = db_schema_path
        self.ddl_path = ddl_path
        self.db_docs_path = db_docs_path
        
        print("=== 增强的NL2SQL RAG系统演示 ===\n")
        self._demo_document_loading()
        self._demo_document_processing()
    
    def _load_training_documents(self) -> List[Document]:
        """加载所有训练数据并转换为Document对象"""
        all_docs = []
        
        # 1. 加载SQL问答对示例
        if self.examples_path and os.path.exists(self.examples_path):
            try:
                with open(self.examples_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                doc = Document(
                    page_content=content,
                    metadata={
                        "source_type": "sql_examples",
                        "content_type": "question_answer_pairs",
                        "file_path": self.examples_path
                    }
                )
                all_docs.append(doc)
                print(f"✓ 成功加载SQL示例文档: {self.examples_path}")
            except Exception as e:
                print(f"✗ 加载SQL示例失败: {e}")
        
        # 2. 加载DDL语句
        if self.ddl_path and os.path.exists(self.ddl_path):
            try:
                with open(self.ddl_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                doc = Document(
                    page_content=content,
                    metadata={
                        "source_type": "ddl_statements",
                        "content_type": "database_schema",
                        "file_path": self.ddl_path
                    }
                )
                all_docs.append(doc)
                print(f"✓ 成功加载DDL文档: {self.ddl_path}")
            except Exception as e:
                print(f"✗ 加载DDL文件失败: {e}")
        
        # 3. 加载数据库描述文档
        if self.db_docs_path and os.path.exists(self.db_docs_path):
            try:
                with open(self.db_docs_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                doc = Document(
                    page_content=content,
                    metadata={
                        "source_type": "database_documentation",
                        "content_type": "descriptive_text",
                        "file_path": self.db_docs_path
                    }
                )
                all_docs.append(doc)
                print(f"✓ 成功加载数据库文档: {self.db_docs_path}")
            except Exception as e:
                print(f"✗ 加载数据库文档失败: {e}")
        
        # 4. 加载数据库结构JSON
        if self.db_schema_path and os.path.exists(self.db_schema_path):
            try:
                import json
                with open(self.db_schema_path, 'r', encoding='utf-8') as f:
                    schema_data = json.load(f)
                
                # 转换为文本格式
                schema_text = self._convert_schema_to_text(schema_data)
                doc = Document(
                    page_content=schema_text,
                    metadata={
                        "source_type": "database_schema",
                        "content_type": "structured_schema",
                        "file_path": self.db_schema_path
                    }
                )
                all_docs.append(doc)
                print(f"✓ 成功转换数据库结构为文档: {self.db_schema_path}")
            except Exception as e:
                print(f"✗ 加载数据库结构失败: {e}")
        
        return all_docs
    
    def _convert_schema_to_text(self, schema_data: Dict) -> str:
        """将数据库结构信息转换为文本格式"""
        schema_text = "数据库结构信息：\n\n"
        
        for table_name, table_info in schema_data.items():
            schema_text += f"表名: {table_name}\n"
            schema_text += "字段信息:\n"
            
            for column in table_info["columns"]:
                col_name = column["name"]
                col_type = column["type"]
                schema_text += f"  - {col_name} ({col_type})\n"
            
            if "foreign_keys" in table_info and table_info["foreign_keys"]:
                schema_text += "外键关系:\n"
                for fk in table_info["foreign_keys"]:
                    schema_text += f"  - {fk['column']} 引用 {fk['reference_table']}.{fk['reference_column']}\n"
            
            schema_text += "\n"
        
        return schema_text
    
    def _demo_document_loading(self):
        """演示文档加载功能"""
        print("1. 文档加载演示")
        print("-" * 50)
        
        docs = self._load_training_documents()
        
        print(f"\n总共加载了 {len(docs)} 个文档")
        
        # 统计文档类型
        doc_types = {}
        for doc in docs:
            source_type = doc.metadata.get("source_type", "unknown")
            doc_types[source_type] = doc_types.get(source_type, 0) + 1
        
        print("\n文档类型分布:")
        for doc_type, count in doc_types.items():
            print(f"  - {doc_type}: {count} 个文档")
        
        return docs
    
    def _demo_document_processing(self):
        """演示文档处理和分割策略"""
        print("\n\n2. 文档处理演示")
        print("-" * 50)
        
        docs = self._load_training_documents()
        
        # 模拟不同的分割策略
        from langchain.text_splitter import CharacterTextSplitter
        
        processed_docs = []
        
        for doc in docs:
            content_type = doc.metadata.get("content_type", "unknown")
            source_type = doc.metadata.get("source_type", "unknown")
            
            print(f"\n处理文档: {source_type} ({content_type})")
            
            if content_type == "question_answer_pairs":
                # 对SQL问答对使用较小的chunk size
                text_splitter = CharacterTextSplitter(
                    chunk_size=500, 
                    chunk_overlap=100,
                    separator="\n\n"
                )
                print("  使用策略: 小块分割 (chunk_size=500, overlap=100)")
            elif content_type == "database_schema":
                # 对DDL语句使用中等chunk size
                text_splitter = CharacterTextSplitter(
                    chunk_size=800, 
                    chunk_overlap=150,
                    separator="\n"
                )
                print("  使用策略: 中等分割 (chunk_size=800, overlap=150)")
            else:
                # 对其他文档使用默认设置
                text_splitter = CharacterTextSplitter(
                    chunk_size=1000, 
                    chunk_overlap=200
                )
                print("  使用策略: 标准分割 (chunk_size=1000, overlap=200)")
            
            split_docs = text_splitter.split_documents([doc])
            processed_docs.extend(split_docs)
            
            print(f"  原始长度: {len(doc.page_content)} 字符")
            print(f"  分割结果: {len(split_docs)} 个文档块")
        
        print(f"\n文档分割后共有 {len(processed_docs)} 个文档块")
        
        # 显示每个文档块的信息
        print("\n文档块详情:")
        for i, doc in enumerate(processed_docs[:10]):  # 只显示前10个
            source_type = doc.metadata.get("source_type", "unknown")
            content_preview = doc.page_content[:100].replace('\n', ' ')
            print(f"  块 {i+1}: [{source_type}] {content_preview}...")
        
        if len(processed_docs) > 10:
            print(f"  ... 还有 {len(processed_docs) - 10} 个文档块")
        
        return processed_docs
    
    def demo_metadata_analysis(self):
        """演示元数据分析功能"""
        print("\n\n3. 元数据分析演示")
        print("-" * 50)
        
        docs = self._load_training_documents()
        
        print("文档元数据分析:")
        for i, doc in enumerate(docs):
            print(f"\n文档 {i+1}:")
            print(f"  源类型: {doc.metadata.get('source_type', 'N/A')}")
            print(f"  内容类型: {doc.metadata.get('content_type', 'N/A')}")
            print(f"  文件路径: {doc.metadata.get('file_path', 'N/A')}")
            print(f"  内容长度: {len(doc.page_content)} 字符")
            
            # 显示内容预览
            preview = doc.page_content[:200].replace('\n', ' ')
            print(f"  内容预览: {preview}...")

def main():
    """主演示函数"""
    print("增强的NL2SQL RAG系统功能演示")
    print("=" * 60)
    
    # 检查文件是否存在
    required_files = [
        "sql_examples.txt",
        "db_schema.json", 
        "ddl_statements.sql",
        "database_documentation.md"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"缺少以下文件: {missing_files}")
        print("请确保所有训练数据文件都存在")
        return
    
    # 创建演示系统
    demo_system = MockNL2SQLSystem(
        examples_path="sql_examples.txt",
        db_schema_path="db_schema.json",
        ddl_path="ddl_statements.sql",
        db_docs_path="database_documentation.md"
    )
    
    # 运行元数据分析演示
    demo_system.demo_metadata_analysis()
    
    print("\n\n" + "=" * 60)
    print("演示完成!")
    print("\n系统新功能总结:")
    print("✓ 支持SQL问答对向量化")
    print("✓ 支持DDL语句向量化") 
    print("✓ 支持数据库文档向量化")
    print("✓ 支持结构化Schema向量化")
    print("✓ 智能文档分割策略")
    print("✓ 元数据标记和分类")
    print("✓ 多源数据融合处理")

if __name__ == "__main__":
    main()
