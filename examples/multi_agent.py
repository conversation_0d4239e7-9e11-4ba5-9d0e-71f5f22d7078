"""
多 Agent 协作示例
展示如何使用多个 Agent 协同完成复杂任务
"""

import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from smolagents import CodeAgent, InferenceClientModel, PythonInterpreterTool
from tools.custom_tools import WeatherTool, DataAnalysisTool, TimerTool

class MultiAgentSystem:
    """多 Agent 系统"""
    
    def __init__(self):
        self.agents = {}
        self.shared_memory = {}
        self.setup_agents()
    
    def setup_agents(self):
        """设置不同专业的 Agent"""
        
        # 基础模型配置
        model_config = {
            "model_id": "Qwen/Qwen2.5-Coder-32B-Instruct",
            "token": os.environ.get("HUGGINGFACE_TOKEN"),
            "provider": "auto"
        }
        
        # 1. 数据分析师 Agent
        self.agents['data_analyst'] = CodeAgent(
            tools=[PythonInterpreterTool(), DataAnalysisTool()],
            model=InferenceClientModel(**model_config),
            stream_outputs=False
        )
        
        # 2. 天气专家 Agent  
        self.agents['weather_expert'] = CodeAgent(
            tools=[WeatherTool(), PythonInterpreterTool()],
            model=InferenceClientModel(**model_config),
            stream_outputs=False
        )
        
        # 3. 项目管理 Agent
        self.agents['project_manager'] = CodeAgent(
            tools=[TimerTool(), PythonInterpreterTool()],
            model=InferenceClientModel(**model_config),
            stream_outputs=False
        )
    
    def coordinate_task(self, task_description):
        """协调多个 Agent 完成任务"""
        print(f"🎯 任务: {task_description}\n")
        
        # 项目管理 Agent 开始计时
        print("📋 项目管理 Agent: 开始任务计时")
        pm_result = self.agents['project_manager'].run("启动计时器并记录当前时间")
        print(f"结果: {pm_result}\n")
        
        # 天气专家收集数据
        print("🌤️ 天气专家 Agent: 收集天气数据")
        weather_result = self.agents['weather_expert'].run(
            "收集北京、上海、广州三个城市的天气数据，并提取温度信息"
        )
        print(f"结果: {weather_result}\n")
        
        # 数据分析师分析数据
        print("📊 数据分析师 Agent: 分析天气数据")
        analysis_result = self.agents['data_analyst'].run(
            f"基于以下天气数据进行统计分析: {weather_result}。"
            "提取温度数值，计算平均温度、最高温度、最低温度，并生成简单的可视化图表。"
        )
        print(f"结果: {analysis_result}\n")
        
        # 项目管理 Agent 结束计时
        print("📋 项目管理 Agent: 结束任务计时")
        final_result = self.agents['project_manager'].run("停止计时器并报告总耗时")
        print(f"结果: {final_result}\n")
        
        return {
            "weather_data": weather_result,
            "analysis": analysis_result,
            "timing": final_result
        }

def demo_simple_multi_agent():
    """简单的多 Agent 演示（不依赖外部模型）"""
    print("🤖 === 简单多 Agent 协作演示 ===\n")
    
    # 直接使用工具模拟 Agent 协作
    weather_tool = WeatherTool()
    data_tool = DataAnalysisTool()
    timer_tool = TimerTool()
    
    print("1️⃣ 天气专家收集数据:")
    cities = ["北京", "上海", "广州"]
    weather_data = []
    temperatures = []
    
    for city in cities:
        weather = weather_tool.forward(city)
        print(f"   {weather}")
        # 提取温度（简单解析）
        temp = int(weather.split("温度 ")[1].split("°C")[0])
        temperatures.append(temp)
        weather_data.append(weather)
    
    print(f"\n2️⃣ 数据分析师分析温度数据: {temperatures}")
    analysis = data_tool.forward(str(temperatures), "basic_stats")
    print(f"   {analysis}")
    
    print("\n3️⃣ 项目管理计时:")
    timer_tool.forward("start")
    timer_tool.forward("sleep", 0.5)
    timing = timer_tool.forward("stop")
    print(f"   {timing}")
    
    print("\n✅ 多 Agent 协作完成！")

def demo_agent_specialization():
    """展示 Agent 专业化的概念"""
    print("🎓 === Agent 专业化概念 ===\n")
    
    specializations = """
    Agent 专业化设计原则:
    
    🔬 数据分析师 Agent:
    ├── 专业工具: DataAnalysisTool, PythonInterpreterTool
    ├── 专长: 统计分析、数据可视化、模式识别
    └── 输出: 分析报告、图表、洞察
    
    🌤️ 天气专家 Agent:
    ├── 专业工具: WeatherTool, 地理信息工具
    ├── 专长: 天气数据收集、气象分析、预测
    └── 输出: 天气报告、趋势分析
    
    📋 项目管理 Agent:
    ├── 专业工具: TimerTool, 任务管理工具
    ├── 专长: 时间管理、任务协调、进度跟踪
    └── 输出: 项目报告、时间统计
    
    🔄 协作模式:
    1. 任务分解: 将复杂任务分解为专业子任务
    2. 并行处理: 不同 Agent 同时处理不同方面
    3. 信息传递: Agent 间共享中间结果
    4. 结果整合: 汇总各 Agent 的输出
    """
    
    print(specializations)

def demo_communication_patterns():
    """展示 Agent 间通信模式"""
    print("💬 === Agent 通信模式 ===\n")
    
    patterns = """
    常见的 Agent 通信模式:
    
    1. 🔄 顺序协作 (Sequential):
       Agent A → Agent B → Agent C
       适用于: 流水线式任务处理
    
    2. 🌟 中心协调 (Hub-and-Spoke):
       Agent B ← Manager → Agent C
                ↑
              Agent A
       适用于: 需要统一协调的复杂任务
    
    3. 🕸️ 网状协作 (Mesh):
       Agent A ↔ Agent B
           ↕       ↕
       Agent C ↔ Agent D
       适用于: 高度协作的复杂问题
    
    4. 📊 分层协作 (Hierarchical):
       Manager Agent
       ├── Specialist A
       ├── Specialist B
       └── Specialist C
       适用于: 大型项目管理
    """
    
    print(patterns)

def demo_real_world_scenarios():
    """展示真实世界的多 Agent 应用场景"""
    print("🌍 === 真实世界应用场景 ===\n")
    
    scenarios = """
    多 Agent 系统的实际应用:
    
    🏢 企业应用:
    ├── 客服系统: 路由Agent + 专业客服Agent
    ├── 数据分析: 收集Agent + 清洗Agent + 分析Agent
    └── 项目管理: 规划Agent + 执行Agent + 监控Agent
    
    🔬 科研应用:
    ├── 文献调研: 搜索Agent + 筛选Agent + 总结Agent
    ├── 实验设计: 假设Agent + 设计Agent + 验证Agent
    └── 数据处理: 预处理Agent + 分析Agent + 可视化Agent
    
    🎮 游戏/娱乐:
    ├── NPC系统: 对话Agent + 行为Agent + 决策Agent
    ├── 内容生成: 创意Agent + 编写Agent + 审核Agent
    └── 个性化推荐: 分析Agent + 匹配Agent + 推荐Agent
    
    🏭 自动化:
    ├── 智能制造: 监控Agent + 控制Agent + 优化Agent
    ├── 供应链: 预测Agent + 采购Agent + 物流Agent
    └── 质量控制: 检测Agent + 分析Agent + 报告Agent
    """
    
    print(scenarios)

if __name__ == "__main__":
    print("🤖 欢迎使用 smolagents 多 Agent 协作示例！\n")
    
    # 简单演示（不需要外部模型）
    demo_simple_multi_agent()
    
    # 概念展示
    demo_agent_specialization()
    demo_communication_patterns()
    demo_real_world_scenarios()
    
    print("\n💡 高级用法:")
    print("1. 设置 HUGGINGFACE_TOKEN 环境变量")
    print("2. 运行完整的多 Agent 系统:")
    print("   system = MultiAgentSystem()")
    print("   system.coordinate_task('分析多城市天气数据')")
    print("3. 自定义 Agent 专业化配置")
    print("4. 实现 Agent 间的消息传递机制")
