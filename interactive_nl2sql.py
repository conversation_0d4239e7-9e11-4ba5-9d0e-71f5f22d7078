#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NL2SQL RAG系统交互式演示

本脚本提供了一个交互式界面，用户可以输入自然语言查询，系统将其转换为SQL查询并显示结果。
"""

import os
import json
import argparse
from nl2sql_rag_system import NL2SQLSystem

# 设置默认的OpenAI API密钥（实际使用时应通过环境变量或命令行参数设置）
os.environ["OPENAI_API_KEY"] = "your-api-key"

def parse_arguments():
    """
    解析命令行参数
    
    :return: 解析后的参数
    """
    parser = argparse.ArgumentParser(description="NL2SQL RAG系统交互式演示")
    parser.add_argument("--examples", type=str, default="sql_examples.txt",
                        help="SQL示例文件路径")
    parser.add_argument("--schema", type=str, default="db_schema.json",
                        help="数据库结构描述文件路径")
    parser.add_argument("--model", type=str, default="gpt-3.5-turbo",
                        help="使用的LLM模型名称")
    parser.add_argument("--db", type=str, default="example.db",
                        help="SQLite数据库文件路径")
    parser.add_argument("--api-key", type=str,
                        help="OpenAI API密钥（如果未设置环境变量）")
    return parser.parse_args()

def print_header():
    """
    打印程序头部信息
    """
    print("\n" + "=" * 60)
    print("\033[1m自然语言到SQL查询转换系统 (NL2SQL RAG)\033[0m")
    print("基于LangChain与LLM的检索增强生成系统")
    print("=" * 60)
    print("输入自然语言查询，系统将自动转换为SQL并执行。")
    print("输入 'exit' 或 'quit' 退出程序。")
    print("输入 'help' 查看示例查询。")
    print("=" * 60 + "\n")

def print_help():
    """
    打印帮助信息
    """
    print("\n示例查询:")
    print("  1. 查找所有年龄大于25岁的用户的姓名和邮箱")
    print("  2. 统计每个产品类别的平均价格")
    print("  3. 找出订单数量最多的前5名用户")
    print("  4. 查询2023年1月1日之后下单的所有订单及其产品名称")
    print("  5. 计算每个用户的总消费金额")
    print("  6. 查找没有下过订单的用户")
    print("  7. 找出价格最高的三种产品")
    print("\n")

def main():
    # 解析命令行参数
    args = parse_arguments()
    
    # 如果提供了API密钥，则设置环境变量
    if args.api_key:
        os.environ["OPENAI_API_KEY"] = args.api_key
    
    # 打印程序头部信息
    print_header()
    
    # 初始化NL2SQL系统
    print("正在初始化NL2SQL系统...")
    nl2sql_system = NL2SQLSystem(
        examples_path=args.examples,
        db_schema_path=args.schema,
        model_name=args.model
    )
    print("系统初始化完成！\n")
    
    # 交互式循环
    while True:
        # 获取用户输入
        query = input("\033[1m请输入自然语言查询:\033[0m ")
        
        # 检查是否退出
        if query.lower() in ["exit", "quit", "q"]:
            print("\n感谢使用NL2SQL系统，再见！")
            break
        
        # 检查是否需要帮助
        if query.lower() in ["help", "h", "?"]:
            print_help()
            continue
        
        # 如果输入为空，则继续
        if not query.strip():
            continue
        
        try:
            # 转换为SQL
            print("\n正在生成SQL查询...")
            sql = nl2sql_system.natural_language_to_sql(query)
            print(f"\n\033[1mSQL查询:\033[0m\n{sql}")
            
            # 执行SQL查询
            print("\n正在执行SQL查询...")
            results = nl2sql_system.execute_sql(sql, args.db)
            
            # 显示结果
            print(f"\n\033[1m查询结果:\033[0m")
            if results:
                # 获取列名
                columns = list(results[0].keys())
                
                # 打印表头
                header = " | ".join(f"\033[1m{col}\033[0m" for col in columns)
                print("\n" + header)
                print("-" * len(header.replace("\033[1m", "").replace("\033[0m", "")))
                
                # 打印数据行
                for row in results:
                    print(" | ".join(str(row[col]) for col in columns))
                
                print(f"\n共 {len(results)} 条记录")
            else:
                print("无结果")
                
        except Exception as e:
            print(f"\n\033[91m错误: {str(e)}\033[0m")
        
        print("\n" + "-" * 60)

if __name__ == "__main__":
    main()