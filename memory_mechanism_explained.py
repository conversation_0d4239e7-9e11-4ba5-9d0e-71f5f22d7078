#!/usr/bin/env python3
"""
RAG系统记忆机制详细解析
回答：记忆存储在哪里？多少轮？如何传递给模型？
"""

import json
from typing import List, Dict, Any
from datetime import datetime

class MemoryMechanismExplainer:
    """记忆机制解释器"""
    
    def __init__(self):
        pass
    
    def explain_memory_storage(self):
        """解释记忆存储机制"""
        print("🧠 RAG系统记忆存储机制详解")
        print("=" * 80)
        
        storage_explanation = {
            "1. 存储位置": {
                "主要存储": "内存中 (RAM)",
                "具体位置": "Python对象的属性中",
                "数据结构": "ConversationBufferWindowMemory对象",
                "持久化": "可选择保存到文件，但默认不持久化"
            },
            "2. 存储内容": {
                "消息列表": "self.memory.chat_memory.messages",
                "消息类型": ["HumanMessage (用户消息)", "AIMessage (助手消息)"],
                "消息属性": ["content (内容)", "type (类型)", "timestamp (时间戳)"]
            },
            "3. 存储限制": {
                "窗口大小": "默认10轮对话 (可配置)",
                "内存管理": "超出窗口大小时自动删除最早的消息",
                "内存占用": "随对话长度线性增长，但有上限"
            }
        }
        
        for section, details in storage_explanation.items():
            print(f"\n📋 {section}")
            print("-" * 60)
            for key, value in details.items():
                if isinstance(value, list):
                    print(f"  {key}:")
                    for item in value:
                        print(f"    • {item}")
                else:
                    print(f"  {key}: {value}")
    
    def demonstrate_memory_flow(self):
        """演示记忆流转过程"""
        print("\n\n🔄 记忆流转过程演示")
        print("=" * 80)
        
        print("当用户输入问题时，系统的处理流程：")
        print("-" * 60)
        
        flow_steps = [
            {
                "步骤": "1. 接收用户输入",
                "位置": "chat() 方法",
                "操作": "user_input = '查询用户表'",
                "代码": "response = self.agent_executor.invoke({'input': user_input, 'chat_history': self.memory.chat_memory.messages})"
            },
            {
                "步骤": "2. 提取历史记忆",
                "位置": "内存中",
                "操作": "从 self.memory.chat_memory.messages 获取历史对话",
                "代码": "chat_history = self.memory.chat_memory.messages"
            },
            {
                "步骤": "3. 构建完整上下文",
                "位置": "Agent内部",
                "操作": "将历史记忆 + 当前输入组合成完整提示词",
                "代码": "prompt = ChatPromptTemplate.from_messages([('system', system_prompt), ('placeholder', '{chat_history}'), ('human', '{input}')])"
            },
            {
                "步骤": "4. 发送给大模型",
                "位置": "LLM API",
                "操作": "完整的上下文作为messages参数发送",
                "代码": "llm.invoke(messages=[system_msg, *chat_history, human_msg])"
            },
            {
                "步骤": "5. 保存新的对话",
                "位置": "内存中",
                "操作": "将用户输入和AI回复保存到记忆中",
                "代码": "self.memory.chat_memory.add_user_message(user_input); self.memory.chat_memory.add_ai_message(ai_response)"
            }
        ]
        
        for step in flow_steps:
            print(f"\n{step['步骤']}")
            print(f"  📍 位置: {step['位置']}")
            print(f"  🔧 操作: {step['操作']}")
            print(f"  💻 代码: {step['代码']}")
    
    def show_memory_structure(self):
        """展示记忆数据结构"""
        print("\n\n📊 记忆数据结构详解")
        print("=" * 80)
        
        print("ConversationBufferWindowMemory 内部结构：")
        print("-" * 60)
        
        structure = """
self.memory = ConversationBufferWindowMemory(
    k=10,                    # 保留最近10轮对话
    memory_key="chat_history",  # 在提示词中的键名
    return_messages=True     # 返回Message对象而不是字符串
)

内部数据结构：
self.memory.chat_memory.messages = [
    HumanMessage(content="查询用户表", type="human"),
    AIMessage(content="生成SQL: SELECT * FROM users", type="ai"),
    HumanMessage(content="执行这个查询", type="human"), 
    AIMessage(content="查询结果: 100条记录", type="ai"),
    # ... 最多保留10轮对话(20条消息)
]
        """
        print(structure)
        
        print("\n消息对象属性：")
        print("-" * 30)
        message_attrs = [
            "content: 消息内容文本",
            "type: 消息类型 ('human' 或 'ai')",
            "additional_kwargs: 额外的元数据",
            "example: 是否为示例消息"
        ]
        
        for attr in message_attrs:
            print(f"  • {attr}")
    
    def explain_prompt_injection(self):
        """解释提示词注入机制"""
        print("\n\n💉 提示词注入机制")
        print("=" * 80)
        
        print("记忆如何注入到提示词中：")
        print("-" * 60)
        
        prompt_template = """
# 1. 系统提示词模板
system_prompt = '''
你是一个专业的SQL数据库助手Agent...
'''

# 2. 提示词模板定义
prompt = ChatPromptTemplate.from_messages([
    ("system", system_prompt),
    ("placeholder", "{chat_history}"),    # 这里注入历史记忆
    ("human", "{input}"),                 # 这里注入用户输入
    ("placeholder", "{agent_scratchpad}") # 这里注入工具调用记录
])

# 3. 实际调用时的消息构建
messages = [
    {"role": "system", "content": "你是一个专业的SQL数据库助手..."},
    {"role": "user", "content": "查询用户表"},
    {"role": "assistant", "content": "生成SQL: SELECT * FROM users"},
    {"role": "user", "content": "执行这个查询"},        # 当前输入
]
        """
        print(prompt_template)
        
        print("\n🔍 关键点解析：")
        print("-" * 30)
        key_points = [
            "chat_history占位符会被替换为历史消息列表",
            "每条历史消息都会转换为对应的role和content",
            "整个消息列表作为context发送给大模型",
            "大模型基于完整上下文生成回复"
        ]
        
        for point in key_points:
            print(f"  • {point}")
    
    def demonstrate_window_mechanism(self):
        """演示窗口机制"""
        print("\n\n🪟 窗口机制演示")
        print("=" * 80)
        
        print("模拟10轮对话的窗口滑动：")
        print("-" * 60)
        
        # 模拟对话历史
        conversations = [
            ("用户", "你好"),
            ("助手", "您好！我是SQL助手"),
            ("用户", "查询用户表"),
            ("助手", "SELECT * FROM users"),
            ("用户", "执行查询"),
            ("助手", "返回100条记录"),
            ("用户", "分析结果"),
            ("助手", "用户平均年龄32岁"),
            ("用户", "创建图表"),
            ("助手", "正在创建图表..."),
            ("用户", "保存图表"),  # 第11条消息
            ("助手", "图表已保存"),  # 第12条消息
        ]
        
        print("窗口大小 = 5轮对话 (10条消息)")
        print()
        
        memory_window = []
        
        for i, (role, content) in enumerate(conversations):
            # 添加到窗口
            memory_window.append(f"{role}: {content}")
            
            # 保持窗口大小
            if len(memory_window) > 10:  # 5轮对话 = 10条消息
                removed = memory_window.pop(0)
                print(f"第{i+1}轮 - 移除: {removed}")
            
            print(f"第{i+1}轮 - 当前窗口大小: {len(memory_window)}")
            
            if i >= 10:  # 从第11轮开始显示窗口内容
                print("  当前窗口内容:")
                for j, msg in enumerate(memory_window[-4:]):  # 显示最近4条
                    print(f"    {j+1}. {msg}")
                print()
    
    def explain_memory_vs_rag(self):
        """解释记忆与RAG的区别"""
        print("\n\n🆚 对话记忆 vs RAG向量检索")
        print("=" * 80)
        
        comparison = {
            "对话记忆 (ConversationMemory)": {
                "存储内容": "当前会话的对话历史",
                "存储位置": "内存中的Python对象",
                "存储时长": "会话期间 (临时)",
                "检索方式": "按时间顺序直接获取",
                "用途": "维持对话连贯性，理解指代词",
                "示例": "'这个查询' 指代上一轮生成的SQL"
            },
            "RAG向量检索 (VectorStore)": {
                "存储内容": "训练数据 (SQL示例、DDL、文档)",
                "存储位置": "向量数据库 (ChromaDB)",
                "存储时长": "持久化存储",
                "检索方式": "语义相似度检索",
                "用途": "提供相关示例和知识",
                "示例": "检索相似的SQL查询示例"
            }
        }
        
        for memory_type, details in comparison.items():
            print(f"\n📋 {memory_type}")
            print("-" * 50)
            for key, value in details.items():
                print(f"  {key}: {value}")
        
        print("\n🔗 两者如何协作：")
        print("-" * 30)
        collaboration = [
            "1. RAG检索: 根据用户问题检索相关知识",
            "2. 记忆注入: 将对话历史加入上下文",
            "3. 上下文融合: 知识 + 历史 + 当前问题",
            "4. 模型生成: 基于完整上下文生成回复",
            "5. 记忆更新: 保存新的对话轮次"
        ]
        
        for step in collaboration:
            print(f"  {step}")
    
    def show_practical_example(self):
        """展示实际例子"""
        print("\n\n💡 实际例子演示")
        print("=" * 80)
        
        print("假设用户进行以下对话：")
        print("-" * 60)
        
        example_conversation = [
            {
                "轮次": 1,
                "用户": "查询用户表的数据",
                "系统处理": {
                    "RAG检索": "检索SQL示例: SELECT * FROM users",
                    "记忆状态": "空 (首轮对话)",
                    "发送给模型": "系统提示词 + RAG结果 + 用户问题"
                },
                "助手": "SELECT * FROM users;",
                "记忆更新": "保存用户问题和助手回复"
            },
            {
                "轮次": 2,
                "用户": "执行这个查询",
                "系统处理": {
                    "RAG检索": "检索执行相关的示例",
                    "记忆状态": "包含第1轮的SQL生成对话",
                    "发送给模型": "系统提示词 + RAG结果 + 历史对话 + 当前问题"
                },
                "助手": "正在执行 SELECT * FROM users; 返回150条记录",
                "记忆更新": "保存第2轮对话"
            },
            {
                "轮次": 3,
                "用户": "分析这些结果",
                "系统处理": {
                    "RAG检索": "检索数据分析相关示例",
                    "记忆状态": "包含前2轮对话 (SQL生成+执行)",
                    "发送给模型": "完整上下文，模型知道'这些结果'指代150条用户记录"
                },
                "助手": "分析150条用户记录：平均年龄32岁...",
                "记忆更新": "保存第3轮对话"
            }
        ]
        
        for conv in example_conversation:
            print(f"\n🔄 第{conv['轮次']}轮对话")
            print(f"👤 用户: {conv['用户']}")
            print("🔧 系统处理:")
            for key, value in conv['系统处理'].items():
                print(f"  • {key}: {value}")
            print(f"🤖 助手: {conv['助手']}")
            print(f"💾 记忆更新: {conv['记忆更新']}")
    
    def run_complete_explanation(self):
        """运行完整解释"""
        print("🧠 RAG系统记忆机制完整解析")
        print("=" * 80)
        print("回答：记忆存在哪里？多少轮？如何传递给模型？")
        
        # 1. 存储机制
        self.explain_memory_storage()
        
        # 2. 流转过程
        self.demonstrate_memory_flow()
        
        # 3. 数据结构
        self.show_memory_structure()
        
        # 4. 提示词注入
        self.explain_prompt_injection()
        
        # 5. 窗口机制
        self.demonstrate_window_mechanism()
        
        # 6. 记忆vs RAG
        self.explain_memory_vs_rag()
        
        # 7. 实际例子
        self.show_practical_example()
        
        print("\n\n🎉 解析完成！")
        print("📝 总结回答：")
        print("  1. 存储位置: 内存中的Python对象 (self.memory.chat_memory.messages)")
        print("  2. 存储轮数: 默认10轮对话 (可配置)")
        print("  3. 传递方式: 作为chat_history注入到提示词模板中")
        print("  4. 发送格式: 转换为messages列表发送给大模型API")

if __name__ == "__main__":
    explainer = MemoryMechanismExplainer()
    explainer.run_complete_explanation()
