from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import Chroma
from langchain.chat_models import ChatOpenAI
from langchain.chains import ConversationalRetrievalChain
from langchain.memory import ConversationBufferMemory
from langchain.document_loaders import TextLoader, CSVLoader
from langchain.text_splitter import CharacterTextSplitter
from langchain.prompts import PromptTemplate, ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langchain.chains.question_answering import load_qa_chain
from langchain.schema import Document
import os
import json
import sqlite3
from typing import List, Dict, Any, Optional

# 设置OpenAI API密钥
os.environ["OPENAI_API_KEY"] = "your-api-key"

class NL2SQLSystem:
    """
    基于LangChain与LLM的RAG系统，实现自然语言自动转换为SQL查询
    通过语义检索机制与动态Prompt模板设计，提高SQL生成准确率
    """
    
    def __init__(self,
                 examples_path: str = "sql_examples.txt",
                 db_schema_path: Optional[str] = None,
                 ddl_path: Optional[str] = None,
                 db_docs_path: Optional[str] = None,
                 vector_db_path: str = "./chroma_db",
                 model_name: str = "gpt-3.5-turbo",
                 temperature: float = 0):
        """
        初始化NL2SQL系统

        :param examples_path: SQL示例文件路径（包含自然语言-SQL问答对）
        :param db_schema_path: 数据库结构描述文件路径（JSON格式）
        :param ddl_path: DDL语句文件路径
        :param db_docs_path: 数据库描述文档路径
        :param vector_db_path: 向量数据库存储路径
        :param model_name: 使用的LLM模型名称
        :param temperature: 模型温度参数
        """
        self.examples_path = examples_path
        self.db_schema_path = db_schema_path
        self.ddl_path = ddl_path
        self.db_docs_path = db_docs_path
        self.vector_db_path = vector_db_path
        self.model_name = model_name
        self.temperature = temperature

        # 初始化组件
        self.llm = None
        self.memory = None
        self.vectorstore = None
        self.qa_chain = None
        self.db_schema = None

        # 加载数据库结构信息（如果提供）
        if db_schema_path:
            self._load_db_schema()

        # 初始化系统
        self._initialize_system()
    
    def _load_db_schema(self):
        """
        加载数据库结构信息
        """
        try:
            with open(self.db_schema_path, 'r', encoding='utf-8') as f:
                self.db_schema = json.load(f)
            print(f"成功加载数据库结构信息: {len(self.db_schema)} 个表")
        except Exception as e:
            print(f"加载数据库结构信息失败: {e}")
            self.db_schema = None
    
    def _convert_schema_to_text(self) -> str:
        """
        将数据库结构信息转换为文本格式，用于向量化

        :return: 数据库结构描述文本
        """
        if not self.db_schema:
            return ""

        schema_text = "数据库结构信息：\n\n"

        for table_name, table_info in self.db_schema.items():
            schema_text += f"表名: {table_name}\n"
            schema_text += "字段信息:\n"

            for column in table_info["columns"]:
                col_name = column["name"]
                col_type = column["type"]
                schema_text += f"  - {col_name} ({col_type})\n"

            if "foreign_keys" in table_info and table_info["foreign_keys"]:
                schema_text += "外键关系:\n"
                for fk in table_info["foreign_keys"]:
                    schema_text += f"  - {fk['column']} 引用 {fk['reference_table']}.{fk['reference_column']}\n"

            schema_text += "\n"

        return schema_text

    def _load_training_documents(self) -> List[Document]:
        """
        加载所有训练数据并转换为Document对象

        :return: 文档列表
        """
        all_docs = []

        # 1. 加载SQL问答对示例
        if self.examples_path and os.path.exists(self.examples_path):
            try:
                example_loader = TextLoader(self.examples_path, encoding='utf-8')
                example_docs = example_loader.load()
                # 为SQL示例添加元数据
                for doc in example_docs:
                    doc.metadata.update({
                        "source_type": "sql_examples",
                        "content_type": "question_answer_pairs"
                    })
                all_docs.extend(example_docs)
                print(f"成功加载SQL示例文档: {len(example_docs)} 个")
            except Exception as e:
                print(f"加载SQL示例失败: {e}")

        # 2. 加载DDL语句
        if self.ddl_path and os.path.exists(self.ddl_path):
            try:
                ddl_loader = TextLoader(self.ddl_path, encoding='utf-8')
                ddl_docs = ddl_loader.load()
                # 为DDL语句添加元数据
                for doc in ddl_docs:
                    doc.metadata.update({
                        "source_type": "ddl_statements",
                        "content_type": "database_schema"
                    })
                all_docs.extend(ddl_docs)
                print(f"成功加载DDL文档: {len(ddl_docs)} 个")
            except Exception as e:
                print(f"加载DDL文件失败: {e}")

        # 3. 加载数据库描述文档
        if self.db_docs_path and os.path.exists(self.db_docs_path):
            try:
                docs_loader = TextLoader(self.db_docs_path, encoding='utf-8')
                db_docs = docs_loader.load()
                # 为数据库文档添加元数据
                for doc in db_docs:
                    doc.metadata.update({
                        "source_type": "database_documentation",
                        "content_type": "descriptive_text"
                    })
                all_docs.extend(db_docs)
                print(f"成功加载数据库文档: {len(db_docs)} 个")
            except Exception as e:
                print(f"加载数据库文档失败: {e}")

        # 4. 如果数据库结构以JSON格式存在，转换为文本形式
        if self.db_schema:
            schema_text = self._convert_schema_to_text()
            schema_doc = Document(
                page_content=schema_text,
                metadata={
                    "source_type": "database_schema",
                    "content_type": "structured_schema"
                }
            )
            all_docs.append(schema_doc)
            print("成功转换数据库结构为文档")

        return all_docs

    def _initialize_system(self):
        """
        初始化系统，加载所有训练数据并创建向量存储
        """
        print("开始初始化NL2SQL系统...")

        # 加载所有训练文档
        all_docs = self._load_training_documents()

        if not all_docs:
            raise ValueError("没有找到任何训练数据，请检查文件路径")

        print(f"总共加载了 {len(all_docs)} 个文档")

        # 使用不同的分割策略处理不同类型的文档
        processed_docs = []

        for doc in all_docs:
            content_type = doc.metadata.get("content_type", "unknown")

            if content_type == "question_answer_pairs":
                # 对SQL问答对使用较小的chunk size以保持问答对的完整性
                text_splitter = CharacterTextSplitter(
                    chunk_size=500,
                    chunk_overlap=100,
                    separator="\n\n"
                )
            elif content_type == "ddl_statements":
                # 对DDL语句使用中等chunk size
                text_splitter = CharacterTextSplitter(
                    chunk_size=800,
                    chunk_overlap=150,
                    separator="\n"
                )
            else:
                # 对其他文档使用默认设置
                text_splitter = CharacterTextSplitter(
                    chunk_size=1000,
                    chunk_overlap=200
                )

            split_docs = text_splitter.split_documents([doc])
            processed_docs.extend(split_docs)

        print(f"文档分割后共有 {len(processed_docs)} 个文档块")

        # 初始化embeddings和向量存储
        embeddings = OpenAIEmbeddings()
        self.vectorstore = Chroma.from_documents(
            documents=processed_docs,
            embedding=embeddings,
            persist_directory=self.vector_db_path
        )
        self.vectorstore.persist()
        print(f"向量存储已保存到: {self.vector_db_path}")

        # 初始化对话模型和记忆
        self.llm = ChatOpenAI(temperature=self.temperature, model_name=self.model_name)
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )

        # 创建对话检索链
        self._create_qa_chain()
        print("NL2SQL系统初始化完成!")
    
    def _create_qa_chain(self):
        """
        创建问答链，使用自定义提示词模板
        """
        # 系统提示词模板
        system_template = """
        你是一个专业的SQL专家，擅长将自然语言转换为准确的SQL查询语句。
        
        {db_schema}
        
        请遵循以下规则：
        1. 仅返回有效的SQL查询语句，不要包含任何解释或注释
        2. 确保SQL语法正确，并与提供的数据库结构兼容
        3. 使用标准SQL语法，避免使用特定数据库的专有功能
        4. 如果查询涉及多个表，请使用适当的JOIN操作
        5. 如果自然语言查询不明确，请生成最合理的SQL查询
        """
        
        # 人类提示词模板
        human_template = """
        基于以下相似的示例和上下文信息，将自然语言查询转换为SQL查询语句：
        
        相似示例：
        {context}
        
        自然语言查询：{question}
        
        SQL查询：
        """
        
        # 创建系统消息提示词模板
        system_message_prompt = SystemMessagePromptTemplate.from_template(system_template)
        
        # 创建人类消息提示词模板
        human_message_prompt = HumanMessagePromptTemplate.from_template(human_template)
        
        # 组合成聊天提示词模板
        chat_prompt = ChatPromptTemplate.from_messages([system_message_prompt, human_message_prompt])
        
        # 创建问答链
        doc_chain = load_qa_chain(
            llm=self.llm,
            chain_type="stuff",
            prompt=chat_prompt
        )
        
        # 创建对话检索链
        self.qa_chain = ConversationalRetrievalChain(
            retriever=self.vectorstore.as_retriever(search_kwargs={"k": 5}),
            combine_docs_chain=doc_chain,
            memory=self.memory,
        )
    
    def _get_db_schema_str(self) -> str:
        """
        将数据库结构信息转换为字符串格式
        
        :return: 数据库结构描述字符串
        """
        if not self.db_schema:
            return "数据库结构信息未提供。"
        
        schema_str = "数据库结构信息：\n"
        
        for table_name, table_info in self.db_schema.items():
            schema_str += f"表名: {table_name}\n"
            schema_str += "列:"  
            
            for column in table_info["columns"]:
                col_name = column["name"]
                col_type = column["type"]
                schema_str += f" {col_name}({col_type}),"
            
            schema_str = schema_str.rstrip(",") + "\n"
            
            if "foreign_keys" in table_info and table_info["foreign_keys"]:
                schema_str += "外键: "
                for fk in table_info["foreign_keys"]:
                    schema_str += f"{fk['column']} -> {fk['reference_table']}.{fk['reference_column']}, "
                schema_str = schema_str.rstrip(", ") + "\n"
            
            schema_str += "\n"
        
        return schema_str
    
    def natural_language_to_sql(self, query: str) -> str:
        """
        将自然语言转换为SQL查询
        
        :param query: 自然语言查询
        :return: SQL查询语句
        """
        # 准备数据库结构信息
        db_schema_str = self._get_db_schema_str()
        
        # 执行查询
        result = self.qa_chain(
            {
                "question": query,
                "db_schema": db_schema_str
            }
        )
        
        return result["answer"]
    
    def execute_sql(self, sql_query: str, db_path: str) -> List[Dict[str, Any]]:
        """
        执行SQL查询并返回结果
        
        :param sql_query: SQL查询语句
        :param db_path: SQLite数据库文件路径
        :return: 查询结果列表
        """
        try:
            # 连接到SQLite数据库
            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 执行查询
            cursor.execute(sql_query)
            
            # 获取结果
            rows = cursor.fetchall()
            
            # 转换为字典列表
            results = [dict(row) for row in rows]
            
            # 关闭连接
            conn.close()
            
            return results
        except Exception as e:
            print(f"执行SQL查询失败: {e}")
            return []

# 使用示例
if __name__ == "__main__":
    print("=== NL2SQL RAG系统演示 ===\n")

    # 初始化增强的NL2SQL系统，支持多种训练数据源
    nl2sql_system = NL2SQLSystem(
        examples_path="sql_examples.txt",           # SQL问答对示例
        db_schema_path="db_schema.json",            # 数据库结构JSON文件
        ddl_path="ddl_statements.sql",              # DDL语句文件
        db_docs_path="database_documentation.md",   # 数据库描述文档
        model_name="gpt-3.5-turbo",
        vector_db_path="./enhanced_chroma_db"
    )

    print("\n=== 测试自然语言到SQL的转换 ===")

    # 测试查询 - 涵盖不同复杂度和业务场景
    test_queries = [
        # 基础查询
        "查找所有年龄大于25岁的用户的姓名和邮箱",

        # 聚合查询
        "统计每个产品类别的平均价格和商品数量",

        # 连接查询
        "找出订单数量最多的前5名用户及其总消费金额",

        # 时间范围查询
        "查询最近30天内的所有订单及其状态",

        # 复杂业务查询
        "找出评分最高的前10个商品及其平均评分和评价数量",

        # 子查询
        "查找购买过价格超过平均价格商品的用户",

        # 多表连接
        "统计每个用户使用优惠券的次数和总节省金额",

        # 窗口函数查询
        "查询每个分类中销量排名前3的商品"
    ]

    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. 自然语言查询: {query}")
        try:
            sql = nl2sql_system.natural_language_to_sql(query)
            print(f"   生成的SQL: {sql}")
        except Exception as e:
            print(f"   错误: {e}")
        print("-" * 80)