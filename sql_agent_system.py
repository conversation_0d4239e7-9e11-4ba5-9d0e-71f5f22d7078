#!/usr/bin/env python3
"""
SQL Agent System - 基于RAG的智能数据库助手
将原有的NL2SQL RAG系统升级为具备多种工具的Agent
"""

import os
import json
import sqlite3
import pandas as pd
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_community.vectorstores import Chroma
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.tools import BaseTool, tool
from langchain.schema import Document
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import CharacterTextSplitter
from langchain.prompts import ChatPromptTemplate
from langchain.memory import ConversationBufferMemory
from pydantic import BaseModel, Field

# 设置OpenAI API密钥
os.environ["OPENAI_API_KEY"] = "your-api-key"

class SQLQueryInput(BaseModel):
    """SQL查询工具的输入模型"""
    natural_language_query: str = Field(description="用户的自然语言查询")
    
class SQLExecutionInput(BaseModel):
    """SQL执行工具的输入模型"""
    sql_query: str = Field(description="要执行的SQL查询语句")
    database_path: str = Field(description="数据库文件路径", default="database.db")

class DataAnalysisInput(BaseModel):
    """数据分析工具的输入模型"""
    data_description: str = Field(description="要分析的数据描述")
    analysis_type: str = Field(description="分析类型：统计、趋势、对比等")

class DatabaseSchemaInput(BaseModel):
    """数据库结构查询工具的输入模型"""
    table_name: Optional[str] = Field(description="特定表名，如果为空则返回所有表", default=None)

class SQLAgent:
    """
    SQL Agent - 智能数据库助手
    集成多种工具：SQL生成、执行、分析、可视化等
    """
    
    def __init__(self, 
                 examples_path: str = "sql_examples.txt",
                 db_schema_path: Optional[str] = None,
                 ddl_path: Optional[str] = None,
                 db_docs_path: Optional[str] = None,
                 vector_db_path: str = "./agent_chroma_db",
                 model_name: str = "gpt-3.5-turbo",
                 temperature: float = 0):
        
        self.examples_path = examples_path
        self.db_schema_path = db_schema_path
        self.ddl_path = ddl_path
        self.db_docs_path = db_docs_path
        self.vector_db_path = vector_db_path
        self.model_name = model_name
        self.temperature = temperature
        
        # 初始化组件
        self.llm = None
        self.vectorstore = None
        self.agent_executor = None
        self.db_schema = None
        
        # 初始化系统
        self._initialize_rag_system()
        self._create_tools()
        self._create_agent()
    
    def _initialize_rag_system(self):
        """初始化RAG系统（复用原有逻辑）"""
        print("初始化RAG系统...")
        
        # 加载数据库结构
        if self.db_schema_path:
            self._load_db_schema()
        
        # 加载训练文档
        all_docs = self._load_training_documents()
        
        if not all_docs:
            print("警告: 没有找到训练数据")
            return
        
        # 文档分割和向量化
        processed_docs = []
        for doc in all_docs:
            content_type = doc.metadata.get("content_type", "unknown")
            
            if content_type == "question_answer_pairs":
                text_splitter = CharacterTextSplitter(chunk_size=500, chunk_overlap=100)
            elif content_type == "database_schema":
                text_splitter = CharacterTextSplitter(chunk_size=800, chunk_overlap=150)
            else:
                text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
            
            split_docs = text_splitter.split_documents([doc])
            processed_docs.extend(split_docs)
        
        # 创建向量存储
        embeddings = OpenAIEmbeddings()
        self.vectorstore = Chroma.from_documents(
            documents=processed_docs,
            embedding=embeddings,
            persist_directory=self.vector_db_path
        )
        
        # 初始化LLM
        self.llm = ChatOpenAI(temperature=self.temperature, model_name=self.model_name)
        
        print(f"RAG系统初始化完成，共处理 {len(processed_docs)} 个文档块")
    
    def _load_db_schema(self):
        """加载数据库结构信息"""
        try:
            with open(self.db_schema_path, 'r', encoding='utf-8') as f:
                self.db_schema = json.load(f)
        except Exception as e:
            print(f"加载数据库结构失败: {e}")
            self.db_schema = None
    
    def _load_training_documents(self) -> List[Document]:
        """加载训练文档（复用原有逻辑）"""
        all_docs = []
        
        # 1. SQL示例
        if self.examples_path and os.path.exists(self.examples_path):
            try:
                loader = TextLoader(self.examples_path, encoding='utf-8')
                docs = loader.load()
                for doc in docs:
                    doc.metadata.update({
                        "source_type": "sql_examples",
                        "content_type": "question_answer_pairs"
                    })
                all_docs.extend(docs)
            except Exception as e:
                print(f"加载SQL示例失败: {e}")
        
        # 2. DDL语句
        if self.ddl_path and os.path.exists(self.ddl_path):
            try:
                loader = TextLoader(self.ddl_path, encoding='utf-8')
                docs = loader.load()
                for doc in docs:
                    doc.metadata.update({
                        "source_type": "ddl_statements",
                        "content_type": "database_schema"
                    })
                all_docs.extend(docs)
            except Exception as e:
                print(f"加载DDL文件失败: {e}")
        
        # 3. 数据库文档
        if self.db_docs_path and os.path.exists(self.db_docs_path):
            try:
                loader = TextLoader(self.db_docs_path, encoding='utf-8')
                docs = loader.load()
                for doc in docs:
                    doc.metadata.update({
                        "source_type": "database_documentation",
                        "content_type": "descriptive_text"
                    })
                all_docs.extend(docs)
            except Exception as e:
                print(f"加载数据库文档失败: {e}")
        
        # 4. 数据库结构
        if self.db_schema:
            schema_text = self._convert_schema_to_text()
            schema_doc = Document(
                page_content=schema_text,
                metadata={
                    "source_type": "database_schema",
                    "content_type": "structured_schema"
                }
            )
            all_docs.append(schema_doc)
        
        return all_docs
    
    def _convert_schema_to_text(self) -> str:
        """将数据库结构转换为文本"""
        if not self.db_schema:
            return ""
        
        schema_text = "数据库结构信息：\n\n"
        for table_name, table_info in self.db_schema.items():
            schema_text += f"表名: {table_name}\n"
            schema_text += "字段信息:\n"
            
            for column in table_info["columns"]:
                col_name = column["name"]
                col_type = column["type"]
                schema_text += f"  - {col_name} ({col_type})\n"
            
            if "foreign_keys" in table_info and table_info["foreign_keys"]:
                schema_text += "外键关系:\n"
                for fk in table_info["foreign_keys"]:
                    schema_text += f"  - {fk['column']} 引用 {fk['reference_table']}.{fk['reference_column']}\n"
            
            schema_text += "\n"
        
        return schema_text
    
    def _create_tools(self):
        """创建Agent工具"""
        
        @tool("sql_generator", args_schema=SQLQueryInput)
        def generate_sql(natural_language_query: str) -> str:
            """
            根据自然语言查询生成SQL语句
            使用RAG系统检索相关示例和文档
            """
            try:
                # 使用向量检索获取相关上下文
                relevant_docs = self.vectorstore.similarity_search(natural_language_query, k=5)
                
                # 构建上下文
                context = "\n".join([doc.page_content for doc in relevant_docs])
                
                # 构建提示词
                prompt = f"""
                基于以下数据库信息和示例，将自然语言查询转换为SQL语句：
                
                数据库结构：
                {self._get_db_schema_str()}
                
                相关示例和文档：
                {context}
                
                自然语言查询：{natural_language_query}
                
                请只返回SQL语句，不要包含任何解释：
                """
                
                response = self.llm.invoke(prompt)
                return response.content.strip()
                
            except Exception as e:
                return f"SQL生成失败: {str(e)}"
        
        @tool("sql_executor", args_schema=SQLExecutionInput)
        def execute_sql(sql_query: str, database_path: str = "database.db") -> str:
            """
            执行SQL查询并返回结果
            支持SELECT、INSERT、UPDATE、DELETE等操作
            """
            try:
                conn = sqlite3.connect(database_path)
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute(sql_query)
                
                if sql_query.strip().upper().startswith('SELECT'):
                    rows = cursor.fetchall()
                    if rows:
                        # 转换为字典列表
                        results = [dict(row) for row in rows]
                        return f"查询成功，返回 {len(results)} 条记录：\n{json.dumps(results, ensure_ascii=False, indent=2)}"
                    else:
                        return "查询成功，但没有找到匹配的记录"
                else:
                    conn.commit()
                    return f"SQL执行成功，影响了 {cursor.rowcount} 行记录"
                
            except Exception as e:
                return f"SQL执行失败: {str(e)}"
            finally:
                if 'conn' in locals():
                    conn.close()
        
        @tool("database_schema_inspector", args_schema=DatabaseSchemaInput)
        def inspect_database_schema(table_name: Optional[str] = None) -> str:
            """
            查看数据库结构信息
            可以查看所有表或特定表的结构
            """
            try:
                if not self.db_schema:
                    return "数据库结构信息未加载"
                
                if table_name:
                    if table_name in self.db_schema:
                        table_info = self.db_schema[table_name]
                        result = f"表 {table_name} 的结构：\n"
                        result += "字段：\n"
                        for col in table_info["columns"]:
                            result += f"  - {col['name']} ({col['type']})\n"
                        
                        if table_info.get("foreign_keys"):
                            result += "外键：\n"
                            for fk in table_info["foreign_keys"]:
                                result += f"  - {fk['column']} -> {fk['reference_table']}.{fk['reference_column']}\n"
                        
                        return result
                    else:
                        return f"表 {table_name} 不存在"
                else:
                    result = "数据库中的所有表：\n"
                    for table_name in self.db_schema.keys():
                        result += f"  - {table_name}\n"
                    return result
                    
            except Exception as e:
                return f"查看数据库结构失败: {str(e)}"
        
        @tool("data_analyzer", args_schema=DataAnalysisInput)
        def analyze_data(data_description: str, analysis_type: str = "统计") -> str:
            """
            对查询结果进行数据分析
            支持统计分析、趋势分析、对比分析等
            """
            try:
                analysis_prompt = f"""
                请对以下数据进行{analysis_type}分析：
                
                数据描述：{data_description}
                
                请提供：
                1. 数据概览
                2. 关键指标
                3. 趋势或模式
                4. 建议或洞察
                """
                
                response = self.llm.invoke(analysis_prompt)
                return response.content
                
            except Exception as e:
                return f"数据分析失败: {str(e)}"
        
        # 存储工具
        self.tools = [generate_sql, execute_sql, inspect_database_schema, analyze_data]
    
    def _get_db_schema_str(self) -> str:
        """获取数据库结构字符串"""
        if not self.db_schema:
            return "数据库结构信息未提供"
        
        schema_str = ""
        for table_name, table_info in self.db_schema.items():
            schema_str += f"表 {table_name}: "
            columns = [f"{col['name']}({col['type']})" for col in table_info["columns"]]
            schema_str += ", ".join(columns) + "\n"
        
        return schema_str
    
    def _create_agent(self):
        """创建Agent执行器"""
        
        # 定义Agent提示词
        system_prompt = """
        你是一个专业的SQL数据库助手Agent，具备以下能力：
        
        1. **SQL生成**: 将自然语言转换为准确的SQL查询
        2. **SQL执行**: 安全地执行SQL语句并返回结果
        3. **数据库检查**: 查看和分析数据库结构
        4. **数据分析**: 对查询结果进行深入分析
        
        工作流程：
        1. 理解用户需求
        2. 选择合适的工具
        3. 执行操作
        4. 分析结果
        5. 提供洞察和建议
        
        注意事项：
        - 始终确保SQL语句的安全性
        - 对于复杂查询，先检查数据库结构
        - 提供清晰、有用的分析结果
        - 如果不确定，先询问用户确认
        """
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            ("human", "{input}"),
            ("placeholder", "{agent_scratchpad}")
        ])
        
        # 创建Agent
        agent = create_openai_tools_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )
        
        # 创建Agent执行器
        self.agent_executor = AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=5
        )
    
    def chat(self, user_input: str) -> str:
        """
        与Agent对话
        """
        try:
            response = self.agent_executor.invoke({"input": user_input})
            return response["output"]
        except Exception as e:
            return f"处理请求时出错: {str(e)}"
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [tool.name for tool in self.tools]

# 使用示例
if __name__ == "__main__":
    print("=== SQL Agent 系统演示 ===\n")
    
    # 初始化Agent
    agent = SQLAgent(
        examples_path="sql_examples.txt",
        db_schema_path="db_schema.json",
        ddl_path="ddl_statements.sql",
        db_docs_path="database_documentation.md"
    )
    
    print("可用工具:", agent.get_available_tools())
    
    # 示例对话
    test_queries = [
        "请帮我查看数据库中有哪些表",
        "我想查询所有年龄大于25岁的用户信息",
        "帮我分析一下用户的年龄分布情况",
        "查询最近一个月的订单统计"
    ]
    
    for query in test_queries:
        print(f"\n用户: {query}")
        print(f"Agent: {agent.chat(query)}")
        print("-" * 80)
