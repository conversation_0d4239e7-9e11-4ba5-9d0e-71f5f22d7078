#!/usr/bin/env python3
"""
测试增强的NL2SQL RAG系统
验证DDL语句、SQL问答对和数据库文档的向量化功能
"""

import os
import sys
from nl2sql_rag_system import NL2SQLSystem

def test_document_loading():
    """测试文档加载功能"""
    print("=== 测试文档加载功能 ===")
    
    # 检查所需文件是否存在
    required_files = {
        "sql_examples.txt": "SQL问答对示例",
        "db_schema.json": "数据库结构文件", 
        "ddl_statements.sql": "DDL语句文件",
        "database_documentation.md": "数据库文档"
    }
    
    missing_files = []
    for file_path, description in required_files.items():
        if os.path.exists(file_path):
            print(f"✓ {description}: {file_path}")
        else:
            print(f"✗ {description}: {file_path} (文件不存在)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n警告: 缺少 {len(missing_files)} 个文件，系统将跳过这些数据源")
        return False
    
    print("\n所有训练数据文件都存在!")
    return True

def test_system_initialization():
    """测试系统初始化"""
    print("\n=== 测试系统初始化 ===")
    
    try:
        # 初始化系统
        system = NL2SQLSystem(
            examples_path="sql_examples.txt",
            db_schema_path="db_schema.json", 
            ddl_path="ddl_statements.sql",
            db_docs_path="database_documentation.md",
            model_name="gpt-3.5-turbo",
            vector_db_path="./test_chroma_db"
        )
        
        print("✓ 系统初始化成功")
        
        # 检查向量存储
        if system.vectorstore:
            print("✓ 向量存储创建成功")
            
            # 获取向量存储中的文档数量
            try:
                # 尝试进行一个简单的相似性搜索来验证向量存储
                test_results = system.vectorstore.similarity_search("用户表", k=3)
                print(f"✓ 向量存储包含 {len(test_results)} 个相关文档片段")
                
                # 显示文档类型分布
                doc_types = {}
                for doc in test_results:
                    source_type = doc.metadata.get("source_type", "unknown")
                    doc_types[source_type] = doc_types.get(source_type, 0) + 1
                
                print("文档类型分布:")
                for doc_type, count in doc_types.items():
                    print(f"  - {doc_type}: {count} 个文档")
                    
            except Exception as e:
                print(f"✗ 向量存储验证失败: {e}")
        else:
            print("✗ 向量存储创建失败")
            
        return system
        
    except Exception as e:
        print(f"✗ 系统初始化失败: {e}")
        return None

def test_query_generation(system):
    """测试查询生成功能"""
    print("\n=== 测试查询生成功能 ===")
    
    if not system:
        print("系统未初始化，跳过查询测试")
        return
    
    # 测试查询列表
    test_queries = [
        {
            "query": "查找所有用户的基本信息",
            "expected_keywords": ["SELECT", "users", "name", "email"]
        },
        {
            "query": "统计每个分类的商品数量",
            "expected_keywords": ["SELECT", "COUNT", "GROUP BY", "category"]
        },
        {
            "query": "查找最近的订单",
            "expected_keywords": ["SELECT", "orders", "ORDER BY", "order_date"]
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_queries, 1):
        query = test_case["query"]
        expected_keywords = test_case["expected_keywords"]
        
        print(f"\n{i}. 测试查询: {query}")
        
        try:
            # 生成SQL
            sql_result = system.natural_language_to_sql(query)
            print(f"   生成的SQL: {sql_result}")
            
            # 检查关键词
            sql_upper = sql_result.upper()
            found_keywords = [kw for kw in expected_keywords if kw.upper() in sql_upper]
            
            if len(found_keywords) >= len(expected_keywords) * 0.5:  # 至少包含50%的期望关键词
                print(f"   ✓ 包含期望关键词: {found_keywords}")
                success_count += 1
            else:
                print(f"   ⚠ 缺少关键词，期望: {expected_keywords}, 找到: {found_keywords}")
                
        except Exception as e:
            print(f"   ✗ 查询生成失败: {e}")
    
    print(f"\n查询测试结果: {success_count}/{len(test_queries)} 成功")

def test_retrieval_quality(system):
    """测试检索质量"""
    print("\n=== 测试检索质量 ===")
    
    if not system:
        print("系统未初始化，跳过检索测试")
        return
    
    # 测试不同类型的检索
    retrieval_tests = [
        {
            "query": "用户表结构",
            "expected_types": ["ddl_statements", "database_schema"]
        },
        {
            "query": "订单查询示例",
            "expected_types": ["sql_examples"]
        },
        {
            "query": "数据库业务规则",
            "expected_types": ["database_documentation"]
        }
    ]
    
    for test in retrieval_tests:
        query = test["query"]
        expected_types = test["expected_types"]
        
        print(f"\n检索测试: {query}")
        
        try:
            # 执行相似性搜索
            results = system.vectorstore.similarity_search(query, k=5)
            
            # 分析结果类型
            found_types = set()
            for doc in results:
                source_type = doc.metadata.get("source_type", "unknown")
                found_types.add(source_type)
            
            print(f"   检索到的文档类型: {list(found_types)}")
            
            # 检查是否包含期望的类型
            overlap = found_types.intersection(set(expected_types))
            if overlap:
                print(f"   ✓ 包含期望类型: {list(overlap)}")
            else:
                print(f"   ⚠ 未找到期望类型: {expected_types}")
                
        except Exception as e:
            print(f"   ✗ 检索失败: {e}")

def cleanup_test_files():
    """清理测试文件"""
    print("\n=== 清理测试文件 ===")
    
    import shutil
    
    # 删除测试向量数据库
    test_db_path = "./test_chroma_db"
    if os.path.exists(test_db_path):
        try:
            shutil.rmtree(test_db_path)
            print(f"✓ 已删除测试向量数据库: {test_db_path}")
        except Exception as e:
            print(f"✗ 删除测试向量数据库失败: {e}")

def main():
    """主测试函数"""
    print("开始测试增强的NL2SQL RAG系统\n")
    
    # 1. 测试文档加载
    files_ok = test_document_loading()
    
    # 2. 测试系统初始化
    system = test_system_initialization()
    
    # 3. 测试查询生成
    test_query_generation(system)
    
    # 4. 测试检索质量
    test_retrieval_quality(system)
    
    # 5. 清理测试文件
    cleanup_test_files()
    
    print("\n=== 测试完成 ===")
    
    if system:
        print("✓ 增强的NL2SQL系统测试通过")
        print("\n系统特性:")
        print("- ✓ 支持SQL问答对向量化")
        print("- ✓ 支持DDL语句向量化") 
        print("- ✓ 支持数据库文档向量化")
        print("- ✓ 智能文档分割策略")
        print("- ✓ 元数据标记和分类")
    else:
        print("✗ 系统测试失败，请检查配置和依赖")

if __name__ == "__main__":
    main()
